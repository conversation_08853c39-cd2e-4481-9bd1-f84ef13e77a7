#!/usr/bin/env python3
"""
Analyze current training data quality for 28 outpoints/pluspoints
Based on <PERSON><PERSON>'s Investigations methodology
"""
import pandas as pd
import json
from collections import Counter, defaultdict

def analyze_current_data():
    """Analyze the current training data quality"""
    print("📊 ANALYZING CURRENT TRAINING DATA QUALITY")
    print("="*60)
    
    # Load current data
    df = pd.read_csv("data/complete_28_category_dataset.csv")
    labeled_df = df[df['label'].notna() & (df['label'] != '')]
    
    print(f"📋 Dataset Overview:")
    print(f"   Total statements: {len(df)}")
    print(f"   Labeled statements: {len(labeled_df)}")
    print(f"   Unlabeled statements: {len(df) - len(labeled_df)}")
    
    # Load official mapping
    with open('official_28_category_mapping.json', 'r') as f:
        mapping = json.load(f)
    
    outpoints = mapping['outpoints']
    pluspoints = mapping['pluspoints']
    all_categories = outpoints + pluspoints + ['NEUTRAL']
    
    print(f"\n🎯 Category Coverage Analysis:")
    print(f"   Expected categories: {len(all_categories)} (14 outpoints + 14 pluspoints + NEUTRAL)")
    
    # Count examples per category
    category_counts = Counter()
    multi_label_examples = []
    
    for idx, row in labeled_df.iterrows():
        labels = [label.strip() for label in row['label'].split(',') if label.strip()]
        
        if len(labels) > 1:
            multi_label_examples.append({
                'text': row['text'][:100] + '...',
                'labels': labels,
                'count': len(labels)
            })
        
        for label in labels:
            if label in all_categories:
                category_counts[label] += 1
            else:
                print(f"   ⚠️ Unknown category found: {label}")
    
    print(f"\n📈 Category Distribution:")
    
    # Analyze outpoints
    print(f"\n🔍 OUTPOINTS (14 categories):")
    outpoint_total = 0
    missing_outpoints = []
    weak_outpoints = []
    
    for outpoint in outpoints:
        count = category_counts.get(outpoint, 0)
        outpoint_total += count
        
        if count == 0:
            missing_outpoints.append(outpoint)
            print(f"   ❌ {outpoint}: {count} examples")
        elif count < 3:
            weak_outpoints.append(outpoint)
            print(f"   ⚠️ {outpoint}: {count} examples (WEAK)")
        else:
            print(f"   ✅ {outpoint}: {count} examples")
    
    # Analyze pluspoints
    print(f"\n✨ PLUSPOINTS (14 categories):")
    pluspoint_total = 0
    missing_pluspoints = []
    weak_pluspoints = []
    
    for pluspoint in pluspoints:
        count = category_counts.get(pluspoint, 0)
        pluspoint_total += count
        
        if count == 0:
            missing_pluspoints.append(pluspoint)
            print(f"   ❌ {pluspoint}: {count} examples")
        elif count < 3:
            weak_pluspoints.append(pluspoint)
            print(f"   ⚠️ {pluspoint}: {count} examples (WEAK)")
        else:
            print(f"   ✅ {pluspoint}: {count} examples")
    
    # NEUTRAL category
    neutral_count = category_counts.get('NEUTRAL', 0)
    print(f"\n⚪ NEUTRAL: {neutral_count} examples")
    
    # Balance analysis
    print(f"\n⚖️ BALANCE ANALYSIS:")
    print(f"   Outpoints total: {outpoint_total}")
    print(f"   Pluspoints total: {pluspoint_total}")
    print(f"   Ratio (out:plus): {outpoint_total/max(pluspoint_total,1):.2f}:1")
    print(f"   NEUTRAL: {neutral_count}")
    
    # Quality issues
    print(f"\n🚨 QUALITY ISSUES:")
    print(f"   Missing outpoints: {len(missing_outpoints)}/14")
    print(f"   Missing pluspoints: {len(missing_pluspoints)}/14")
    print(f"   Weak outpoints (<3 examples): {len(weak_outpoints)}")
    print(f"   Weak pluspoints (<3 examples): {len(weak_pluspoints)}")
    print(f"   Multi-label examples: {len(multi_label_examples)}")
    
    if missing_outpoints:
        print(f"\n❌ MISSING OUTPOINTS:")
        for outpoint in missing_outpoints:
            print(f"   - {outpoint}")
    
    if missing_pluspoints:
        print(f"\n❌ MISSING PLUSPOINTS:")
        for pluspoint in missing_pluspoints:
            print(f"   - {pluspoint}")
    
    if weak_outpoints:
        print(f"\n⚠️ WEAK OUTPOINTS:")
        for outpoint in weak_outpoints:
            print(f"   - {outpoint}: {category_counts[outpoint]} examples")
    
    if weak_pluspoints:
        print(f"\n⚠️ WEAK PLUSPOINTS:")
        for pluspoint in weak_pluspoints:
            print(f"   - {pluspoint}: {category_counts[pluspoint]} examples")
    
    # Multi-label analysis
    if multi_label_examples:
        print(f"\n🏷️ MULTI-LABEL EXAMPLES (top 10):")
        for i, example in enumerate(multi_label_examples[:10]):
            print(f"   {i+1}. {example['text']}")
            print(f"      Labels: {', '.join(example['labels'])}")
    
    return {
        'total_statements': len(df),
        'labeled_statements': len(labeled_df),
        'category_counts': dict(category_counts),
        'missing_outpoints': missing_outpoints,
        'missing_pluspoints': missing_pluspoints,
        'weak_outpoints': weak_outpoints,
        'weak_pluspoints': weak_pluspoints,
        'multi_label_count': len(multi_label_examples),
        'outpoint_total': outpoint_total,
        'pluspoint_total': pluspoint_total,
        'neutral_count': neutral_count
    }

def create_improvement_plan(analysis):
    """Create a data improvement plan based on analysis"""
    print(f"\n🎯 DATA IMPROVEMENT PLAN")
    print("="*50)
    
    priority_categories = []
    
    # High priority: missing categories
    missing_total = len(analysis['missing_outpoints']) + len(analysis['missing_pluspoints'])
    if missing_total > 0:
        print(f"🔥 HIGH PRIORITY: {missing_total} missing categories")
        priority_categories.extend(analysis['missing_outpoints'])
        priority_categories.extend(analysis['missing_pluspoints'])
    
    # Medium priority: weak categories
    weak_total = len(analysis['weak_outpoints']) + len(analysis['weak_pluspoints'])
    if weak_total > 0:
        print(f"⚠️ MEDIUM PRIORITY: {weak_total} weak categories (<3 examples)")
        priority_categories.extend(analysis['weak_outpoints'])
        priority_categories.extend(analysis['weak_pluspoints'])
    
    print(f"\n📋 RECOMMENDED ACTIONS:")
    
    print(f"\n1. 🎯 TARGET DATA COLLECTION:")
    print(f"   Focus on these {len(priority_categories)} categories:")
    for i, category in enumerate(priority_categories[:10]):  # Show top 10
        print(f"   {i+1}. {category}")
    
    print(f"\n2. 📚 DATA SOURCES TO EXPLORE:")
    print(f"   - News articles (BBC, Reuters, AP)")
    print(f"   - Government reports and statements")
    print(f"   - Corporate communications")
    print(f"   - Academic papers and studies")
    print(f"   - Legal documents and court cases")
    print(f"   - Historical records and archives")
    
    print(f"\n3. 🔍 SPECIFIC SEARCH STRATEGIES:")
    
    # Outpoint-specific strategies
    if analysis['missing_outpoints'] or analysis['weak_outpoints']:
        print(f"   OUTPOINTS:")
        outpoint_strategies = {
            'OMITTED_DATA_OUT': 'Look for incomplete reports, missing context',
            'ALTERED_SEQUENCE_OUT': 'Find timeline errors, out-of-order events',
            'DROPPED_TIME_OUT': 'Search for undated statements, missing timestamps',
            'FALSEHOOD_OUT': 'Identify contradicted claims, proven false statements',
            'ALTERED_IMPORTANCE_OUT': 'Find exaggerated or minimized claims',
            'WRONG_TARGET_OUT': 'Look for misdirected blame or focus',
            'WRONG_SOURCE_OUT': 'Find misattributed quotes or information',
            'CONTRARY_FACTS_OUT': 'Search for contradictory statements',
            'ADDED_TIME_OUT': 'Find unnecessarily specific time claims',
            'ADDED_INAPPLICABLE_DATA_OUT': 'Look for irrelevant information',
            'INCORRECTLY_INCLUDED_DATUM_OUT': 'Find misplaced or wrong data',
            'ASSUMED_IDENTITIES_NOT_IDENTICAL_OUT': 'Look for false equivalences',
            'ASSUMED_SIMILARITIES_NOT_SIMILAR_OUT': 'Find false comparisons',
            'ASSUMED_DIFFERENCES_NOT_DIFFERENT_OUT': 'Look for false distinctions'
        }
        
        for outpoint in (analysis['missing_outpoints'] + analysis['weak_outpoints'])[:5]:
            if outpoint in outpoint_strategies:
                print(f"   - {outpoint}: {outpoint_strategies[outpoint]}")
    
    # Pluspoint-specific strategies
    if analysis['missing_pluspoints'] or analysis['weak_pluspoints']:
        print(f"   PLUSPOINTS:")
        pluspoint_strategies = {
            'RELATED_FACTS_KNOWN_PLUS': 'Find well-supported statements with evidence',
            'EVENTS_IN_CORRECT_SEQUENCE_PLUS': 'Look for proper chronological order',
            'TIME_NOTED_PLUS': 'Find statements with clear timestamps',
            'DATA_PROVEN_FACTUAL_PLUS': 'Search for verified, factual statements',
            'CORRECT_RELATIVE_IMPORTANCE_PLUS': 'Find appropriately weighted claims',
            'EXPECTED_TIME_PERIOD_PLUS': 'Look for realistic timeframes',
            'ADEQUATE_DATA_PLUS': 'Find complete, sufficient information',
            'APPLICABLE_DATA_PLUS': 'Look for relevant, on-topic information',
            'CORRECT_SOURCE_PLUS': 'Find properly attributed information',
            'CORRECT_TARGET_PLUS': 'Look for appropriately directed focus',
            'DATA_IN_SAME_CLASSIFICATION_PLUS': 'Find consistent categorization',
            'IDENTITIES_ARE_IDENTICAL_PLUS': 'Look for proper equivalences',
            'SIMILARITIES_ARE_SIMILAR_PLUS': 'Find valid comparisons',
            'DIFFERENCES_ARE_DIFFERENT_PLUS': 'Look for valid distinctions'
        }
        
        for pluspoint in (analysis['missing_pluspoints'] + analysis['weak_pluspoints'])[:5]:
            if pluspoint in pluspoint_strategies:
                print(f"   - {pluspoint}: {pluspoint_strategies[pluspoint]}")
    
    print(f"\n4. 🎯 TARGET METRICS:")
    current_total = analysis['labeled_statements']
    target_total = max(500, current_total * 2)
    target_per_category = max(10, target_total // 29)
    
    print(f"   - Current labeled examples: {current_total}")
    print(f"   - Target total examples: {target_total}")
    print(f"   - Target per category: {target_per_category}")
    print(f"   - Additional examples needed: {target_total - current_total}")
    
    return {
        'priority_categories': priority_categories,
        'target_total': target_total,
        'target_per_category': target_per_category,
        'additional_needed': target_total - current_total
    }

def main():
    print("🔍 TRUTH ALGORITHM DATA QUALITY ANALYSIS")
    print("="*60)
    print("Based on L. Ron Hubbard's Investigations methodology")
    print("Analyzing 14 outpoints + 14 pluspoints + NEUTRAL")
    print()
    
    # Analyze current data
    analysis = analyze_current_data()
    
    # Create improvement plan
    plan = create_improvement_plan(analysis)
    
    # Save results
    results = {
        'analysis': analysis,
        'improvement_plan': plan,
        'timestamp': pd.Timestamp.now().isoformat()
    }
    
    with open('data_quality_analysis.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Analysis saved to: data_quality_analysis.json")
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Review priority categories list")
    print(f"2. Begin targeted data collection")
    print(f"3. Use AI-assisted labeling for efficiency")
    print(f"4. Validate against L. Ron Hubbard definitions")
    print(f"5. Retrain model with improved dataset")

if __name__ == "__main__":
    main()
