#!/usr/bin/env python3
"""
Analyze training data distribution to understand model bias
"""
import pandas as pd
import numpy as np
from collections import Counter
import json

def analyze_label_distribution():
    """Analyze the distribution of labels in our training data"""
    print("📊 TRAINING DATA ANALYSIS")
    print("="*50)
    
    # Load data
    df = pd.read_csv("data/statements_to_label.csv")
    print(f"📂 Loaded {len(df)} total statements")
    
    # Filter labeled data
    labeled_df = df[df['label'].notna() & (df['label'] != '')]
    print(f"📂 Found {len(labeled_df)} labeled statements")
    
    # Count label distribution
    label_counts = Counter()
    all_labels = []
    
    for _, row in labeled_df.iterrows():
        labels = [label.strip() for label in row['label'].split(',')]
        all_labels.extend(labels)
        for label in labels:
            label_counts[label] += 1
    
    print(f"\n📈 LABEL DISTRIBUTION:")
    print("-" * 60)
    print(f"{'Label':<25} {'Count':<8} {'Percentage':<12} {'Category'}")
    print("-" * 60)
    
    total_labels = sum(label_counts.values())
    
    # Sort by count (descending)
    sorted_labels = sorted(label_counts.items(), key=lambda x: x[1], reverse=True)
    
    outpoints = []
    pluspoints = []
    neutral = []
    
    for label, count in sorted_labels:
        percentage = (count / total_labels) * 100
        
        if "_OUT" in label:
            category = "OUTPOINT"
            outpoints.append((label, count, percentage))
        elif "_PLUS" in label:
            category = "PLUSPOINT"
            pluspoints.append((label, count, percentage))
        else:
            category = "NEUTRAL"
            neutral.append((label, count, percentage))
        
        print(f"{label:<25} {count:<8} {percentage:<12.1f}% {category}")
    
    # Summary statistics
    print("-" * 60)
    print(f"\n📊 SUMMARY STATISTICS:")
    print(f"Total Labels: {total_labels}")
    print(f"Unique Labels: {len(label_counts)}")
    print(f"Outpoints: {len(outpoints)} categories, {sum(count for _, count, _ in outpoints)} instances")
    print(f"Pluspoints: {len(pluspoints)} categories, {sum(count for _, count, _ in pluspoints)} instances")
    print(f"Neutral: {len(neutral)} categories, {sum(count for _, count, _ in neutral)} instances")
    
    return label_counts, outpoints, pluspoints, neutral

def identify_data_imbalance(label_counts, outpoints, pluspoints, neutral):
    """Identify specific data imbalance issues"""
    print(f"\n⚠️ DATA IMBALANCE ANALYSIS:")
    print("="*50)
    
    total_labels = sum(label_counts.values())
    issues = []
    
    # Check for dominant labels
    dominant_threshold = 0.3  # 30% or more
    for label, count in label_counts.items():
        percentage = (count / total_labels) * 100
        if percentage >= dominant_threshold * 100:
            issues.append(f"🚨 DOMINANT: {label} ({percentage:.1f}% of all labels)")
    
    # Check for rare labels
    rare_threshold = 3  # Less than 3 instances
    rare_labels = []
    for label, count in label_counts.items():
        if count < rare_threshold:
            rare_labels.append((label, count))
    
    if rare_labels:
        issues.append(f"📉 RARE LABELS: {len(rare_labels)} categories with <{rare_threshold} examples")
        for label, count in rare_labels[:5]:  # Show first 5
            issues.append(f"   • {label}: {count} examples")
    
    # Check outpoint vs pluspoint balance
    outpoint_total = sum(count for _, count, _ in outpoints)
    pluspoint_total = sum(count for _, count, _ in pluspoints)
    
    if outpoint_total > 0 and pluspoint_total > 0:
        ratio = max(outpoint_total, pluspoint_total) / min(outpoint_total, pluspoint_total)
        if ratio > 2:
            issues.append(f"⚖️ IMBALANCED: Outpoints vs Pluspoints ratio {ratio:.1f}:1")
    
    # Print issues
    for issue in issues:
        print(issue)
    
    return issues, rare_labels

def generate_data_enhancement_plan(label_counts, rare_labels, issues):
    """Generate specific plan for data enhancement"""
    print(f"\n💡 DATA ENHANCEMENT PLAN:")
    print("="*50)
    
    plan = {
        'immediate_actions': [],
        'data_collection_targets': [],
        'augmentation_strategies': [],
        'balancing_actions': []
    }
    
    # Address rare labels
    if rare_labels:
        plan['immediate_actions'].append("🎯 Priority: Add examples for rare categories")
        for label, count in rare_labels:
            target = max(5, count * 2)  # At least 5 examples, or double current
            plan['data_collection_targets'].append({
                'label': label,
                'current': count,
                'target': target,
                'needed': target - count
            })
    
    # Address dominant labels
    dominant_labels = [(label, count) for label, count in label_counts.items() 
                      if (count / sum(label_counts.values())) > 0.3]
    
    if dominant_labels:
        for label, count in dominant_labels:
            plan['balancing_actions'].append(f"Reduce reliance on {label} (currently {count} examples)")
    
    # Augmentation strategies
    plan['augmentation_strategies'].extend([
        "Use AI-assisted labeling for additional BBC statements",
        "Paraphrase existing examples to increase diversity",
        "Focus on underrepresented outpoint categories",
        "Create synthetic examples for rare categories"
    ])
    
    # Print plan
    print("🚀 Immediate Actions:")
    for action in plan['immediate_actions']:
        print(f"  • {action}")
    
    print(f"\n📈 Data Collection Targets:")
    for target in plan['data_collection_targets'][:5]:  # Show top 5
        print(f"  • {target['label']}: {target['current']} → {target['target']} (+{target['needed']} needed)")
    
    print(f"\n🔧 Augmentation Strategies:")
    for strategy in plan['augmentation_strategies']:
        print(f"  • {strategy}")
    
    return plan

def save_analysis_results(label_counts, issues, plan):
    """Save analysis results for reference"""
    results = {
        'timestamp': pd.Timestamp.now().isoformat(),
        'label_distribution': dict(label_counts),
        'total_labels': sum(label_counts.values()),
        'unique_labels': len(label_counts),
        'identified_issues': issues,
        'enhancement_plan': plan,
        'recommendations': {
            'priority_1': 'Add examples for rare categories (<3 instances)',
            'priority_2': 'Balance outpoint vs pluspoint representation',
            'priority_3': 'Reduce model bias toward dominant categories',
            'priority_4': 'Implement data augmentation strategies'
        }
    }
    
    with open('training_data_analysis.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Analysis saved to: training_data_analysis.json")
    return results

def main():
    try:
        # Analyze label distribution
        label_counts, outpoints, pluspoints, neutral = analyze_label_distribution()
        
        # Identify imbalance issues
        issues, rare_labels = identify_data_imbalance(label_counts, outpoints, pluspoints, neutral)
        
        # Generate enhancement plan
        plan = generate_data_enhancement_plan(label_counts, rare_labels, issues)
        
        # Save results
        results = save_analysis_results(label_counts, issues, plan)
        
        print(f"\n✅ Training data analysis complete!")
        print(f"📊 Found {len(issues)} data quality issues")
        print(f"🎯 Generated enhancement plan with {len(plan['data_collection_targets'])} targets")
        print(f"🚀 Ready to implement data improvements!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
