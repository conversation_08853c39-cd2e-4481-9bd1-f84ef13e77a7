[{"statement_id": 9, "statement": "<PERSON> repeatedly says joining the US would be \"a massive tax cut\" for Canadian citizens.", "timestamp": "2025-07-08T23:24:53.349411", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87E4A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.084027528762817}, "processed_order": 1}, {"statement_id": 10, "statement": "Russia is gearing up to celebrate the 80th anniversary of the end of World War Two in Europe.", "timestamp": "2025-07-08T23:24:57.458779", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87F2B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.091300010681152}, "processed_order": 2}, {"statement_id": 11, "statement": "\"I'll let history judge that,\" he said.", "timestamp": "2025-07-08T23:25:01.507463", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F8040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.048684358596802}, "processed_order": 3}, {"statement_id": 12, "statement": "<PERSON><PERSON>, an Eritrean migrant who did not want to give his real name for security reasons, made the crossing at the same time as <PERSON><PERSON><PERSON> and her sister.", "timestamp": "2025-07-08T23:25:05.648554", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87FB50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.057242155075073}, "processed_order": 4}, {"statement_id": 13, "statement": "A spokesperson for Pakistan's military says several locations have been hit by missiles.", "timestamp": "2025-07-08T23:25:09.732010", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87EDA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.083455562591553}, "processed_order": 5}, {"statement_id": 14, "statement": "Like <PERSON>'s family, many flee to avoid military conscription in a country that has been embroiled in numerous wars in the region, and where free political and religious activity is not tolerated as the government tries to keep a tight grip on power.", "timestamp": "2025-07-08T23:25:13.828859", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87E500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.09684944152832}, "processed_order": 6}, {"statement_id": 15, "statement": "\"Thank God nothing happened, but we tempt fate if no changes are made.\"", "timestamp": "2025-07-08T23:25:17.918347", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F8F70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.08948826789856}, "processed_order": 7}, {"statement_id": 16, "statement": "Ambitious economies like Vietnam and Indonesia are stuck between China and the US as the trade war escalates.", "timestamp": "2025-07-08T23:25:21.985911", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87DC90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.067564249038696}, "processed_order": 8}, {"statement_id": 17, "statement": "From rare earths to billions in US debt, here's what China has in its arsenal - and how strong they are.", "timestamp": "2025-07-08T23:25:26.067051", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87F310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.08113956451416}, "processed_order": 9}, {"statement_id": 18, "statement": "Thousands of fans cheer their team's celebrations after the Bantams win promotion.", "timestamp": "2025-07-08T23:25:30.148699", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F9630>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.081647872924805}, "processed_order": 10}, {"statement_id": 19, "statement": "It also turns the page on decades of missed economic opportunities, given the strong historic connections between the two nations.", "timestamp": "2025-07-08T23:25:34.199237", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F8160>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.050538063049316}, "processed_order": 11}, {"statement_id": 20, "statement": "The baba is often served at Easter in Poland, with the most extraordinary version – the muslin baba – made from a rich dough of flour, yeast, butter and quite a lot of egg yolks.", "timestamp": "2025-07-08T23:25:38.282696", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87EBF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.082615852355957}, "processed_order": 12}, {"statement_id": 21, "statement": "A female smuggler in Kenya confirmed to the BBC that Lake Turkana was increasingly being used as an illegal crossing for the migrants.", "timestamp": "2025-07-08T23:25:42.359891", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87D930>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.076591968536377}, "processed_order": 13}, {"statement_id": 22, "statement": "The smuggling network operates in all these countries, handing over migrants to different \"agents\" until they reach their final destination, which - in some cases - can also be Europe or North America.", "timestamp": "2025-07-08T23:25:46.447075", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8FBA00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.087183713912964}, "processed_order": 14}, {"statement_id": 23, "statement": "Follow the twists and turns of <PERSON>'s second term with North America correspondent <PERSON>'s weekly US Politics Unspun newsletter.", "timestamp": "2025-07-08T23:25:50.544689", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F9300>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.097614288330078}, "processed_order": 15}, {"statement_id": 24, "statement": "<PERSON><PERSON><PERSON>'s sister survived by clinging to the sinking boat until another vessel - also operated by the smugglers - came to the rescue.", "timestamp": "2025-07-08T23:25:54.633445", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87E770>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.088755369186401}, "processed_order": 16}, {"statement_id": 25, "statement": "The UK government sees this as a win-win which helps exporters, creates jobs, and means lower prices for consumers.", "timestamp": "2025-07-08T23:25:58.663861", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87F520>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.03041672706604}, "processed_order": 17}, {"statement_id": 26, "statement": "As Western alliances unravel, Europe is entering an era that seems distant from what VE Day represented, the BBC's <PERSON><PERSON> writes.", "timestamp": "2025-07-08T23:26:02.749358", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F86A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.082815408706665}, "processed_order": 18}, {"statement_id": 27, "statement": "\"May God heal our land and deliver us from all this.\"", "timestamp": "2025-07-08T23:26:06.809197", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8FBA00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.059838771820068}, "processed_order": 19}, {"statement_id": 28, "statement": "Inter Milan's stunning 7-6 aggregate win against Barcelona was one of the all-time great Champions League semi-finals, but where does it rank?", "timestamp": "2025-07-08T23:26:10.866970", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87F670>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.0577733516693115}, "processed_order": 20}, {"statement_id": 29, "statement": "\"I found it sort of beneath America in the way that took place,\" <PERSON><PERSON> said of the meeting.", "timestamp": "2025-07-08T23:26:14.946647", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87D780>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.0778868198394775}, "processed_order": 21}, {"statement_id": 30, "statement": "\"That's 90 seconds of a wholly filled-up sky of planes literally flying blind over one of America's busiest airports,\" said New York Senator <PERSON> on Tuesday.", "timestamp": "2025-07-08T23:26:19.054332", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8FB7C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.10768461227417}, "processed_order": 22}, {"statement_id": 31, "statement": "For just two weeks each spring, a rare violet artichoke is harvested by hand on Venice's northern islands – and locals go to great lengths to keep it authentic.", "timestamp": "2025-07-08T23:26:23.130777", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F87F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.076444864273071}, "processed_order": 23}, {"statement_id": 32, "statement": "The pilot is heard calmly responding: \"Alright, we're ready to move.\"", "timestamp": "2025-07-08T23:26:27.246281", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87E9B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.115504503250122}, "processed_order": 24}, {"statement_id": 33, "statement": "In June 2024, the UN's refugee agency, UNHCR, recorded 345,000 Eritrean refugees and asylum seekers in East Africa, out of 580,000 globally.", "timestamp": "2025-07-08T23:26:31.329956", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87E4D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.083674669265747}, "processed_order": 25}, {"statement_id": 34, "statement": "The menu at the Icehotel, the world's first hotel made out of ice and snow, is served on plates of ice.", "timestamp": "2025-07-08T23:26:35.746124", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8FA020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.054993391036987}, "processed_order": 26}, {"statement_id": 35, "statement": "Chinese leader <PERSON> urged \"all-out efforts\" in the search and rescue mission.", "timestamp": "2025-07-08T23:26:39.829091", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F8220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.082967519760132}, "processed_order": 27}, {"statement_id": 36, "statement": "\"Things moved so quickly that it made it difficult to walk away.", "timestamp": "2025-07-08T23:26:43.900932", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87FD60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.07184100151062}, "processed_order": 28}, {"statement_id": 37, "statement": "The 1999 Kargil conflict saw Pakistani troops infiltrate Indian-administered Kashmir; it was the first clash between the nuclear-armed rivals, sparking global alarm.", "timestamp": "2025-07-08T23:26:47.947962", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB87E6E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.047029972076416}, "processed_order": 29}, {"statement_id": 38, "statement": "<PERSON> told his colleagues their government had no credibility after presidential elections.", "timestamp": "2025-07-08T23:26:52.016756", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8FA500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.0649144649505615}, "processed_order": 30}, {"statement_id": 39, "statement": "<PERSON> has long resisted continuing the level of US military support that <PERSON><PERSON> gave to Ukraine, arguing that his ultimate aim is to end the bloodshed.", "timestamp": "2025-07-08T23:26:56.084984", "ai_result": {"success": false, "error": "HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFAB8F8130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "response_time": 4.068228006362915}, "processed_order": 31}]