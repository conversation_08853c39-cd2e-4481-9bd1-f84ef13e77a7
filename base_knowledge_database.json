{"established_facts": {"physical_world": {"sky_color": {"default": "blue during clear daytime", "exceptions": {"sunset": ["pink", "orange", "red", "purple"], "sunrise": ["pink", "orange", "red"], "storm": ["gray", "dark gray", "black"], "pollution": ["brown", "hazy", "smoggy"], "night": ["black", "dark"], "overcast": ["gray", "white"]}, "context_required": ["time_of_day", "weather_conditions", "location", "atmospheric_conditions"]}, "water_properties": {"default_state": "liquid at room temperature (20°C)", "freezing_point": "0°C at standard pressure", "boiling_point": "100°C at standard pressure", "exceptions": {"pressure_variations": "boiling/freezing points change with pressure", "dissolved_substances": "salt water freezes at lower temperature"}}, "gravity": {"default": "objects fall downward on Earth surface", "acceleration": "9.8 m/s² at sea level", "exceptions": {"space": "weightlessness in orbit", "underwater": "buoyancy can overcome gravity", "magnetic_objects": "magnetic forces can overcome gravity"}}}, "temporal_facts": {"causality": {"default": "cause precedes effect in normal circumstances", "exceptions": {"quantum_mechanics": "quantum entanglement effects", "relativity": "time dilation in extreme conditions"}}, "historical_events": {"world_war_ii_end": "1945", "moon_landing": "July 20, 1969", "berlin_wall_fall": "November 9, 1989", "internet_creation": "1960s-1990s (gradual development)", "covid_pandemic_start": "2020"}, "calendar_facts": {"days_in_year": 365, "days_in_leap_year": 366, "months_in_year": 12, "hours_in_day": 24}}, "mathematical_facts": {"basic_arithmetic": {"2+2": 4, "10-5": 5, "3*4": 12, "8/2": 4}, "constants": {"pi": 3.14159, "speed_of_light": "299,792,458 m/s", "absolute_zero": "-273.15°C"}, "logical_principles": {"contradiction": "A statement cannot be both true and false simultaneously", "identity": "A thing is identical to itself", "excluded_middle": "A statement is either true or false"}}, "business_logic": {"financial_principles": {"profit_formula": "revenue - expenses = profit", "cash_flow_requirement": "must have money available to pay bills", "employment_principle": "employees must be paid for work performed", "accounting_equation": "assets = liabilities + equity"}, "organizational_principles": {"hierarchy": "authority flows from top to bottom", "communication": "information must flow between relevant parties", "responsibility": "someone must be accountable for each function"}}, "legal_facts": {"universal_principles": {"presumption_of_innocence": "accused is innocent until proven guilty", "burden_of_proof": "accuser must provide evidence", "due_process": "fair procedures must be followed"}}, "scientific_facts": {"biology": {"human_lifespan": "typically 70-90 years in developed countries", "human_body_temperature": "37°C (98.6°F) normal", "breathing_requirement": "humans need oxygen to survive"}, "chemistry": {"periodic_table": "118 known elements", "water_formula": "H2O", "conservation_of_mass": "matter cannot be created or destroyed in chemical reactions"}}}, "working_assumptions": {"default_contexts": {"location": "Earth surface", "time_period": "current era (21st century)", "conditions": "standard atmospheric pressure and temperature", "legal_system": "modern democratic legal system", "economic_system": "market-based economy"}, "communication_assumptions": {"language": "statements are in understandable language", "intent": "speaker intends to communicate truthfully unless indicated otherwise", "context": "sufficient context is provided unless noted as missing"}}, "contradiction_patterns": {"direct_contradictions": [{"pattern": "profitable vs unable_to_pay", "terms_a": ["profitable", "record profits", "successful", "making money"], "terms_b": ["couldn't pay", "unable to pay", "bankrupt", "no money"], "context_required": ["timing", "cash_flow_vs_accounting", "different_entities"]}, {"pattern": "safe vs dangerous", "terms_a": ["safe", "secure", "protected", "no risk"], "terms_b": ["dangerous", "unsafe", "risky", "hazardous"], "context_required": ["different_aspects", "timing", "conditions"]}, {"pattern": "increasing vs decreasing", "terms_a": ["increased", "rose", "went up", "growing"], "terms_b": ["decreased", "fell", "went down", "shrinking"], "context_required": ["different_metrics", "different_time_periods", "different_entities"]}], "temporal_contradictions": [{"pattern": "effect_before_cause", "description": "Effect described as happening before its cause", "examples": ["solved before analyzing", "paid before earning", "graduated before studying"]}]}, "investigation_triggers": {"high_priority": ["direct_contradiction_with_established_fact", "impossible_timeline", "mathematical_impossibility", "logical_contradiction"], "medium_priority": ["vague_attribution", "missing_context", "unusual_claim_without_evidence", "conflicting_statements_same_source"], "low_priority": ["minor_inconsistency", "unclear_wording", "missing_minor_details"]}, "validation_rules": {"outpoint_validation": {"OMITTED_DATA_OUT": {"required_indicators": ["missing_information", "vague_attribution", "incomplete_details"], "false_positive_checks": ["sufficient_context_provided", "appropriate_level_of_detail"]}, "CONTRARY_FACTS_OUT": {"required_indicators": ["contradictory_statements", "conflicting_claims"], "false_positive_checks": ["different_contexts", "different_time_periods", "different_entities"]}, "FALSEHOOD_OUT": {"required_indicators": ["contradicts_established_fact", "proven_false"], "false_positive_checks": ["context_dependent_truth", "opinion_vs_fact"]}}, "pluspoint_validation": {"DATA_PROVEN_FACTUAL_PLUS": {"required_indicators": ["verifiable_source", "specific_data", "documented_evidence"], "false_positive_checks": ["unverified_claims", "opinions_presented_as_facts"]}, "ADEQUATE_DATA_PLUS": {"required_indicators": ["sufficient_detail", "complete_information", "proper_context"], "false_positive_checks": ["excessive_irrelevant_detail", "information_overload"]}}}, "metadata": {"version": "1.0", "created": "2025-08-03", "source": "<PERSON><PERSON> Investigations methodology", "purpose": "Base knowledge for TruthAlgorithm outpoint/pluspoint detection", "expandable": true, "validation_required": true}}