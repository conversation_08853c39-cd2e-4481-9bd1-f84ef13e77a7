#!/usr/bin/env python3
"""
Base Knowledge Database for Truth Algorithm
Contains well-established facts for contradiction detection
"""
import json

class BaseKnowledgeDB:
    """Simple base knowledge database for contradiction detection"""
    
    def __init__(self):
        self.facts = {
            # Physical facts
            "sky_color": "blue",
            "water_freezes": "0°C",
            "gravity_direction": "downward",
            
            # Mathematical facts
            "2+2": "4",
            "pi_approx": "3.14",
            
            # Business logic
            "profit_formula": "revenue - expenses",
            "payment_requirement": "need money to pay bills",
            
            # Temporal facts
            "cause_effect": "cause precedes effect",
            "wwii_end": "1945",
            "moon_landing": "1969"
        }
        
        self.contextual_exceptions = {
            "sky_color": {
                "sunset": ["pink", "orange", "red"],
                "storm": ["gray", "dark"],
                "night": ["black"]
            }
        }
    
    def check_contradiction(self, statement):
        """Check if statement contradicts base knowledge"""
        statement_lower = statement.lower()
        contradictions = []
        
        # Sky color check
        if "sky is pink" in statement_lower and "sunset" not in statement_lower:
            contradictions.append({
                "type": "CONTRARY_FACTS_OUT",
                "fact": "Sky is normally blue",
                "needs_context": ["time", "weather", "location"]
            })

        # Mathematical contradictions check
        if "2+2" in statement_lower.replace(" ", ""):
            if any(wrong in statement_lower for wrong in ["5", "3", "6", "equals 5", "= 5"]):
                contradictions.append({
                    "type": "FALSEHOOD_OUT",
                    "fact": "2+2 = 4 (mathematical fact)",
                    "needs_context": []
                })

        # Business logic check
        if ("record profit" in statement_lower and
            ("couldn't pay" in statement_lower or "unable to pay" in statement_lower)):
            contradictions.append({
                "type": "CONTRARY_FACTS_OUT",
                "fact": "Profitable companies should be able to pay expenses",
                "needs_context": ["timing", "cash_flow"]
            })

        return contradictions

def create_sample_corrected_data():
    """Create sample of manually corrected training data"""
    
    corrected_examples = [
        {
            "text": "The company reported record profits but couldn't pay suppliers.",
            "original_label": "CONTRARY_FACTS_OUT",
            "corrected_label": "CONTRARY_FACTS_OUT",
            "validation": "CORRECT - Clear contradiction between profits and inability to pay",
            "lrh_definition": "Two contradictory facts qualify as outpoint"
        },
        {
            "text": "Sources say there were problems with the project.",
            "original_label": "OMITTED_DATA_OUT",
            "corrected_label": "OMITTED_DATA_OUT, WRONG_SOURCE_OUT",
            "validation": "ENHANCED - Added WRONG_SOURCE_OUT for vague attribution",
            "lrh_definition": "Omitted data (what problems?) + Wrong source (vague 'sources')"
        },
        {
            "text": "According to NASA's official report, the mission was successful.",
            "original_label": "DATA_PROVEN_FACTUAL_PLUS",
            "corrected_label": "DATA_PROVEN_FACTUAL_PLUS, CORRECT_SOURCE_PLUS, ADEQUATE_DATA_PLUS",
            "validation": "ENHANCED - Added source and data adequacy pluspoints",
            "lrh_definition": "Factual data + Correct source + Adequate information"
        },
        {
            "text": "The sky is pink during sunset over the ocean.",
            "original_label": "CONTRARY_FACTS_OUT",
            "corrected_label": "DATA_PROVEN_FACTUAL_PLUS, TIME_NOTED_PLUS",
            "validation": "CORRECTED - Not contradiction with proper context",
            "lrh_definition": "Factual with context + Time reference provided"
        }
    ]
    
    return corrected_examples

def main():
    print("🗄️ BASE KNOWLEDGE DATABASE")
    print("="*40)
    
    # Initialize database
    db = BaseKnowledgeDB()
    
    print("📋 Base Facts Loaded:")
    for category, fact in db.facts.items():
        print(f"   {category}: {fact}")
    
    print(f"\n🧪 Testing Contradiction Detection:")
    
    test_statements = [
        "The sky is pink today",
        "The company had record profits but couldn't pay bills",
        "The sky is pink during sunset",
        "2+2 equals 5"
    ]
    
    for stmt in test_statements:
        contradictions = db.check_contradiction(stmt)
        print(f"\nStatement: {stmt}")
        if contradictions:
            for c in contradictions:
                print(f"   ⚠️ {c['type']}: {c['fact']}")
                if c.get('needs_context'):
                    print(f"   🔍 Context needed: {c['needs_context']}")
        else:
            print(f"   ✅ No contradictions found")
    
    # Show sample corrections
    print(f"\n📝 SAMPLE CORRECTED DATA:")
    corrected = create_sample_corrected_data()
    
    for example in corrected[:2]:  # Show first 2
        print(f"\nText: {example['text']}")
        print(f"Original: {example['original_label']}")
        print(f"Corrected: {example['corrected_label']}")
        print(f"Validation: {example['validation']}")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Manually review training data using this framework")
    print(f"2. Apply L. Ron Hubbard definitions strictly")
    print(f"3. Check for contradictions with base knowledge")
    print(f"4. Consider context and exceptions")
    print(f"5. Create corrected dataset for Round 3")

if __name__ == "__main__":
    main()
