#!/usr/bin/env python3
"""
Check current model status and availability
"""
import json
from pathlib import Path

def check_models():
    print("🤖 CHECKING MODEL STATUS")
    print("="*40)
    
    models_to_check = [
        ("models/bert-test", "Round 1 Baseline (DEPRECATED)", "17 categories"),
        ("models/round2-28categories", "Round 2 Primary", "28 categories"),
        ("models/round2-simple", "Round 2 Backup", "28 categories")
    ]
    
    for model_dir, description, categories in models_to_check:
        model_path = Path(model_dir)
        
        print(f"\n📁 {description}")
        print(f"   Path: {model_dir}")
        print(f"   Categories: {categories}")
        
        if model_path.exists():
            print(f"   Status: ✅ EXISTS")
            
            # Check for key files
            key_files = ["config.json", "pytorch_model.bin", "tokenizer_config.json"]
            missing_files = []
            
            for file_name in key_files:
                if not (model_path / file_name).exists():
                    missing_files.append(file_name)
            
            if missing_files:
                print(f"   Missing: ⚠️ {missing_files}")
            else:
                print(f"   Files: ✅ Complete")
            
            # Check label mapping
            label_map_path = model_path / "label_map.json"
            if label_map_path.exists():
                try:
                    with open(label_map_path, 'r') as f:
                        label_data = json.load(f)
                    
                    if 'num_labels' in label_data:
                        num_labels = label_data['num_labels']
                        print(f"   Labels: ✅ {num_labels} categories")
                    else:
                        print(f"   Labels: ⚠️ No num_labels field")
                        
                except Exception as e:
                    print(f"   Labels: ❌ Error reading: {e}")
            else:
                print(f"   Labels: ❌ No label_map.json")
        else:
            print(f"   Status: ❌ NOT FOUND")
    
    # Check training data
    print(f"\n📊 TRAINING DATA STATUS")
    
    data_files = [
        ("data/complete_28_category_dataset.csv", "Complete 28-category dataset"),
        ("official_28_category_mapping.json", "Official category mapping"),
        ("data/enhanced_training_data.csv", "Enhanced dataset (deprecated)")
    ]
    
    for file_path, description in data_files:
        if Path(file_path).exists():
            print(f"   ✅ {description}: {file_path}")
        else:
            print(f"   ❌ {description}: {file_path}")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS")
    
    if Path("models/round2-28categories").exists():
        print("   ✅ Use models/round2-28categories/ for production")
    elif Path("models/round2-simple").exists():
        print("   ⚠️ Use models/round2-simple/ as backup")
    else:
        print("   🔄 Need to train Round 2 model")
    
    if not Path("data/complete_28_category_dataset.csv").exists():
        print("   ❌ Run: python fix_28_categories.py")
    
    print(f"\n📋 CURRENT PRIORITY: Train Round 2 model with 28 categories")

if __name__ == "__main__":
    check_models()
