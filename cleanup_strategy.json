{"safe_to_remove": [{"file": "models/bert-test/checkpoint-12/config.json", "reason": "Training checkpoints (keep latest only)", "size": 1831}, {"file": "models/bert-test/checkpoint-12/model.safetensors", "reason": "Training checkpoints (keep latest only)", "size": 438004788}, {"file": "models/bert-test/checkpoint-12/optimizer.pt", "reason": "Training checkpoints (keep latest only)", "size": 876125050}, {"file": "models/bert-test/checkpoint-12/rng_state.pth", "reason": "Training checkpoints (keep latest only)", "size": 13990}, {"file": "models/bert-test/checkpoint-12/scheduler.pt", "reason": "Training checkpoints (keep latest only)", "size": 1064}, {"file": "models/bert-test/checkpoint-12/special_tokens_map.json", "reason": "Training checkpoints (keep latest only)", "size": 132}, {"file": "models/bert-test/checkpoint-12/tokenizer.json", "reason": "Training checkpoints (keep latest only)", "size": 711494}, {"file": "models/bert-test/checkpoint-12/tokenizer_config.json", "reason": "Training checkpoints (keep latest only)", "size": 1277}, {"file": "models/bert-test/checkpoint-12/trainer_state.json", "reason": "Training checkpoints (keep latest only)", "size": 1045}, {"file": "models/bert-test/checkpoint-12/training_args.bin", "reason": "Training checkpoints (keep latest only)", "size": 5240}, {"file": "models/bert-test/checkpoint-12/vocab.txt", "reason": "Training checkpoints (keep latest only)", "size": 231508}, {"file": "models/round2-model/checkpoint-50/config.json", "reason": "Training checkpoints (keep latest only)", "size": 1465}, {"file": "models/round2-model/checkpoint-50/model.safetensors", "reason": "Training checkpoints (keep latest only)", "size": 438004788}, {"file": "models/round2-model/checkpoint-50/optimizer.pt", "reason": "Training checkpoints (keep latest only)", "size": 876125050}, {"file": "models/round2-model/checkpoint-50/rng_state.pth", "reason": "Training checkpoints (keep latest only)", "size": 13990}, {"file": "models/round2-model/checkpoint-50/scheduler.pt", "reason": "Training checkpoints (keep latest only)", "size": 1064}, {"file": "models/round2-model/checkpoint-50/trainer_state.json", "reason": "Training checkpoints (keep latest only)", "size": 2177}, {"file": "models/round2-model/checkpoint-50/training_args.bin", "reason": "Training checkpoints (keep latest only)", "size": 5240}, {"file": "models/round2-model/checkpoint-66/config.json", "reason": "Training checkpoints (keep latest only)", "size": 1465}, {"file": "models/round2-model/checkpoint-66/model.safetensors", "reason": "Training checkpoints (keep latest only)", "size": 438004788}, {"file": "models/round2-model/checkpoint-66/optimizer.pt", "reason": "Training checkpoints (keep latest only)", "size": 876125050}, {"file": "models/round2-model/checkpoint-66/rng_state.pth", "reason": "Training checkpoints (keep latest only)", "size": 13990}, {"file": "models/round2-model/checkpoint-66/scheduler.pt", "reason": "Training checkpoints (keep latest only)", "size": 1064}, {"file": "models/round2-model/checkpoint-66/trainer_state.json", "reason": "Training checkpoints (keep latest only)", "size": 2327}, {"file": "models/round2-model/checkpoint-66/training_args.bin", "reason": "Training checkpoints (keep latest only)", "size": 5240}, {"file": "models/round2-simple/checkpoint-24/config.json", "reason": "Training checkpoints (keep latest only)", "size": 1993}, {"file": "models/round2-simple/checkpoint-24/model.safetensors", "reason": "Training checkpoints (keep latest only)", "size": 438041700}, {"file": "models/round2-simple/checkpoint-24/optimizer.pt", "reason": "Training checkpoints (keep latest only)", "size": 876198778}, {"file": "models/round2-simple/checkpoint-24/rng_state.pth", "reason": "Training checkpoints (keep latest only)", "size": 13990}, {"file": "models/round2-simple/checkpoint-24/scheduler.pt", "reason": "Training checkpoints (keep latest only)", "size": 1064}, {"file": "models/round2-simple/checkpoint-24/trainer_state.json", "reason": "Training checkpoints (keep latest only)", "size": 1443}, {"file": "models/round2-simple/checkpoint-24/training_args.bin", "reason": "Training checkpoints (keep latest only)", "size": 5304}, {"file": "models/__pycache__/deberta_classifier.cpython-310.pyc", "reason": "Python cache directory", "size": 2734}, {"file": "models/__pycache__/llm_evaluator.cpython-310.pyc", "reason": "Python cache directory", "size": 12991}, {"file": "models/__pycache__/rag_implementation.cpython-310.pyc", "reason": "Python cache directory", "size": 5999}, {"file": "models/__pycache__/statement_comparator.cpython-310.pyc", "reason": "Python cache directory", "size": 4168}, {"file": "models/__pycache__/__init__.cpython-310.pyc", "reason": "Python cache directory", "size": 246}, {"file": "pipeline/__pycache__/classifier.cpython-310.pyc", "reason": "Python cache directory", "size": 6068}, {"file": "pipeline/__pycache__/__init__.cpython-310.pyc", "reason": "Python cache directory", "size": 195}, {"file": "rules/__pycache__/rules.cpython-310.pyc", "reason": "Python cache directory", "size": 5506}, {"file": "rules/__pycache__/rule_engine.cpython-310.pyc", "reason": "Python cache directory", "size": 1751}, {"file": "rules/__pycache__/__init__.cpython-310.pyc", "reason": "Python cache directory", "size": 371}, {"file": "utils/__pycache__/scraper.cpython-310.pyc", "reason": "Python cache directory", "size": 10283}, {"file": "utils/__pycache__/statement_categorizer.cpython-310.pyc", "reason": "Python cache directory", "size": 15637}, {"file": "__pycache__/base_knowledge_db.cpython-310.pyc", "reason": "Python cache directory", "size": 4321}, {"file": "__pycache__/llm_evaluator.cpython-310.pyc", "reason": "Python cache directory", "size": 13826}, {"file": "__pycache__/rag_implementation.cpython-310.pyc", "reason": "Python cache directory", "size": 5992}, {"file": "__pycache__/rules.cpython-310.pyc", "reason": "Python cache directory", "size": 5238}, {"file": "__pycache__/truth_algorithm.cpython-310.pyc", "reason": "Python cache directory", "size": 13021}, {"file": "__pycache__/truth_graph.cpython-310.pyc", "reason": "Python cache directory", "size": 7139}], "archive_candidates": [{"file": "data/ai_batch_results_20250708_232449.json", "reason": "Old AI batch results", "size": 21486}, {"file": "data/ai_batch_results_20250708_235154.json", "reason": "Old AI batch results", "size": 55214048}, {"file": "data/ai_batch_results_20250709_014454.json", "reason": "Old AI batch results", "size": 18407069}, {"file": "data/ai_batch_results_20250709_015354.json", "reason": "Old AI batch results", "size": 1196405594}, {"file": "data/statements_to_label_backup_20250709_153655.csv", "reason": "Old backup files from July", "size": 23162}, {"file": "data/statements_to_label_backup_20250709_154615.csv", "reason": "Old backup files from July", "size": 23162}, {"file": "data/statements_to_label_backup_corrected.csv", "reason": "Backup labeling files", "size": 15888}], "mark_as_legacy": [{"file": "data/enhanced_training_data.csv", "reason": "Replaced by complete_28_category_dataset.csv", "size": 22558}, {"file": "models/train_deberta.py", "reason": "Original training script", "size": 10327}, {"file": "models/bert-test/config.json", "reason": "Round 1 model (17 categories only)", "size": 1831}, {"file": "models/bert-test/label_map.json", "reason": "Round 1 model (17 categories only)", "size": 489}, {"file": "models/bert-test/model.safetensors", "reason": "Round 1 model (17 categories only)", "size": 438004788}, {"file": "models/bert-test/special_tokens_map.json", "reason": "Round 1 model (17 categories only)", "size": 132}, {"file": "models/bert-test/threshold_config.json", "reason": "Round 1 model (17 categories only)", "size": 200}, {"file": "models/bert-test/tokenizer.json", "reason": "Round 1 model (17 categories only)", "size": 711494}, {"file": "models/bert-test/tokenizer_config.json", "reason": "Round 1 model (17 categories only)", "size": 1277}, {"file": "models/bert-test/training_args.bin", "reason": "Round 1 model (17 categories only)", "size": 5240}, {"file": "models/bert-test/vocab.txt", "reason": "Round 1 model (17 categories only)", "size": 231508}], "keep_active": [{"file": "base_knowledge_db.py", "reason": "Data quality tool", "size": 5912}, {"file": "SESSION_STATUS.md", "reason": "Project status", "size": 15818}, {"file": "truth_algorithm.py", "reason": "Main algorithm", "size": 18050}, {"file": "data/complete_28_category_dataset.csv", "reason": "Current training data", "size": 29503}, {"file": "models/round2-simple/config.json", "reason": "Current model", "size": 1993}, {"file": "models/round2-simple/label_map.json", "reason": "Current model", "size": 2208}, {"file": "models/round2-simple/model.safetensors", "reason": "Current model", "size": 438041700}, {"file": "models/round2-simple/special_tokens_map.json", "reason": "Current model", "size": 132}, {"file": "models/round2-simple/tokenizer.json", "reason": "Current model", "size": 711661}, {"file": "models/round2-simple/tokenizer_config.json", "reason": "Current model", "size": 1277}, {"file": "models/round2-simple/vocab.txt", "reason": "Current model", "size": 231508}], "needs_review": [{"file": "analyze_data_quality.py", "reason": "Uncategorized - manual review needed", "size": 11848}, {"file": "analyze_training_data.py", "reason": "Uncategorized - manual review needed", "size": 8015}, {"file": "base_knowledge_database.json", "reason": "Uncategorized - manual review needed", "size": 7632}, {"file": "check_model_status.py", "reason": "Uncategorized - manual review needed", "size": 3294}, {"file": "COMMIT_SUMMARY.md", "reason": "Uncategorized - manual review needed", "size": 4832}, {"file": "create_label_mapping.py", "reason": "Uncategorized - manual review needed", "size": 1415}, {"file": "CurrentChecklistofState.txt", "reason": "Uncategorized - manual review needed", "size": 8083}, {"file": "custom_model.md", "reason": "Uncategorized - manual review needed", "size": 4940}, {"file": "data_enhancement_report.json", "reason": "Uncategorized - manual review needed", "size": 1189}, {"file": "data_quality_analysis.json", "reason": "Uncategorized - manual review needed", "size": 1626}, {"file": "enhance_training_data.py", "reason": "Uncategorized - manual review needed", "size": 11090}, {"file": "file_cleanup_report.txt", "reason": "Uncategorized - manual review needed", "size": 4158}, {"file": "file_cleanup_strategy.py", "reason": "Uncategorized - manual review needed", "size": 8749}, {"file": "file_maintenance_report.txt", "reason": "Uncategorized - manual review needed", "size": 503}, {"file": "fix_28_categories.py", "reason": "Uncategorized - manual review needed", "size": 14553}, {"file": "llm_cache.pkl", "reason": "Uncategorized - manual review needed", "size": 6086}, {"file": "maintain_file_registry.py", "reason": "Uncategorized - manual review needed", "size": 11987}, {"file": "manual_data_review.py", "reason": "Uncategorized - manual review needed", "size": 11316}, {"file": "manual_review_results.json", "reason": "Uncategorized - manual review needed", "size": 2374}, {"file": "Modelfile", "reason": "Uncategorized - manual review needed", "size": 2519}, {"file": "monitor_training.py", "reason": "Uncategorized - manual review needed", "size": 2549}, {"file": "nuanced_evaluation_system.py", "reason": "Uncategorized - manual review needed", "size": 20979}, {"file": "official_28_category_mapping.json", "reason": "Uncategorized - manual review needed", "size": 3177}, {"file": "ollama_timing.log", "reason": "Uncategorized - manual review needed", "size": 2472}, {"file": "pipeline_test_results.json", "reason": "Uncategorized - manual review needed", "size": 759}, {"file": "quick_model_test.py", "reason": "Uncategorized - manual review needed", "size": 4804}, {"file": "quick_ollama_test.py", "reason": "Uncategorized - manual review needed", "size": 1243}, {"file": "quick_round2_test.py", "reason": "Uncategorized - manual review needed", "size": 2798}, {"file": "quick_training_test.py", "reason": "Uncategorized - manual review needed", "size": 2903}, {"file": "README.md", "reason": "Uncategorized - manual review needed", "size": 7745}, {"file": "reference_guide.md", "reason": "Uncategorized - manual review needed", "size": 10956}, {"file": "registry_update.md", "reason": "Uncategorized - manual review needed", "size": 22742}, {"file": "requirements_deberta.txt", "reason": "Uncategorized - manual review needed", "size": 296}, {"file": "round2_error_analysis.json", "reason": "Uncategorized - manual review needed", "size": 13227}, {"file": "round2_improvement_plan.json", "reason": "Uncategorized - manual review needed", "size": 2580}, {"file": "round2_simple_results.json", "reason": "Uncategorized - manual review needed", "size": 246}, {"file": "round2_validation_results.json", "reason": "Uncategorized - manual review needed", "size": 7845}, {"file": "rules_analysis_guide.md", "reason": "Uncategorized - manual review needed", "size": 2893}, {"file": "safe_cleanup.py", "reason": "Uncategorized - manual review needed", "size": 0}, {"file": "sample.json", "reason": "Uncategorized - manual review needed", "size": 439}, {"file": "sample_results.json", "reason": "Uncategorized - manual review needed", "size": 1993}, {"file": "simple_error_analysis.py", "reason": "Uncategorized - manual review needed", "size": 9094}, {"file": "simple_pipeline_test.py", "reason": "Uncategorized - manual review needed", "size": 4124}, {"file": "simple_round2_training.py", "reason": "Uncategorized - manual review needed", "size": 6507}, {"file": "simple_validation_test.py", "reason": "Uncategorized - manual review needed", "size": 2593}, {"file": "start_ollama.bat", "reason": "Uncategorized - manual review needed", "size": 889}, {"file": "test_28_category_setup.py", "reason": "Uncategorized - manual review needed", "size": 2680}, {"file": "test_complete_pipeline.py", "reason": "Uncategorized - manual review needed", "size": 10584}, {"file": "test_deberta_threshold.py", "reason": "Uncategorized - manual review needed", "size": 3484}, {"file": "test_fast_pipeline.py", "reason": "Uncategorized - manual review needed", "size": 10125}, {"file": "test_improved_threshold.py", "reason": "Uncategorized - manual review needed", "size": 5078}, {"file": "test_ollama_connection.py", "reason": "Uncategorized - manual review needed", "size": 3446}, {"file": "test_ollama_startup.py", "reason": "Uncategorized - manual review needed", "size": 5552}, {"file": "test_rag_integration.py", "reason": "Uncategorized - manual review needed", "size": 3121}, {"file": "test_simple_statement.json", "reason": "Uncategorized - manual review needed", "size": 165}, {"file": "test_simple_statement_results.json", "reason": "Uncategorized - manual review needed", "size": 893}, {"file": "test_statements.json", "reason": "Uncategorized - manual review needed", "size": 1558}, {"file": "threshold_optimization.py", "reason": "Uncategorized - manual review needed", "size": 8844}, {"file": "threshold_optimization_results.json", "reason": "Uncategorized - manual review needed", "size": 2348}, {"file": "training_data_analysis.json", "reason": "Uncategorized - manual review needed", "size": 2702}, {"file": "train_round2_28categories.py", "reason": "Uncategorized - manual review needed", "size": 11486}, {"file": "train_round2_model.py", "reason": "Uncategorized - manual review needed", "size": 9727}, {"file": "truth_algorithm_vectorstore", "reason": "Uncategorized - manual review needed", "size": 24573218}, {"file": "truth_algorithm_vectorstore.index", "reason": "Uncategorized - manual review needed", "size": 19204653}, {"file": "truth_graph.py", "reason": "Uncategorized - manual review needed", "size": 8405}, {"file": "validate_file_registry.py", "reason": "Uncategorized - manual review needed", "size": 6762}, {"file": "validate_round2_performance.py", "reason": "Uncategorized - manual review needed", "size": 9254}, {"file": "validate_training_data.py", "reason": "Uncategorized - manual review needed", "size": 13249}, {"file": ".git/COMMIT_EDITMSG", "reason": "Uncategorized - manual review needed", "size": 557}, {"file": ".git/config", "reason": "Uncategorized - manual review needed", "size": 356}, {"file": ".git/description", "reason": "Uncategorized - manual review needed", "size": 73}, {"file": ".git/FETCH_HEAD", "reason": "Uncategorized - manual review needed", "size": 0}, {"file": ".git/HEAD", "reason": "Uncategorized - manual review needed", "size": 21}, {"file": ".git/index", "reason": "Uncategorized - manual review needed", "size": 8391}, {"file": ".git/hooks/applypatch-msg.sample", "reason": "Uncategorized - manual review needed", "size": 478}, {"file": ".git/hooks/commit-msg.sample", "reason": "Uncategorized - manual review needed", "size": 896}, {"file": ".git/hooks/fsmonitor-watchman.sample", "reason": "Uncategorized - manual review needed", "size": 4726}, {"file": ".git/hooks/post-update.sample", "reason": "Uncategorized - manual review needed", "size": 189}, {"file": ".git/hooks/pre-applypatch.sample", "reason": "Uncategorized - manual review needed", "size": 424}, {"file": ".git/hooks/pre-commit.sample", "reason": "Uncategorized - manual review needed", "size": 1649}, {"file": ".git/hooks/pre-merge-commit.sample", "reason": "Uncategorized - manual review needed", "size": 416}, {"file": ".git/hooks/pre-push.sample", "reason": "Uncategorized - manual review needed", "size": 1374}, {"file": ".git/hooks/pre-rebase.sample", "reason": "Uncategorized - manual review needed", "size": 4898}, {"file": ".git/hooks/pre-receive.sample", "reason": "Uncategorized - manual review needed", "size": 544}, {"file": ".git/hooks/prepare-commit-msg.sample", "reason": "Uncategorized - manual review needed", "size": 1492}, {"file": ".git/hooks/push-to-checkout.sample", "reason": "Uncategorized - manual review needed", "size": 2783}, {"file": ".git/hooks/sendemail-validate.sample", "reason": "Uncategorized - manual review needed", "size": 2308}, {"file": ".git/hooks/update.sample", "reason": "Uncategorized - manual review needed", "size": 3650}, {"file": ".git/info/exclude", "reason": "Uncategorized - manual review needed", "size": 240}, {"file": ".git/logs/HEAD", "reason": "Uncategorized - manual review needed", "size": 983}, {"file": ".git/logs/refs/heads/main", "reason": "Uncategorized - manual review needed", "size": 799}, {"file": ".git/logs/refs/remotes/origin/main", "reason": "Uncategorized - manual review needed", "size": 438}, {"file": ".git/objects/05/2f6cff51701eecb007e75c3957a5ddbcfdab80", "reason": "Uncategorized - manual review needed", "size": 813}, {"file": ".git/objects/05/a08d51432c8ca05501d170fbc36142a93a163e", "reason": "Uncategorized - manual review needed", "size": 237297}, {"file": ".git/objects/07/a8ea25ea81f387ad367faa742b5d71e91b634d", "reason": "Uncategorized - manual review needed", "size": 2516}, {"file": ".git/objects/07/d7b934a7b5fb9e0d7f91119396b97c04e5f223", "reason": "Uncategorized - manual review needed", "size": 510}, {"file": ".git/objects/07/fee44a20ebcceda753cebfc3c579722f861aa1", "reason": "Uncategorized - manual review needed", "size": 481}, {"file": ".git/objects/09/0dddc117ca9f0724b0ef28569a4d92ff9e265e", "reason": "Uncategorized - manual review needed", "size": 722}, {"file": ".git/objects/0a/af0ff5b7aa2f46221ef30df952422c9d332f35", "reason": "Uncategorized - manual review needed", "size": 68}, {"file": ".git/objects/0a/d225e59365d97b1e52138897a94be1a7333f3d", "reason": "Uncategorized - manual review needed", "size": 2315}, {"file": ".git/objects/0d/3062b3b77cfef0891caa561090fd27d3f8f57f", "reason": "Uncategorized - manual review needed", "size": 1847}, {"file": ".git/objects/0e/01aee5350c0fd2b489e043249ec0e9bb62fef1", "reason": "Uncategorized - manual review needed", "size": 1103}, {"file": ".git/objects/0e/110df1748d32cfd098b65bd23204ad89bb4fb2", "reason": "Uncategorized - manual review needed", "size": 786}, {"file": ".git/objects/0e/683e45c2eb43c53b347a1f79f937c129512960", "reason": "Uncategorized - manual review needed", "size": 288}, {"file": ".git/objects/11/0bdd77f395d85031b0bf8f163996f86af14e35", "reason": "Uncategorized - manual review needed", "size": 2156}, {"file": ".git/objects/12/1551b50d81e6ccd9738b0b132af881a198d391", "reason": "Uncategorized - manual review needed", "size": 310}, {"file": ".git/objects/13/204b7f1924f549acb93f6edcc271bcefe025d4", "reason": "Uncategorized - manual review needed", "size": 80}, {"file": ".git/objects/15/7fb28a3e24cdacc318be17f4197a21dff2bf4a", "reason": "Uncategorized - manual review needed", "size": 813}, {"file": ".git/objects/16/f10b12a6677948f300a32cd18e4ab2ef036d04", "reason": "Uncategorized - manual review needed", "size": 1647}, {"file": ".git/objects/17/6e5d1a4cfeae7ec9fa8104b6d8b900c22e453d", "reason": "Uncategorized - manual review needed", "size": 1347}, {"file": ".git/objects/19/dc94e01789e84a7ee2ccb979fd5f929a70695b", "reason": "Uncategorized - manual review needed", "size": 2495}, {"file": ".git/objects/1a/e9651a3d83ea0f6c4022b14475cf2a4a535a9f", "reason": "Uncategorized - manual review needed", "size": 3418}, {"file": ".git/objects/1a/f4e0575d5f0532b8e536a2508d19709d442262", "reason": "Uncategorized - manual review needed", "size": 3157}, {"file": ".git/objects/1b/7822de1574a827967ffd3cbc3c17f24a6766bd", "reason": "Uncategorized - manual review needed", "size": 2821}, {"file": ".git/objects/1d/2be9f96835e1c5186c8e9757762bc912737586", "reason": "Uncategorized - manual review needed", "size": 524}, {"file": ".git/objects/1d/58a98573e919c4983927d61b733214e589708e", "reason": "Uncategorized - manual review needed", "size": 2766}, {"file": ".git/objects/1d/919a622d71eed869ce6d39655af31fccbb03fb", "reason": "Uncategorized - manual review needed", "size": 3067}, {"file": ".git/objects/1f/f0985f6d2aa49d89ab99346fee27167986fbee", "reason": "Uncategorized - manual review needed", "size": 605}, {"file": ".git/objects/21/66ce354594fa53775afc42c39c9ed60b41dd3d", "reason": "Uncategorized - manual review needed", "size": 3071}, {"file": ".git/objects/22/41331aa1b1f25cf6e967701b7ec3c8d523ab15", "reason": "Uncategorized - manual review needed", "size": 130}, {"file": ".git/objects/22/651c002829e2f0a8d1c5a5d9bd9b0883f86767", "reason": "Uncategorized - manual review needed", "size": 764}, {"file": ".git/objects/23/560a17f1fd51762de6066addd96625e95a8183", "reason": "Uncategorized - manual review needed", "size": 1047}, {"file": ".git/objects/23/61ceba932c77b7f98c55a190d418307c6eda63", "reason": "Uncategorized - manual review needed", "size": 5770}, {"file": ".git/objects/23/8cb86e95f35a87fdb689b7d25445ed1c2243e9", "reason": "Uncategorized - manual review needed", "size": 2348}, {"file": ".git/objects/23/a63ce22c56a48ff4f6de0e19c0c320f3a6a2c0", "reason": "Uncategorized - manual review needed", "size": 378158553}, {"file": ".git/objects/25/51c9adddb993fb4dad16a9cf774a2363d1d6c5", "reason": "Uncategorized - manual review needed", "size": 2701}, {"file": ".git/objects/27/f5e84fe9489a8ec0f34e73dc1f285506467aaa", "reason": "Uncategorized - manual review needed", "size": 132}, {"file": ".git/objects/28/ba21d2487356866d2f3ad3c21b4e14260f36c4", "reason": "Uncategorized - manual review needed", "size": 5522}, {"file": ".git/objects/28/e5653b03bc3ebb58bb6f0bc308351ccc3d1f10", "reason": "Uncategorized - manual review needed", "size": 3159}, {"file": ".git/objects/29/4d81e00a532f7932cd7fffd591012731735aaf", "reason": "Uncategorized - manual review needed", "size": 241}, {"file": ".git/objects/2a/bbcb06c1623e6247d7c05caa320a21431b9775", "reason": "Uncategorized - manual review needed", "size": 1587}, {"file": ".git/objects/2b/ef7b1baaaceda7bceb219f5ce4d5aca59ee589", "reason": "Uncategorized - manual review needed", "size": 57}, {"file": ".git/objects/2c/ea5540e09520a31b515b2f6c3e698ee2b22354", "reason": "Uncategorized - manual review needed", "size": 724}, {"file": ".git/objects/2d/974991770ee2670e86607b88c356d8d40c1939", "reason": "Uncategorized - manual review needed", "size": 3627}, {"file": ".git/objects/2f/4be8f86e61c5025c79af20c2c1e85f51475913", "reason": "Uncategorized - manual review needed", "size": 2248}, {"file": ".git/objects/2f/c5dff6a87871eee5aed2be2f38a6b5dc10e017", "reason": "Uncategorized - manual review needed", "size": 490}, {"file": ".git/objects/34/18f2a3aca3b3e5b3b3972d70216a6d65341087", "reason": "Uncategorized - manual review needed", "size": 221}, {"file": ".git/objects/35/0ffa2e2ef1dfab5b9cb2f7838fcce0127c36d5", "reason": "Uncategorized - manual review needed", "size": 2533}, {"file": ".git/objects/35/d45741de4809987e9a2f6f93bc8ab1c19a52e5", "reason": "Uncategorized - manual review needed", "size": 1707}, {"file": ".git/objects/36/272a41302bb6c09e3fcf937083f31e9b2ea431", "reason": "Uncategorized - manual review needed", "size": 691}, {"file": ".git/objects/36/c530bae814cdeff314b9792c6075403f9112ed", "reason": "Uncategorized - manual review needed", "size": 231}, {"file": ".git/objects/38/655e77980065f348cab602af4d7449991370c4", "reason": "Uncategorized - manual review needed", "size": 1764}, {"file": ".git/objects/39/9ba067d4a370f2c89f62e6bbd98bdda39db958", "reason": "Uncategorized - manual review needed", "size": 2085}, {"file": ".git/objects/3a/1ddfa0b6cff03bfff84e8008766510272b075d", "reason": "Uncategorized - manual review needed", "size": 352}, {"file": ".git/objects/3b/97071e0a90aae464501932ef0333ee42115b40", "reason": "Uncategorized - manual review needed", "size": 2789}, {"file": ".git/objects/3d/0ba6f96dc98ff5f85e4d004d9d9c1a924a7c0f", "reason": "Uncategorized - manual review needed", "size": 1515}, {"file": ".git/objects/3d/0d4a668d3d556bca41e16563938e849ed20f6d", "reason": "Uncategorized - manual review needed", "size": 2914}, {"file": ".git/objects/3e/3d54eb333a03e246bd60037d93c063d91235e1", "reason": "Uncategorized - manual review needed", "size": 129}, {"file": ".git/objects/3e/b3fd5b77631d2431a7519d13f727a0ffe7116d", "reason": "Uncategorized - manual review needed", "size": 178}, {"file": ".git/objects/40/6246004fbbc8c5d56ceeee2a5be1aed664b2a9", "reason": "Uncategorized - manual review needed", "size": 787}, {"file": ".git/objects/43/abbf602e199194b6d9dfe88893f16ffe75ea99", "reason": "Uncategorized - manual review needed", "size": 1151}, {"file": ".git/objects/45/410c7328cc0242583574ff716a4c4e9d6453c7", "reason": "Uncategorized - manual review needed", "size": 2242}, {"file": ".git/objects/46/604c91faee3c2e392631783a6b23f87bf80046", "reason": "Uncategorized - manual review needed", "size": 675}, {"file": ".git/objects/46/62c8aacbed4a2bcc0075ecd2d52ca277910b6e", "reason": "Uncategorized - manual review needed", "size": 2978}, {"file": ".git/objects/46/b733ad7e098b0daa82979c9b608a66b85707fa", "reason": "Uncategorized - manual review needed", "size": 184}, {"file": ".git/objects/47/bc43358da51ee6b8bfc0e43cfc15a18c511e1c", "reason": "Uncategorized - manual review needed", "size": 2273}, {"file": ".git/objects/48/2a344bbbe780da44bbab702f40ae301c9e907c", "reason": "Uncategorized - manual review needed", "size": 7741859}, {"file": ".git/objects/48/f70f08c7155ebb627cd42619a9d0446b56750b", "reason": "Uncategorized - manual review needed", "size": 5114556}, {"file": ".git/objects/49/260ecb89641f637e89f7329adebe8a99b6aa24", "reason": "Uncategorized - manual review needed", "size": 3407}, {"file": ".git/objects/49/a71f8ecc9a524d966d3ee2ac3ecffa77af6da7", "reason": "Uncategorized - manual review needed", "size": 1213}, {"file": ".git/objects/49/b91fadf1eead1232a277f90594b363b6fbde73", "reason": "Uncategorized - manual review needed", "size": 6850}, {"file": ".git/objects/49/e69969c601bb79aea0ad2f01df33d697d056dd", "reason": "Uncategorized - manual review needed", "size": 218}, {"file": ".git/objects/4a/b436cd867733c58834fbac3f6267038321c614", "reason": "Uncategorized - manual review needed", "size": 3551}, {"file": ".git/objects/4a/deb338f87f9b6ee83df68b38cdde61dd6c2631", "reason": "Uncategorized - manual review needed", "size": 1655}, {"file": ".git/objects/4b/54035911a519ff0dd37cfe7b8b2ce336bb4f1c", "reason": "Uncategorized - manual review needed", "size": 2892}, {"file": ".git/objects/4b/dd4c1486328a5b25878297929645e10e90ef87", "reason": "Uncategorized - manual review needed", "size": 3627}, {"file": ".git/objects/4c/337184d6441e185d94271447bb00b0aaa057d2", "reason": "Uncategorized - manual review needed", "size": 3379}, {"file": ".git/objects/4c/9477a9be45c4e0161137a57d1fa7e1ab4341e1", "reason": "Uncategorized - manual review needed", "size": 882}, {"file": ".git/objects/4d/3d316fb496f67afbb65304b81173057afa4700", "reason": "Uncategorized - manual review needed", "size": 1140}, {"file": ".git/objects/4d/99846ab99fb2da9f6afbcffec42d19c33d9ca3", "reason": "Uncategorized - manual review needed", "size": 16859}, {"file": ".git/objects/4e/9518adf3a34a2dd182b757c6557ea98c490fca", "reason": "Uncategorized - manual review needed", "size": 3272}, {"file": ".git/objects/4e/aaa084c5d10c79bd14cab2d02fa9b49b2699d3", "reason": "Uncategorized - manual review needed", "size": 231}, {"file": ".git/objects/52/df994fd5ae50a301a74bd8ff35880c041bdd15", "reason": "Uncategorized - manual review needed", "size": 17859914}, {"file": ".git/objects/53/2970013123adbc1b0d73f636b961f2317b109d", "reason": "Uncategorized - manual review needed", "size": 5750}, {"file": ".git/objects/53/4684b1a4ae62aeaf52882587b18499302029cb", "reason": "Uncategorized - manual review needed", "size": 824}, {"file": ".git/objects/53/9b30e32d1fd71880fb6884c6b814e9bf49f626", "reason": "Uncategorized - manual review needed", "size": 2309}, {"file": ".git/objects/54/c7a47ecce4a585be1b3aec95f84bf34f4ba287", "reason": "Uncategorized - manual review needed", "size": 816}, {"file": ".git/objects/55/82d14984bdd647b6114149b0cab6834abded4b", "reason": "Uncategorized - manual review needed", "size": 235}, {"file": ".git/objects/55/c280b3da9c762fa3c6ab34165fee7ff1b0f8db", "reason": "Uncategorized - manual review needed", "size": 3603}, {"file": ".git/objects/55/e0f90be8396175514255283d24c3137ae938cc", "reason": "Uncategorized - manual review needed", "size": 751}, {"file": ".git/objects/57/2a6fe7ca03f53442b417414211e879931c3dfe", "reason": "Uncategorized - manual review needed", "size": 1260}, {"file": ".git/objects/57/db3332ca14c7e3b79749617cd40d7e315a4f55", "reason": "Uncategorized - manual review needed", "size": 1952}, {"file": ".git/objects/59/3cbd1f7349bbe0cb7bdc644e474ff7cbab27b7", "reason": "Uncategorized - manual review needed", "size": 2984}, {"file": ".git/objects/59/faf3279ee19680027da3db7fd3362fa535ae08", "reason": "Uncategorized - manual review needed", "size": 2832}, {"file": ".git/objects/5f/a6e6bbbd4f72c5796d3bb0a80644a77ffb51e5", "reason": "Uncategorized - manual review needed", "size": 1851}, {"file": ".git/objects/5f/c22d1577701149c66080999e9ffa6d71271994", "reason": "Uncategorized - manual review needed", "size": 159}, {"file": ".git/objects/61/7da6fc99ae414505c62be3678368bbb2c22987", "reason": "Uncategorized - manual review needed", "size": 1650}, {"file": ".git/objects/63/b40182d0e57834ded808699e4da5a68ccc9bd8", "reason": "Uncategorized - manual review needed", "size": 1907}, {"file": ".git/objects/64/40c177fdb88d7d5dbccaefd741499abf251d9c", "reason": "Uncategorized - manual review needed", "size": 20894276}, {"file": ".git/objects/64/76e28876bdc36ca61f277871baecdfb9374859", "reason": "Uncategorized - manual review needed", "size": 905}, {"file": ".git/objects/65/e793c0a2fefd2e75473aa8d45bd7280135b8ab", "reason": "Uncategorized - manual review needed", "size": 1129}, {"file": ".git/objects/66/c7bf7525f95a92c8bd1f669e361ff80404a85f", "reason": "Uncategorized - manual review needed", "size": 578}, {"file": ".git/objects/68/7691f72eff25ea95d75988343e3f06cfa00151", "reason": "Uncategorized - manual review needed", "size": 1050}, {"file": ".git/objects/69/f03a1d04492460842e771aea5b21db3544645a", "reason": "Uncategorized - manual review needed", "size": 3392}, {"file": ".git/objects/6c/8ce68933cb9901e3557105305ee701a95d68e3", "reason": "Uncategorized - manual review needed", "size": 5659}, {"file": ".git/objects/6d/deda9a59c3037e8d3d4b1ef6d39051927482ac", "reason": "Uncategorized - manual review needed", "size": 1156}, {"file": ".git/objects/6e/4cabed78db615a9a175dbce20b1635441bf911", "reason": "Uncategorized - manual review needed", "size": 3906}, {"file": ".git/objects/6e/b2b10ba9cf974f4fafa4a48fa2f9e9f616463a", "reason": "Uncategorized - manual review needed", "size": 555}, {"file": ".git/objects/6f/9c7774a54621bfd6ecc0d6975fb219a303544c", "reason": "Uncategorized - manual review needed", "size": 2334}, {"file": ".git/objects/73/863ae0e154239619e7fc045099042230a7d416", "reason": "Uncategorized - manual review needed", "size": 6464}, {"file": ".git/objects/74/e6441e843dc0ef2c398e9280c1b99af4f34222", "reason": "Uncategorized - manual review needed", "size": 833}, {"file": ".git/objects/75/20992f25914d962f0e2fd0e0566fc33d19ec59", "reason": "Uncategorized - manual review needed", "size": 90}, {"file": ".git/objects/75/40dd594225bc59300f40a9e84ca4c8e6aebdcc", "reason": "Uncategorized - manual review needed", "size": 1014}, {"file": ".git/objects/76/e6114069f830d0efe9f6cf61a5199e68565b3c", "reason": "Uncategorized - manual review needed", "size": 4183}, {"file": ".git/objects/77/17168cebec36a57d805ec751e8c43585fcdac0", "reason": "Uncategorized - manual review needed", "size": 4589}, {"file": ".git/objects/77/1e31970d56133fb8d2be9ebcd6cc5a5bb167ad", "reason": "Uncategorized - manual review needed", "size": 1154089}, {"file": ".git/objects/78/37a9d35550664d06719b84473a686586f5da78", "reason": "Uncategorized - manual review needed", "size": 683}, {"file": ".git/objects/7b/e41f0547add3099a0047f08d593f03b14fd46b", "reason": "Uncategorized - manual review needed", "size": 2971}, {"file": ".git/objects/7c/bc9b5280d58cf0af4bd701a3bb90e03c5c0ddc", "reason": "Uncategorized - manual review needed", "size": 11408}, {"file": ".git/objects/7d/40c0084e555d59da513c49ba62f5542b6289a2", "reason": "Uncategorized - manual review needed", "size": 129}, {"file": ".git/objects/7e/b140ef906c752092572b6f2cc601d3d4ce3fe3", "reason": "Uncategorized - manual review needed", "size": 1155}, {"file": ".git/objects/81/6f547deea5668b71c4ae6b618d1a9fa3d8abe7", "reason": "Uncategorized - manual review needed", "size": 7106}, {"file": ".git/objects/84/c7d1db1ebc3c2c338f512cec5d3720873ab2ef", "reason": "Uncategorized - manual review needed", "size": 2396}, {"file": ".git/objects/86/5caeab7e8e76c4f8b42f18764c59ac091e2794", "reason": "Uncategorized - manual review needed", "size": 502}, {"file": ".git/objects/87/e74d206686ea3fbc379a7361fe2a6b4d103531", "reason": "Uncategorized - manual review needed", "size": 2140}, {"file": ".git/objects/88/6998053d007dc8042497438437b54bdb83beb3", "reason": "Uncategorized - manual review needed", "size": 405006}, {"file": ".git/objects/88/9867736cb2969c71dedcfff3d54a219b01a900", "reason": "Uncategorized - manual review needed", "size": 3498}, {"file": ".git/objects/89/325af5a112528d2659bb012f52818374e5e929", "reason": "Uncategorized - manual review needed", "size": 285}, {"file": ".git/objects/89/ab91b0e73e5f7db2b2ab378965b851e7377081", "reason": "Uncategorized - manual review needed", "size": 6375}, {"file": ".git/objects/8c/eaf6bfcb89e11b7bb753f57fb53256084e08aa", "reason": "Uncategorized - manual review needed", "size": 428}, {"file": ".git/objects/8f/778e7d950e055b18b53222c75d2c54c3732df5", "reason": "Uncategorized - manual review needed", "size": 237357}, {"file": ".git/objects/90/877e4e91c9e57f14e7eb080bc77974e127cc6f", "reason": "Uncategorized - manual review needed", "size": 10149}, {"file": ".git/objects/91/67e07e99dde1f7b0cc11728aea6e5e630039b4", "reason": "Uncategorized - manual review needed", "size": 1241}, {"file": ".git/objects/92/f820fae21dcec068c0db664adc59c73684f1f4", "reason": "Uncategorized - manual review needed", "size": 1936}, {"file": ".git/objects/93/80b082f2909894849321636cca0d48e25f8f9d", "reason": "Uncategorized - manual review needed", "size": 596}, {"file": ".git/objects/95/1200427cddf6fbf0b2e872fecf4bb5d955aa1a", "reason": "Uncategorized - manual review needed", "size": 10150}, {"file": ".git/objects/96/d8a8a79002feaa2347280a7a66862ead32d3e2", "reason": "Uncategorized - manual review needed", "size": 2716}, {"file": ".git/objects/97/6a2fd3099481d940a15aa6c2c603541114cede", "reason": "Uncategorized - manual review needed", "size": 105}, {"file": ".git/objects/98/b6066c9544a774e52c345c106495d27c3a34a2", "reason": "Uncategorized - manual review needed", "size": 144}, {"file": ".git/objects/99/b03358e6a24f057a0db9a4372928d9d5de7640", "reason": "Uncategorized - manual review needed", "size": 121}, {"file": ".git/objects/9c/2b39f1904ca5a9c1b0617612c3cb125e3e0cd9", "reason": "Uncategorized - manual review needed", "size": 2628}, {"file": ".git/objects/a2/475ffb6964f432a110dd0a7aec7f74e03a41cc", "reason": "Uncategorized - manual review needed", "size": 5386}, {"file": ".git/objects/a4/46e3fa6498d46b2b0e6e05b7330c59a604054f", "reason": "Uncategorized - manual review needed", "size": 3027}, {"file": ".git/objects/a5/412ec8a9983393d2cc3385af59a8c962201282", "reason": "Uncategorized - manual review needed", "size": 3219}, {"file": ".git/objects/a5/b0934eb75a58a788f4f28c0362da246540a85e", "reason": "Uncategorized - manual review needed", "size": 1584}, {"file": ".git/objects/a5/b1ec605ac57482ec8c11350cfb24fd837f09aa", "reason": "Uncategorized - manual review needed", "size": 9035}, {"file": ".git/objects/a6/218044b4e301e156cc5fac7a2b2d29e5a7731d", "reason": "Uncategorized - manual review needed", "size": 288}, {"file": ".git/objects/a7/c11b70caadad925899a07eef7207dbdef7417f", "reason": "Uncategorized - manual review needed", "size": 3358}, {"file": ".git/objects/a9/0334b108322d1a0f0f93337d8d59c60d49127c", "reason": "Uncategorized - manual review needed", "size": 281}, {"file": ".git/objects/aa/4c670b7c70327d95c9848e8c1dab09a7d11057", "reason": "Uncategorized - manual review needed", "size": 604}, {"file": ".git/objects/aa/847b01f7aee3c167b69b325dff3c1d15e1da68", "reason": "Uncategorized - manual review needed", "size": 172}, {"file": ".git/objects/ab/03971c004c377bcf9aeaa507dd5c97b0f19e28", "reason": "Uncategorized - manual review needed", "size": 237362}, {"file": ".git/objects/ab/04e8be07948eaffac7e1387b98eafe79d3a9b3", "reason": "Uncategorized - manual review needed", "size": 1143}, {"file": ".git/objects/ad/3f7ae681cbb6e3977317316c536b52a349afa3", "reason": "Uncategorized - manual review needed", "size": 2330}, {"file": ".git/objects/ad/e81923f5ae9600dcf4c87aa10308f5cded607a", "reason": "Uncategorized - manual review needed", "size": 378164578}, {"file": ".git/objects/ae/11a18728191aaff60ad8bc1f05dfcbd91d3974", "reason": "Uncategorized - manual review needed", "size": 462}, {"file": ".git/objects/af/adcc28dd62fbf3bb29718d8a33401e4efe414e", "reason": "Uncategorized - manual review needed", "size": 2150}, {"file": ".git/objects/b0/e0b44ed6d6f8be0df52666fffeb07278192b1d", "reason": "Uncategorized - manual review needed", "size": 129}, {"file": ".git/objects/b1/65826614b017c5dd34aae9501d7dbc2c04fa19", "reason": "Uncategorized - manual review needed", "size": 6782}, {"file": ".git/objects/b3/a3d1ab306a2a95499ace04a991bd7eb1fbf9b2", "reason": "Uncategorized - manual review needed", "size": 717}, {"file": ".git/objects/b5/b5fd93fe0eb6463d1b2b1f083e01523ff430b8", "reason": "Uncategorized - manual review needed", "size": 711}, {"file": ".git/objects/b6/1e61f85d374f7ea8c057badaabf0d2761f6a65", "reason": "Uncategorized - manual review needed", "size": 5711}, {"file": ".git/objects/b7/b43ae8c3740c7241491d2720429e12ce2109b9", "reason": "Uncategorized - manual review needed", "size": 1447}, {"file": ".git/objects/b8/da099693d243f1664a0fea0a0b5533d8531965", "reason": "Uncategorized - manual review needed", "size": 4492}, {"file": ".git/objects/b9/431747ed4f4cb3c7756a8bbdb951deb1e94a1f", "reason": "Uncategorized - manual review needed", "size": 5121}, {"file": ".git/objects/ba/bbe681cd6bd50bc453537f0a81f8d422638fcb", "reason": "Uncategorized - manual review needed", "size": 2556}, {"file": ".git/objects/ba/db545ddeb7655fb4313457cbef1942bb6a667d", "reason": "Uncategorized - manual review needed", "size": 2262}, {"file": ".git/objects/bb/08b8355c37bb90c23ee2709472bcf2725bd2a1", "reason": "Uncategorized - manual review needed", "size": 254}, {"file": ".git/objects/bb/1a5cdc0a9d3173593b91ef2c076eb47237560f", "reason": "Uncategorized - manual review needed", "size": 1228}, {"file": ".git/objects/bb/2ab07ee0b130bb5cba1a3e7384b07ab238408b", "reason": "Uncategorized - manual review needed", "size": 6256}, {"file": ".git/objects/bc/149c17cfffa3342fad7bc1730cca1efde02ef1", "reason": "Uncategorized - manual review needed", "size": 2131}, {"file": ".git/objects/bd/fa2282375de760e7f2b8a3d190db785dcbab4e", "reason": "Uncategorized - manual review needed", "size": 193}, {"file": ".git/objects/be/1c752092dc2bee81b1fe525960b4d1d757a8df", "reason": "Uncategorized - manual review needed", "size": 213}, {"file": ".git/objects/c0/4d9f910453b39a2fff95ba40b16592133c5a30", "reason": "Uncategorized - manual review needed", "size": 2966}, {"file": ".git/objects/c0/9c44303474e7ee698ea1c5c48b534eac5869e0", "reason": "Uncategorized - manual review needed", "size": 1013}, {"file": ".git/objects/c3/00b5f22fe672eaf24bdcec21d3ad410174c687", "reason": "Uncategorized - manual review needed", "size": 7844}, {"file": ".git/objects/c4/2b65da146152698ceb3d031ecec7c88a878dff", "reason": "Uncategorized - manual review needed", "size": 219}, {"file": ".git/objects/ca/b103c0c4991ff09bb8a91c81952670d9d4239f", "reason": "Uncategorized - manual review needed", "size": 438959}, {"file": ".git/objects/ca/de183758662aad75e0ee490fa4973dc3f73024", "reason": "Uncategorized - manual review needed", "size": 3307}, {"file": ".git/objects/cb/d11c071e206da504eeef869c2c6fa10196d32f", "reason": "Uncategorized - manual review needed", "size": 6549}, {"file": ".git/objects/cc/a2344c03f2db92b21a2e3030a72962f7a54d01", "reason": "Uncategorized - manual review needed", "size": 3562}, {"file": ".git/objects/cd/25167839cadd7c1dcef2c71fa9b1ca00597567", "reason": "Uncategorized - manual review needed", "size": 37}, {"file": ".git/objects/cd/64e4991f7a237f60d7ce155a16bf62dbc1da3e", "reason": "Uncategorized - manual review needed", "size": 2580941}, {"file": ".git/objects/cd/ae5ffa5bdaa1f912993727e6ded6bf2a322ca5", "reason": "Uncategorized - manual review needed", "size": 955}, {"file": ".git/objects/ce/2201694e75e6f9994040f212a441e397557230", "reason": "Uncategorized - manual review needed", "size": 1226}, {"file": ".git/objects/d0/18689d920586397c76af290a8a337626008240", "reason": "Uncategorized - manual review needed", "size": 1224}, {"file": ".git/objects/d1/02fce141411e7ae1013520e5751717efff9339", "reason": "Uncategorized - manual review needed", "size": 3238}, {"file": ".git/objects/d2/a57b97b46957913190f617d0d4d7f6b530c33c", "reason": "Uncategorized - manual review needed", "size": 10127}, {"file": ".git/objects/d5/b90640ff616a22e9121f99a429ce2058a81200", "reason": "Uncategorized - manual review needed", "size": 2269}, {"file": ".git/objects/d7/769f8c2c08f4ca9cd33466daa24ce78f0dbdc2", "reason": "Uncategorized - manual review needed", "size": 458}, {"file": ".git/objects/d7/c6edaa1d58d9e9f201d3b55de68a357afdece8", "reason": "Uncategorized - manual review needed", "size": 400}, {"file": ".git/objects/d9/f1b73e76dea6d5aa2ce949c0917288416564e8", "reason": "Uncategorized - manual review needed", "size": 492}, {"file": ".git/objects/db/5d648f561bdf2cd1bb59527ef4ce277c2a7d15", "reason": "Uncategorized - manual review needed", "size": 501}, {"file": ".git/objects/db/d1a83b0202688438403b865ce27dca6940ec67", "reason": "Uncategorized - manual review needed", "size": 105}, {"file": ".git/objects/db/f401a190766282aff7bac14bd75cd6d88905e8", "reason": "Uncategorized - manual review needed", "size": 828}, {"file": ".git/objects/dd/809395188cbe652b903b6fcc1765e0da259a24", "reason": "Uncategorized - manual review needed", "size": 10171}, {"file": ".git/objects/e1/98153f9a13df5ade6611f01dfab838dd8488fc", "reason": "Uncategorized - manual review needed", "size": 795}, {"file": ".git/objects/e1/f7d1139bfe7e87e726861d6f427b032efb36e6", "reason": "Uncategorized - manual review needed", "size": 164}, {"file": ".git/objects/e3/010b39ae101dacd8a12ccd93ee85ab93f8051f", "reason": "Uncategorized - manual review needed", "size": 2611}, {"file": ".git/objects/e3/a027e421cac97f8cc5aa789406ebeee0385534", "reason": "Uncategorized - manual review needed", "size": 309}, {"file": ".git/objects/e4/870072a38eb65fe4e523b3e8ffe556419f7a7c", "reason": "Uncategorized - manual review needed", "size": 2980}, {"file": ".git/objects/e5/c80d43bd06e844e2cac53619058bd4828ad50f", "reason": "Uncategorized - manual review needed", "size": 1175}, {"file": ".git/objects/e5/e871b782ac41f0e3fd40faf7df5b62542ddf90", "reason": "Uncategorized - manual review needed", "size": 3097}, {"file": ".git/objects/e6/5200d50ca33284023757e3da891f69a448df4c", "reason": "Uncategorized - manual review needed", "size": 543}, {"file": ".git/objects/e6/9de29bb2d1d6434b8b29ae775ad8c2e48c5391", "reason": "Uncategorized - manual review needed", "size": 15}, {"file": ".git/objects/e7/901eccc25f309fde0031440db73a893a539353", "reason": "Uncategorized - manual review needed", "size": 5758}, {"file": ".git/objects/e9/a56c87d92709f3c8b6352aee9843209e54ab2e", "reason": "Uncategorized - manual review needed", "size": 2540}, {"file": ".git/objects/ea/cb95d81ef9930b51ca9113f7dee0e6a4dc39f0", "reason": "Uncategorized - manual review needed", "size": 288}, {"file": ".git/objects/ea/ff51fd8948d68bb7635460f1272673c2620535", "reason": "Uncategorized - manual review needed", "size": 1923}, {"file": ".git/objects/eb/744ffb8fb299c95988d4f1e8f54327eb427f72", "reason": "Uncategorized - manual review needed", "size": 378149998}, {"file": ".git/objects/ec/af28bdb56f88efb54fc2b6573b3c827eefe8b3", "reason": "Uncategorized - manual review needed", "size": 2865}, {"file": ".git/objects/ec/bd3c6eb03d5025de6c2d5dd3876e34afce9761", "reason": "Uncategorized - manual review needed", "size": 505}, {"file": ".git/objects/ee/28586ec311b0cd3005ba7529f73e061676acdb", "reason": "Uncategorized - manual review needed", "size": 570}, {"file": ".git/objects/ee/d3f8bce699b2d61f146bac297fcdf3c00cec63", "reason": "Uncategorized - manual review needed", "size": 3662}, {"file": ".git/objects/ef/b0d2cc4fb412f975ae19113c797731939ba970", "reason": "Uncategorized - manual review needed", "size": 2285}, {"file": ".git/objects/f1/52d62b99ab37e18257c275c8b09b989f7b0665", "reason": "Uncategorized - manual review needed", "size": 2709}, {"file": ".git/objects/f1/94d7b9acf1d6f09e5183856a8d36dd85e5c89d", "reason": "Uncategorized - manual review needed", "size": 9169}, {"file": ".git/objects/f3/942fe2367aee4b3b0f5ecc5904d32a6efb9969", "reason": "Uncategorized - manual review needed", "size": 2401}, {"file": ".git/objects/f5/0764701417450b22010b8a9e076771d9dc0edd", "reason": "Uncategorized - manual review needed", "size": 111}, {"file": ".git/objects/f5/c105d00c93818dfe2b1f2f5cd9b10549dd6691", "reason": "Uncategorized - manual review needed", "size": 3425}, {"file": ".git/objects/f6/42530484ad87cc374a9621bb091ac629f13c87", "reason": "Uncategorized - manual review needed", "size": 2961}, {"file": ".git/objects/f6/85cd12b53d6daf5db9ea2ce673af1f87923b15", "reason": "Uncategorized - manual review needed", "size": 183}, {"file": ".git/objects/f6/93ec86c484c6706ec8abc95cda1f56b784e14f", "reason": "Uncategorized - manual review needed", "size": 1382}, {"file": ".git/objects/f6/f9cf37f951d327e28d8d28ae501e0e350ee9b0", "reason": "Uncategorized - manual review needed", "size": 2469}, {"file": ".git/objects/f7/977a82423e0631b11f28c08203bbbf1a445d56", "reason": "Uncategorized - manual review needed", "size": 2120}, {"file": ".git/objects/f9/aaefde56fb8f9cb7a541342b8bf915c45162f2", "reason": "Uncategorized - manual review needed", "size": 3861}, {"file": ".git/objects/fa/1977f22351142e55093034adbe704a7d605f66", "reason": "Uncategorized - manual review needed", "size": 121}, {"file": ".git/objects/fa/89ffd7af16c729b6f1583f34db59cc08917306", "reason": "Uncategorized - manual review needed", "size": 3075}, {"file": ".git/objects/fb/140275c155a9c7c5a3b3e0e77a9e839594a938", "reason": "Uncategorized - manual review needed", "size": 121530}, {"file": ".git/objects/fb/61684d60e4888b596a810a2bdaeed86d0d0568", "reason": "Uncategorized - manual review needed", "size": 5977}, {"file": ".git/objects/fb/87d5dc0162bef29a096a7c1d2e3ab8b26216ca", "reason": "Uncategorized - manual review needed", "size": 2233}, {"file": ".git/objects/fb/fccad0df91cdf754499ef0ce9ce458aaf9b83a", "reason": "Uncategorized - manual review needed", "size": 7141}, {"file": ".git/objects/fc/d6896538843a6c818a10d050cfa8760b2742d4", "reason": "Uncategorized - manual review needed", "size": 32192}, {"file": ".git/objects/fd/954a60547540755911fba86e6244ac8b946882", "reason": "Uncategorized - manual review needed", "size": 668}, {"file": ".git/objects/fd/e9696a512477cb6500eef6d6398ca2c3eeae57", "reason": "Uncategorized - manual review needed", "size": 3433}, {"file": ".git/objects/ff/ceb1131227a11c6f98c608227cc10739fe6a84", "reason": "Uncategorized - manual review needed", "size": 192}, {"file": ".git/refs/heads/main", "reason": "Uncategorized - manual review needed", "size": 41}, {"file": ".git/refs/remotes/origin/main", "reason": "Uncategorized - manual review needed", "size": 41}, {"file": "data/statements_to_label.csv", "reason": "Uncategorized - manual review needed", "size": 15779}, {"file": "docs/32243356-Investigations.pdf", "reason": "Uncategorized - manual review needed", "size": 5284028}, {"file": "docs/Admin Dictionary.txt", "reason": "Uncategorized - manual review needed", "size": 2592329}, {"file": "docs/Algorithm.txt", "reason": "Uncategorized - manual review needed", "size": 5524}, {"file": "docs/DeBERTaandLLM(2).txt", "reason": "Uncategorized - manual review needed", "size": 5442}, {"file": "docs/DeBERTa_Implementation_Plan.md", "reason": "Uncategorized - manual review needed", "size": 5873}, {"file": "docs/DeBERTa_Labeling_Guide.md", "reason": "Uncategorized - manual review needed", "size": 4961}, {"file": "docs/FILE_REGISTRY.md", "reason": "Uncategorized - manual review needed", "size": 10234}, {"file": "docs/ImproveLLMKnowledgePlan.txt", "reason": "Uncategorized - manual review needed", "size": 2548}, {"file": "docs/InitialClassificationPlan.txt", "reason": "Uncategorized - manual review needed", "size": 6122}, {"file": "docs/Investigations.txt", "reason": "Uncategorized - manual review needed", "size": 73603}, {"file": "docs/ITERATIVE_IMPROVEMENT_PLAN.md", "reason": "Uncategorized - manual review needed", "size": 7062}, {"file": "docs/Labeling_Tools_Comparison.md", "reason": "Uncategorized - manual review needed", "size": 5126}, {"file": "docs/LLM_STARTUP_GUIDE.md", "reason": "Uncategorized - manual review needed", "size": 4102}, {"file": "docs/MODEL_DOCUMENTATION.md", "reason": "Uncategorized - manual review needed", "size": 9764}, {"file": "docs/NGVManagementSeriesV1.txt", "reason": "Uncategorized - manual review needed", "size": 928722}, {"file": "docs/ollama_server_output.txt", "reason": "Uncategorized - manual review needed", "size": 106696}, {"file": "docs/outpoint_pluspoint_definitions.txt", "reason": "Uncategorized - manual review needed", "size": 5519}, {"file": "docs/output.csv", "reason": "Uncategorized - manual review needed", "size": 51898}, {"file": "docs/Plan.txt", "reason": "Uncategorized - manual review needed", "size": 5196}, {"file": "docs/project_structure.txt", "reason": "Uncategorized - manual review needed", "size": 958}, {"file": "docs/RecentFixes_2025-01-03.md", "reason": "Uncategorized - manual review needed", "size": 5110}, {"file": "docs/RegexLayer(1).txt", "reason": "Uncategorized - manual review needed", "size": 5391}, {"file": "docs/Round2_Progress_Documentation.md", "reason": "Uncategorized - manual review needed", "size": 8158}, {"file": "docs/Session_Continuity_Guide.md", "reason": "Uncategorized - manual review needed", "size": 7132}, {"file": "docs/Statementstoclass.txt", "reason": "Uncategorized - manual review needed", "size": 46807}, {"file": "docs/Tech Dictionary.txt", "reason": "Uncategorized - manual review needed", "size": 985678}, {"file": "models/analyze_errors.py", "reason": "Uncategorized - manual review needed", "size": 16995}, {"file": "models/deberta_classifier.py", "reason": "Uncategorized - manual review needed", "size": 3097}, {"file": "models/debug_training.py", "reason": "Uncategorized - manual review needed", "size": 2691}, {"file": "models/llm_evaluator.py", "reason": "Uncategorized - manual review needed", "size": 18779}, {"file": "models/rag_implementation.py", "reason": "Uncategorized - manual review needed", "size": 7032}, {"file": "models/simple_train.py", "reason": "Uncategorized - manual review needed", "size": 4396}, {"file": "models/statement_comparator.py", "reason": "Uncategorized - manual review needed", "size": 4309}, {"file": "models/__init__.py", "reason": "Uncategorized - manual review needed", "size": 89}, {"file": "models/round2-model/config.json", "reason": "Uncategorized - manual review needed", "size": 1465}, {"file": "models/round2-model/model.safetensors", "reason": "Uncategorized - manual review needed", "size": 438004788}, {"file": "models/round2-model/special_tokens_map.json", "reason": "Uncategorized - manual review needed", "size": 132}, {"file": "models/round2-model/tokenizer.json", "reason": "Uncategorized - manual review needed", "size": 711661}, {"file": "models/round2-model/tokenizer_config.json", "reason": "Uncategorized - manual review needed", "size": 1277}, {"file": "models/round2-model/training_args.bin", "reason": "Uncategorized - manual review needed", "size": 5240}, {"file": "models/round2-model/vocab.txt", "reason": "Uncategorized - manual review needed", "size": 231508}, {"file": "pipeline/classifier.py", "reason": "Uncategorized - manual review needed", "size": 10622}, {"file": "pipeline/__init__.py", "reason": "Uncategorized - manual review needed", "size": 46}, {"file": "rules/patterns.yml", "reason": "Uncategorized - manual review needed", "size": 26513}, {"file": "rules/rules.py", "reason": "Uncategorized - manual review needed", "size": 5208}, {"file": "rules/rule_engine.py", "reason": "Uncategorized - manual review needed", "size": 1671}, {"file": "rules/__init__.py", "reason": "Uncategorized - manual review needed", "size": 164}, {"file": "utils/ai_label_assistant.py", "reason": "Uncategorized - manual review needed", "size": 8482}, {"file": "utils/ai_response_reviewer.py", "reason": "Uncategorized - manual review needed", "size": 8925}, {"file": "utils/batch_ai_labeler.py", "reason": "Uncategorized - manual review needed", "size": 8891}, {"file": "utils/build_vector_store.py", "reason": "Uncategorized - manual review needed", "size": 1941}, {"file": "utils/check_batch_progress.py", "reason": "Uncategorized - manual review needed", "size": 3150}, {"file": "utils/clean_scraped_data.py", "reason": "Uncategorized - manual review needed", "size": 4944}, {"file": "utils/label_corrector.py", "reason": "Uncategorized - manual review needed", "size": 5915}, {"file": "utils/manual_labeling_helper.py", "reason": "Uncategorized - manual review needed", "size": 7550}, {"file": "utils/ocr-extraction.bat", "reason": "Uncategorized - manual review needed", "size": 3245}, {"file": "utils/optimized_prompt_template.py", "reason": "Uncategorized - manual review needed", "size": 3532}, {"file": "utils/patient_ai_labeler.py", "reason": "Uncategorized - manual review needed", "size": 7313}, {"file": "utils/precise_timing_test.py", "reason": "Uncategorized - manual review needed", "size": 7220}, {"file": "utils/prepare_training_data.py", "reason": "Uncategorized - manual review needed", "size": 1738}, {"file": "utils/scraper.py", "reason": "Uncategorized - manual review needed", "size": 15984}, {"file": "utils/slow_ai_labeler.py", "reason": "Uncategorized - manual review needed", "size": 7706}, {"file": "utils/statement_categorizer.py", "reason": "Uncategorized - manual review needed", "size": 20968}, {"file": "utils/test_deberta.py", "reason": "Uncategorized - manual review needed", "size": 2660}, {"file": "utils/ultra_slow_ai_test.py", "reason": "Uncategorized - manual review needed", "size": 2517}, {"file": "utils/update_session_status.py", "reason": "Uncategorized - manual review needed", "size": 6176}]}