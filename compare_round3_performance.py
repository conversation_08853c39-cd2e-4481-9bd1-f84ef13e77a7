#!/usr/bin/env python3
"""
Compare Round 3 vs Round 2 Performance
Measure accuracy improvement from corrected data
"""
import pandas as pd
import json
import os
from datetime import datetime
from truth_algorithm import TruthAlgorithm

def load_test_statements():
    """Load test statements for performance comparison"""
    print("📂 LOADING TEST STATEMENTS")
    print("="*30)
    
    # Use a subset of statements not used in training
    try:
        df = pd.read_csv("data/complete_28_category_dataset.csv")
        labeled_df = df[df['label'].notna() & (df['label'] != '')]
        
        # Use statements 50-70 as test set (not used in Round 3 training)
        test_statements = labeled_df.iloc[50:70].copy()
        
        print(f"✅ Loaded {len(test_statements)} test statements")
        return test_statements
        
    except Exception as e:
        print(f"❌ Error loading test data: {e}")
        
        # Fallback: Use simple test statements
        test_data = [
            {
                "text": "The UK government announced new trade policies.",
                "expected": ["CORRECT_SOURCE_PLUS", "ADEQUATE_DATA_PLUS"]
            },
            {
                "text": "He said it was the best decision ever made.",
                "expected": ["OMITTED_DATA_OUT"]
            },
            {
                "text": "According to NASA, the mission was successful.",
                "expected": ["CORRECT_SOURCE_PLUS", "DATA_PROVEN_FACTUAL_PLUS"]
            }
        ]
        
        return pd.DataFrame(test_data)

def test_model_performance(model_path, test_statements, model_name):
    """Test model performance on test statements"""
    print(f"\n🧪 TESTING {model_name.upper()} PERFORMANCE")
    print("="*40)
    
    try:
        # Initialize algorithm with specific model
        algorithm = TruthAlgorithm(model_path=model_path)
        
        results = []
        correct_predictions = 0
        total_predictions = 0
        
        for idx, row in test_statements.iterrows():
            text = row['text']
            
            # Get expected labels
            if 'expected' in row:
                expected = row['expected']
            else:
                expected = [label.strip() for label in row['label'].split(',')]
            
            print(f"\nTesting: {text[:60]}...")
            
            # Get prediction
            try:
                prediction = algorithm.analyze_statement(text)
                predicted_labels = prediction.get('final_classification', [])
                
                # Calculate accuracy (simple overlap)
                if predicted_labels and expected:
                    overlap = len(set(predicted_labels) & set(expected))
                    accuracy = overlap / max(len(expected), len(predicted_labels))
                else:
                    accuracy = 0.0
                
                if accuracy > 0.5:  # Consider >50% overlap as correct
                    correct_predictions += 1
                
                total_predictions += 1
                
                results.append({
                    'text': text,
                    'expected': expected,
                    'predicted': predicted_labels,
                    'accuracy': accuracy,
                    'correct': accuracy > 0.5
                })
                
                print(f"   Expected: {expected}")
                print(f"   Predicted: {predicted_labels}")
                print(f"   Accuracy: {accuracy:.1%}")
                
            except Exception as e:
                print(f"   ❌ Prediction failed: {e}")
                results.append({
                    'text': text,
                    'expected': expected,
                    'predicted': [],
                    'accuracy': 0.0,
                    'correct': False,
                    'error': str(e)
                })
                total_predictions += 1
        
        overall_accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        
        print(f"\n📊 {model_name.upper()} RESULTS:")
        print(f"   Correct predictions: {correct_predictions}/{total_predictions}")
        print(f"   Overall accuracy: {overall_accuracy:.1%}")
        
        return {
            'model_name': model_name,
            'model_path': model_path,
            'overall_accuracy': overall_accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'detailed_results': results
        }
        
    except Exception as e:
        print(f"❌ Model testing failed: {e}")
        return {
            'model_name': model_name,
            'model_path': model_path,
            'overall_accuracy': 0.0,
            'error': str(e)
        }

def compare_models():
    """Compare Round 2 vs Round 3 performance"""
    print("⚖️ COMPARING MODEL PERFORMANCE")
    print("="*40)
    
    # Load test data
    test_statements = load_test_statements()
    
    # Test Round 2 model (baseline)
    round2_results = test_model_performance(
        "models/round2-simple", 
        test_statements, 
        "round2"
    )
    
    # Test Round 3 model (corrected data)
    round3_results = test_model_performance(
        "models/round3-corrected", 
        test_statements, 
        "round3"
    )
    
    return round2_results, round3_results

def analyze_improvement(round2_results, round3_results):
    """Analyze improvement from Round 2 to Round 3"""
    print(f"\n📈 IMPROVEMENT ANALYSIS")
    print("="*30)
    
    round2_acc = round2_results['overall_accuracy']
    round3_acc = round3_results['overall_accuracy']
    
    if round2_acc > 0:
        improvement_factor = round3_acc / round2_acc
        improvement_points = (round3_acc - round2_acc) * 100
    else:
        improvement_factor = float('inf') if round3_acc > 0 else 1.0
        improvement_points = round3_acc * 100
    
    print(f"📊 ACCURACY COMPARISON:")
    print(f"   Round 2 (baseline): {round2_acc:.1%}")
    print(f"   Round 3 (corrected): {round3_acc:.1%}")
    print(f"   Improvement: +{improvement_points:.1f} percentage points")
    print(f"   Improvement factor: {improvement_factor:.1f}x")
    
    # Validate prediction
    predicted_improvement = 3.9  # Our prediction
    if improvement_factor >= predicted_improvement * 0.8:  # Within 80% of prediction
        print(f"✅ PREDICTION VALIDATED: Achieved {improvement_factor:.1f}x vs predicted {predicted_improvement}x")
    elif improvement_factor >= 2.0:  # At least 2x improvement
        print(f"✅ SIGNIFICANT IMPROVEMENT: {improvement_factor:.1f}x better than baseline")
    elif improvement_factor >= 1.5:  # At least 50% improvement
        print(f"✅ MODERATE IMPROVEMENT: {improvement_factor:.1f}x better than baseline")
    else:
        print(f"⚠️ LIMITED IMPROVEMENT: {improvement_factor:.1f}x - may need more data correction")
    
    return {
        'round2_accuracy': round2_acc,
        'round3_accuracy': round3_acc,
        'improvement_factor': improvement_factor,
        'improvement_points': improvement_points,
        'prediction_validated': improvement_factor >= predicted_improvement * 0.8
    }

def main():
    print("📊 ROUND 3 PERFORMANCE COMPARISON")
    print("="*50)
    print("Measuring accuracy improvement from corrected data")
    print()
    
    # Compare models
    round2_results, round3_results = compare_models()
    
    # Analyze improvement
    improvement = analyze_improvement(round2_results, round3_results)
    
    # Create comprehensive comparison report
    comparison_report = {
        "comparison_date": datetime.now().isoformat(),
        "methodology": "corrected_data_vs_baseline",
        "round2_results": round2_results,
        "round3_results": round3_results,
        "improvement_analysis": improvement,
        "conclusion": {
            "success": improvement['improvement_factor'] >= 2.0,
            "recommendation": "Scale to full dataset" if improvement['improvement_factor'] >= 2.0 else "Review data correction methodology"
        }
    }
    
    # Save results
    with open('round3_performance_comparison.json', 'w') as f:
        json.dump(comparison_report, f, indent=2)
    
    print(f"\n💾 RESULTS SAVED:")
    print(f"   📄 round3_performance_comparison.json")
    
    # Final recommendation
    if improvement['improvement_factor'] >= 2.0:
        print(f"\n🎉 SUCCESS! READY FOR SCALE-UP")
        print(f"   ✅ Achieved {improvement['improvement_factor']:.1f}x improvement")
        print(f"   🎯 Next step: Apply corrections to full 186-statement dataset")
        print(f"   📈 Expected final accuracy: >15%")
    else:
        print(f"\n🔧 NEEDS REFINEMENT")
        print(f"   📊 Achieved {improvement['improvement_factor']:.1f}x improvement")
        print(f"   🎯 Next step: Refine data correction methodology")
        print(f"   💡 Consider expanding corrected sample size")
    
    print(f"\n✅ Performance comparison complete!")

if __name__ == "__main__":
    main()
