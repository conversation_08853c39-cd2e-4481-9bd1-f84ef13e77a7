#!/usr/bin/env python3
"""
Correct Training Data Based on <PERSON><PERSON> Definitions
Apply strict <PERSON><PERSON> methodology to fix labeling issues
"""
import pandas as pd
import json
from base_knowledge_db import BaseKnowledgeDB

def load_lrh_strict_definitions():
    """Load strict <PERSON><PERSON> definitions for accurate labeling"""
    return {
        # OUTPOINTS - Logical errors and problems
        "OMITTED_DATA_OUT": {
            "definition": "Missing crucial information that should be present",
            "strict_criteria": [
                "Vague pronouns without clear referents (he, she, they, it)",
                "Missing context (what trade? what happened?)",
                "Incomplete quotes or statements",
                "References without explanation"
            ],
            "examples": ["sources say", "he said without identifying who"]
        },
        
        "CONTRARY_FACTS_OUT": {
            "definition": "Two contradictory facts that cannot both be true",
            "strict_criteria": [
                "Direct contradictions within same statement",
                "Contradicts established facts",
                "Mutually exclusive claims"
            ],
            "examples": ["record profits but can't pay bills"]
        },
        
        "FALSEHOOD_OUT": {
            "definition": "Demonstrably untrue statements",
            "strict_criteria": [
                "Contradicts mathematical facts",
                "Contradicts physical laws",
                "Proven false claims"
            ],
            "examples": ["2+2=5", "sky is green"]
        },
        
        # PLUSPOINTS - Logical strengths
        "DATA_PROVEN_FACTUAL_PLUS": {
            "definition": "Information that is verifiable and factual",
            "strict_criteria": [
                "Specific numbers, dates, names",
                "Verifiable claims",
                "Official sources cited",
                "Documented facts"
            ],
            "examples": ["According to NASA report", "Statistics show 23%"]
        },
        
        "CORRECT_SOURCE_PLUS": {
            "definition": "Reliable, properly attributed sources",
            "strict_criteria": [
                "Named sources (not 'sources say')",
                "Official organizations",
                "Credible attribution",
                "Specific source identification"
            ],
            "examples": ["UK government", "NASA", "Dr. Smith from Harvard"]
        },
        
        "ADEQUATE_DATA_PLUS": {
            "definition": "Sufficient information for complete understanding",
            "strict_criteria": [
                "Complete context provided",
                "All necessary details present",
                "Clear and comprehensive",
                "No missing crucial information"
            ],
            "examples": ["Complete descriptions", "Full context provided"]
        },
        
        "TIME_NOTED_PLUS": {
            "definition": "Proper time references when relevant",
            "strict_criteria": [
                "Specific dates or times",
                "Clear temporal context",
                "Appropriate time precision"
            ],
            "examples": ["March 15, 2024", "3 PM yesterday"]
        }
    }

def analyze_statement_strictly(text, current_labels, definitions, knowledge_db):
    """Analyze statement with strict L. Ron Hubbard criteria"""
    analysis = {
        "text": text,
        "current_labels": current_labels,
        "correct_labels": [],
        "incorrect_labels": [],
        "missing_labels": [],
        "reasoning": []
    }
    
    text_lower = text.lower()
    
    # Check each current label against strict criteria
    for label in current_labels:
        if label in definitions:
            defn = definitions[label]
            criteria_met = False
            
            # Check if statement meets strict criteria for this label
            for criterion in defn["strict_criteria"]:
                if any(indicator in text_lower for indicator in criterion.lower().split()):
                    criteria_met = True
                    break
            
            if criteria_met:
                analysis["correct_labels"].append(label)
                analysis["reasoning"].append(f"✅ {label}: Meets strict criteria")
            else:
                analysis["incorrect_labels"].append(label)
                analysis["reasoning"].append(f"❌ {label}: Does not meet strict criteria")
        else:
            analysis["incorrect_labels"].append(label)
            analysis["reasoning"].append(f"❌ {label}: Unknown category")
    
    # Check for missing obvious labels
    
    # OMITTED_DATA_OUT - Check for vague references
    if ("he said" in text_lower or "she said" in text_lower) and "OMITTED_DATA_OUT" not in current_labels:
        # Only if the person is not identified
        if not any(name_indicator in text_lower for name_indicator in ["mr.", "ms.", "dr.", "president"]):
            analysis["missing_labels"].append("OMITTED_DATA_OUT")
            analysis["reasoning"].append("➕ OMITTED_DATA_OUT: Vague pronoun reference")
    
    # CORRECT_SOURCE_PLUS - Check for good attribution
    if any(source in text_lower for source in ["government", "nasa", "official", "according to"]) and "CORRECT_SOURCE_PLUS" not in current_labels:
        analysis["missing_labels"].append("CORRECT_SOURCE_PLUS")
        analysis["reasoning"].append("➕ CORRECT_SOURCE_PLUS: Good source attribution")
    
    # Check base knowledge contradictions
    contradictions = knowledge_db.check_contradiction(text)
    if contradictions:
        for contradiction in contradictions:
            if contradiction["type"] not in current_labels:
                analysis["missing_labels"].append(contradiction["type"])
                analysis["reasoning"].append(f"➕ {contradiction['type']}: {contradiction['fact']}")
    
    return analysis

def correct_sample_statements():
    """Correct the sample statements from manual review"""
    print("🔧 CORRECTING TRAINING DATA WITH STRICT L. RON HUBBARD DEFINITIONS")
    print("="*70)
    
    # Load data and tools
    df = pd.read_csv("data/complete_28_category_dataset.csv")
    labeled_df = df[df['label'].notna() & (df['label'] != '')]
    
    definitions = load_lrh_strict_definitions()
    knowledge_db = BaseKnowledgeDB()
    
    # Load manual review results
    with open('manual_review_results.json', 'r') as f:
        review_results = json.load(f)
    
    corrections = []
    
    for result in review_results:
        index = result['index']
        text = result['text']
        current_labels = result['current_labels']
        
        print(f"\n📝 CORRECTING STATEMENT {index}:")
        print(f"Text: {text[:80]}...")
        print(f"Current: {', '.join(current_labels)}")
        
        # Analyze with strict criteria
        analysis = analyze_statement_strictly(text, current_labels, definitions, knowledge_db)
        
        # Determine corrected labels
        corrected_labels = analysis["correct_labels"] + analysis["missing_labels"]
        
        print(f"Corrected: {', '.join(corrected_labels) if corrected_labels else 'NEUTRAL'}")
        
        # Show reasoning
        for reason in analysis["reasoning"]:
            print(f"   {reason}")
        
        corrections.append({
            "index": index,
            "text": text,
            "original_labels": current_labels,
            "corrected_labels": corrected_labels if corrected_labels else ["NEUTRAL"],
            "analysis": analysis
        })
    
    return corrections

def create_corrected_dataset_sample(corrections):
    """Create a sample corrected dataset"""
    print(f"\n📊 CREATING CORRECTED DATASET SAMPLE")
    print("="*40)
    
    # Load original dataset
    df = pd.read_csv("data/complete_28_category_dataset.csv")
    
    # Apply corrections to sample
    corrected_count = 0
    for correction in corrections:
        index = correction["index"]
        new_labels = ", ".join(correction["corrected_labels"])
        
        # Update the dataframe
        df.loc[index, 'label'] = new_labels
        df.loc[index, 'notes'] = f"Corrected using strict L. Ron Hubbard definitions"
        corrected_count += 1
    
    # Save corrected sample
    df.to_csv("data/sample_corrected_dataset.csv", index=False)
    
    print(f"✅ Applied {corrected_count} corrections")
    print(f"💾 Saved to: data/sample_corrected_dataset.csv")
    
    # Save correction log
    with open('data_corrections_log.json', 'w') as f:
        json.dump({
            "corrections": corrections,
            "summary": {
                "total_corrected": corrected_count,
                "correction_date": pd.Timestamp.now().isoformat(),
                "methodology": "Strict L. Ron Hubbard definitions"
            }
        }, f, indent=2)
    
    print(f"📋 Correction log: data_corrections_log.json")

def main():
    print("🔧 TRAINING DATA CORRECTION - STRICT L. RON HUBBARD METHODOLOGY")
    print("="*70)
    print("Applying strict definitions to fix labeling issues identified in manual review")
    print()
    
    # Correct sample statements
    corrections = correct_sample_statements()
    
    # Create corrected dataset sample
    create_corrected_dataset_sample(corrections)
    
    # Summary
    print(f"\n🎯 CORRECTION SUMMARY:")
    
    total_changes = 0
    for correction in corrections:
        if correction["original_labels"] != correction["corrected_labels"]:
            total_changes += 1
    
    print(f"   Statements reviewed: {len(corrections)}")
    print(f"   Statements changed: {total_changes}")
    print(f"   Methodology: Strict L. Ron Hubbard definitions")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Review data/sample_corrected_dataset.csv")
    print(f"2. Apply corrections to full dataset")
    print(f"3. Train Round 3 model with corrected data")
    print(f"4. Measure accuracy improvement")
    
    print(f"\n✅ Data correction complete!")

if __name__ == "__main__":
    main()
