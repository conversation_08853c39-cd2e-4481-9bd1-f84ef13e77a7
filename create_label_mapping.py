#!/usr/bin/env python3
"""
Create label mapping for our trained model
"""
import pandas as pd
import json
from pathlib import Path

def create_label_mapping():
    """Create label mapping from our training data"""
    print("🔍 Creating label mapping from training data...")
    
    # Load the data
    df = pd.read_csv("data/statements_to_label.csv")
    
    # Extract unique labels
    unique_labels = set()
    label_col = 'label' if 'label' in df.columns else 'labels'
    
    for _, row in df.iterrows():
        if pd.notna(row[label_col]) and row[label_col] != '':
            labels = [label.strip() for label in row[label_col].split(',')]
            unique_labels.update(labels)
    
    # Sort labels for consistency
    sorted_labels = sorted(unique_labels)
    
    # Create mapping
    label_map = {label: i for i, label in enumerate(sorted_labels)}
    
    print(f"✅ Found {len(label_map)} unique labels:")
    for label in sorted_labels:
        print(f"  {label_map[label]}: {label}")
    
    # Save to model directory
    output_file = Path("models/bert-test/label_map.json")
    with open(output_file, 'w') as f:
        json.dump(label_map, f, indent=2)
    
    print(f"\n💾 Label mapping saved to: {output_file}")
    
    return label_map

if __name__ == "__main__":
    label_map = create_label_mapping()
    print(f"\n✅ Label mapping created with {len(label_map)} categories")
