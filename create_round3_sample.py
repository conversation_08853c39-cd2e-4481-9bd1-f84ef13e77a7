#!/usr/bin/env python3
"""
Create Round 3 Sample Dataset
Apply strict L<PERSON> methodology to create high-quality training sample
"""
import pandas as pd
import json
from correct_training_data import load_lrh_strict_definitions, analyze_statement_strictly
from base_knowledge_db import BaseKnowledgeDB

def select_best_statements(df, target_count=30):
    """Select the best statements for Round 3 training"""
    print(f"🔍 SELECTING BEST {target_count} STATEMENTS FOR ROUND 3")
    print("="*50)
    
    # Load tools
    definitions = load_lrh_strict_definitions()
    knowledge_db = BaseKnowledgeDB()
    
    # Analyze all labeled statements
    labeled_df = df[df['label'].notna() & (df['label'] != '')]
    statement_scores = []
    
    for idx, row in labeled_df.iterrows():
        text = row['text']
        current_labels = [label.strip() for label in row['label'].split(',')]
        
        # Analyze with strict criteria
        analysis = analyze_statement_strictly(text, current_labels, definitions, knowledge_db)
        
        # Calculate quality score
        correct_count = len(analysis['correct_labels'])
        incorrect_count = len(analysis['incorrect_labels'])
        missing_count = len(analysis['missing_labels'])
        
        # Quality score: correct labels - incorrect labels + missing labels
        quality_score = correct_count - incorrect_count + missing_count
        
        statement_scores.append({
            'index': idx,
            'text': text,
            'original_labels': current_labels,
            'analysis': analysis,
            'quality_score': quality_score,
            'correct_labels': analysis['correct_labels'],
            'missing_labels': analysis['missing_labels']
        })
    
    # Sort by quality score (highest first)
    statement_scores.sort(key=lambda x: x['quality_score'], reverse=True)
    
    print(f"📊 QUALITY ANALYSIS:")
    print(f"   Total statements analyzed: {len(statement_scores)}")
    print(f"   Quality scores range: {statement_scores[-1]['quality_score']} to {statement_scores[0]['quality_score']}")
    
    # Select top statements
    selected = statement_scores[:target_count]
    
    print(f"\n✅ SELECTED TOP {len(selected)} STATEMENTS:")
    for i, stmt in enumerate(selected[:5]):  # Show top 5
        print(f"   {i+1}. Score: {stmt['quality_score']} - {stmt['text'][:60]}...")
    
    return selected

def create_corrected_dataset(selected_statements):
    """Create corrected dataset with high-quality labels"""
    print(f"\n🔧 CREATING CORRECTED DATASET")
    print("="*35)
    
    corrected_data = []
    
    for stmt in selected_statements:
        # Use corrected labels (correct + missing)
        corrected_labels = stmt['correct_labels'] + stmt['missing_labels']
        
        # If no labels, mark as NEUTRAL
        if not corrected_labels:
            corrected_labels = ['NEUTRAL']
        
        corrected_data.append({
            'text': stmt['text'],
            'label': ', '.join(corrected_labels),
            'original_labels': ', '.join(stmt['original_labels']),
            'quality_score': stmt['quality_score'],
            'correction_applied': 'strict_lrh_methodology'
        })
    
    # Create DataFrame
    corrected_df = pd.DataFrame(corrected_data)
    
    print(f"✅ Created corrected dataset:")
    print(f"   Statements: {len(corrected_df)}")
    print(f"   Average quality score: {corrected_df['quality_score'].mean():.1f}")
    
    # Show label distribution
    all_labels = []
    for labels_str in corrected_df['label']:
        all_labels.extend([label.strip() for label in labels_str.split(',')])
    
    label_counts = pd.Series(all_labels).value_counts()
    print(f"\n📊 LABEL DISTRIBUTION:")
    for label, count in label_counts.head(10).items():
        print(f"   {label}: {count}")
    
    return corrected_df

def main():
    print("🚀 CREATING ROUND 3 SAMPLE DATASET")
    print("="*50)
    print("Applying strict L. Ron Hubbard methodology for high-quality training data")
    print()
    
    # Load original dataset
    print("📂 Loading original dataset...")
    df = pd.read_csv("data/complete_28_category_dataset.csv")
    print(f"   Total statements: {len(df)}")
    print(f"   Labeled statements: {len(df[df['label'].notna() & (df['label'] != '')])}")
    
    # Select best statements
    selected = select_best_statements(df, target_count=30)
    
    # Create corrected dataset
    corrected_df = create_corrected_dataset(selected)
    
    # Save corrected dataset
    output_path = "data/round3_sample_dataset.csv"
    corrected_df.to_csv(output_path, index=False)
    
    print(f"\n💾 DATASET SAVED:")
    print(f"   File: {output_path}")
    print(f"   Statements: {len(corrected_df)}")
    print(f"   Quality: High (strict L. Ron Hubbard criteria)")
    
    # Save selection log
    selection_log = {
        "creation_date": pd.Timestamp.now().isoformat(),
        "methodology": "strict_lrh_criteria",
        "source_dataset": "data/complete_28_category_dataset.csv",
        "statements_selected": len(selected),
        "selection_criteria": "highest_quality_score",
        "quality_scores": [stmt['quality_score'] for stmt in selected],
        "average_quality": sum(stmt['quality_score'] for stmt in selected) / len(selected)
    }
    
    with open('round3_sample_creation_log.json', 'w') as f:
        json.dump(selection_log, f, indent=2)
    
    print(f"📋 Selection log: round3_sample_creation_log.json")
    
    print(f"\n🎯 NEXT STEP:")
    print(f"   Execute: python train_round3_corrected.py")
    print(f"   Goal: Train model with high-quality corrected data")
    print(f"   Expected: Significant accuracy improvement over 3.4% baseline")
    
    print(f"\n✅ Round 3 sample dataset creation complete!")

if __name__ == "__main__":
    main()
