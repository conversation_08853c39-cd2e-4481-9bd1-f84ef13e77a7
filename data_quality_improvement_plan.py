#!/usr/bin/env python3
"""
Data Quality Improvement Plan
Based on manual review and strict L. Ron <PERSON> methodology
"""
import pandas as pd
import json
from datetime import datetime

def analyze_correction_patterns():
    """Analyze patterns in the corrections to identify systematic issues"""
    print("📊 ANALYZING CORRECTION PATTERNS")
    print("="*40)
    
    with open('data_corrections_log.json', 'r') as f:
        corrections_data = json.load(f)
    
    corrections = corrections_data['corrections']
    
    # Analyze correction patterns
    patterns = {
        'over_labeling': 0,  # Too many labels applied
        'under_labeling': 0,  # Missing obvious labels
        'wrong_labels': 0,   # Incorrect labels applied
        'correct_labels': 0   # Labels that were correct
    }
    
    common_issues = {
        'duplicate_labels': 0,
        'unknown_categories': 0,
        'strict_criteria_failures': 0,
        'missing_obvious_outpoints': 0
    }
    
    for correction in corrections:
        original = correction['original_labels']
        corrected = correction['corrected_labels']
        analysis = correction['analysis']
        
        # Count patterns
        if len(original) > len(corrected):
            patterns['over_labeling'] += 1
        elif len(original) < len(corrected):
            patterns['under_labeling'] += 1
        
        patterns['wrong_labels'] += len(analysis['incorrect_labels'])
        patterns['correct_labels'] += len(analysis['correct_labels'])
        
        # Check for specific issues
        if len(set(original)) < len(original):
            common_issues['duplicate_labels'] += 1
        
        for reason in analysis['reasoning']:
            if 'Unknown category' in reason:
                common_issues['unknown_categories'] += 1
            elif 'Does not meet strict criteria' in reason:
                common_issues['strict_criteria_failures'] += 1
    
    print(f"📋 CORRECTION PATTERNS:")
    for pattern, count in patterns.items():
        print(f"   {pattern.replace('_', ' ').title()}: {count}")
    
    print(f"\n🚨 COMMON ISSUES:")
    for issue, count in common_issues.items():
        print(f"   {issue.replace('_', ' ').title()}: {count}")
    
    return patterns, common_issues

def create_systematic_correction_strategy():
    """Create strategy for systematic data correction"""
    print(f"\n🎯 SYSTEMATIC CORRECTION STRATEGY")
    print("="*40)
    
    strategy = {
        "phase_1_immediate": {
            "description": "Fix obvious errors in sample data",
            "actions": [
                "Remove duplicate labels (e.g., DATA_PROVEN_FACTUAL_PLUS appearing twice)",
                "Apply strict L. Ron Hubbard criteria to all labels",
                "Remove labels that don't meet strict criteria",
                "Add missing obvious outpoints/pluspoints"
            ],
            "target": "Sample of 20-30 statements for immediate Round 3 training"
        },
        
        "phase_2_expansion": {
            "description": "Expand corrected dataset systematically",
            "actions": [
                "Apply correction methodology to full 186-statement dataset",
                "Use base knowledge database for contradiction detection",
                "Implement strict criteria checking for all categories",
                "Cross-reference statements for consistency"
            ],
            "target": "Complete 186-statement dataset with high quality labels"
        },
        
        "phase_3_validation": {
            "description": "Validate and optimize corrected dataset",
            "actions": [
                "Manual spot-check of corrected labels",
                "Ensure balanced representation across all 28 categories",
                "Add high-quality examples for weak categories",
                "Final validation against L. Ron Hubbard source materials"
            ],
            "target": "Production-ready dataset for final model training"
        }
    }
    
    for phase, details in strategy.items():
        print(f"\n{phase.upper().replace('_', ' ')}:")
        print(f"   Description: {details['description']}")
        print(f"   Target: {details['target']}")
        print(f"   Actions:")
        for action in details['actions']:
            print(f"      - {action}")
    
    return strategy

def estimate_improvement_potential():
    """Estimate potential accuracy improvement from data corrections"""
    print(f"\n📈 IMPROVEMENT POTENTIAL ANALYSIS")
    print("="*40)
    
    # Current baseline
    current_accuracy = 0.034  # 3.4% from Round 2
    current_issues = {
        'over_labeling': 3,  # From our sample analysis
        'wrong_labels': 6,   # Labels that don't meet strict criteria
        'missing_labels': 2   # Obvious labels that were missed
    }
    
    # Calculate issue rate
    sample_size = 5
    total_issues = sum(current_issues.values())
    issue_rate = total_issues / sample_size
    
    print(f"📊 CURRENT STATE:")
    print(f"   Baseline accuracy: {current_accuracy*100:.1f}%")
    print(f"   Sample issue rate: {issue_rate*100:.1f}% ({total_issues}/{sample_size} statements)")
    
    # Estimate improvement potential
    if issue_rate > 0.5:  # More than 50% of statements have issues
        potential_improvement = 10  # Could improve to 13-15%
    elif issue_rate > 0.3:  # 30-50% have issues
        potential_improvement = 5   # Could improve to 8-10%
    else:
        potential_improvement = 2   # Could improve to 5-6%
    
    estimated_new_accuracy = current_accuracy + (potential_improvement / 100)
    
    print(f"\n🎯 IMPROVEMENT POTENTIAL:")
    print(f"   Estimated new accuracy: {estimated_new_accuracy*100:.1f}%")
    print(f"   Potential improvement: +{potential_improvement}% points")
    print(f"   Improvement factor: {estimated_new_accuracy/current_accuracy:.1f}x")
    
    # Factors for improvement
    print(f"\n🔧 IMPROVEMENT FACTORS:")
    print(f"   ✅ Strict L. Ron Hubbard criteria application")
    print(f"   ✅ Removal of duplicate and incorrect labels")
    print(f"   ✅ Addition of missing obvious outpoints/pluspoints")
    print(f"   ✅ Base knowledge contradiction detection")
    print(f"   ✅ Consistent methodology across all statements")
    
    return estimated_new_accuracy

def create_immediate_action_plan():
    """Create immediate action plan for Round 3 preparation"""
    print(f"\n🚀 IMMEDIATE ACTION PLAN - ROUND 3 PREPARATION")
    print("="*50)
    
    action_plan = {
        "step_1": {
            "task": "Create high-quality sample dataset",
            "description": "Apply strict corrections to 30 best statements",
            "time_estimate": "30 minutes",
            "output": "data/round3_sample_dataset.csv"
        },
        "step_2": {
            "task": "Train Round 3 model with corrected sample",
            "description": "Use corrected sample to train improved model",
            "time_estimate": "15 minutes",
            "output": "models/round3-corrected/"
        },
        "step_3": {
            "task": "Measure accuracy improvement",
            "description": "Compare Round 3 vs Round 2 performance",
            "time_estimate": "10 minutes",
            "output": "round3_performance_comparison.json"
        },
        "step_4": {
            "task": "Scale to full dataset if successful",
            "description": "Apply corrections to all 186 statements",
            "time_estimate": "60 minutes",
            "output": "data/complete_corrected_dataset.csv"
        }
    }
    
    for step, details in action_plan.items():
        print(f"\n{step.upper()}:")
        print(f"   Task: {details['task']}")
        print(f"   Description: {details['description']}")
        print(f"   Time: {details['time_estimate']}")
        print(f"   Output: {details['output']}")
    
    return action_plan

def main():
    print("📋 DATA QUALITY IMPROVEMENT PLAN")
    print("="*50)
    print("Based on manual review and L. Ron Hubbard strict methodology")
    print()
    
    # Analyze correction patterns
    patterns, issues = analyze_correction_patterns()
    
    # Create systematic strategy
    strategy = create_systematic_correction_strategy()
    
    # Estimate improvement potential
    estimated_accuracy = estimate_improvement_potential()
    
    # Create immediate action plan
    action_plan = create_immediate_action_plan()
    
    # Save comprehensive plan
    improvement_plan = {
        "analysis_date": datetime.now().isoformat(),
        "correction_patterns": patterns,
        "common_issues": issues,
        "systematic_strategy": strategy,
        "estimated_improvement": {
            "current_accuracy": 0.034,
            "estimated_new_accuracy": estimated_accuracy,
            "improvement_factor": estimated_accuracy / 0.034
        },
        "immediate_action_plan": action_plan
    }
    
    with open('data_quality_improvement_plan.json', 'w') as f:
        json.dump(improvement_plan, f, indent=2)
    
    print(f"\n💾 PLAN SAVED:")
    print(f"   📄 data_quality_improvement_plan.json")
    
    print(f"\n🎯 NEXT IMMEDIATE ACTION:")
    print(f"   Execute Step 1: Create high-quality sample dataset")
    print(f"   Goal: Improve from 3.4% to ~13-15% accuracy")
    print(f"   Method: Strict L. Ron Hubbard methodology")
    
    print(f"\n✅ Data quality improvement plan complete!")

if __name__ == "__main__":
    main()
