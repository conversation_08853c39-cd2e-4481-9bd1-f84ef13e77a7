# Truth Algorithm - Conceptual Framework

## Core Principles
The Truth Algorithm evaluates statements based on logical consistency, factual accuracy, and source credibility. It identifies both negative indicators (outpoints) and positive indicators (pluspoints) to determine the reliability of information.

## Process Flow
1. Statement Collection
   - Gather statements with metadata (source, time, context)
   - Normalize and preprocess text

2. Individual Statement Analysis
   - Apply outpoint rules to detect logical inconsistencies
   - Apply pluspoint rules to identify positive indicators
   - Calculate preliminary truth score
   - Use RAG system to retrieve relevant context from knowledge base
   - Apply LLM-based evaluation with confidence scoring

3. Multi-Layer Classification Approach
   - Layer 1: Regex/heuristic rules for high-precision pattern matching (WORKING)
   - Layer 2: DeBERTa classifier for efficient semantic understanding (PLACEHOLDER)
   - Layer 3: LLM-based evaluation with RAG for complex cases (WORKING)
   - Confidence thresholds determine which layer handles evaluation
   - Automatic fallback between layers based on confidence scores

4. Relational Analysis
   - Build knowledge graph of statements (WORKING)
   - Identify contradictions between statements (WORKING)
   - Automatically assign contrary_facts outpoint to contradicting statements (WORKING)
   - Detect supporting evidence relationships (BASIC)
   - Cluster related problematic statements (BASIC)

5. Classification and Reporting
   - Traffic light system (red/yellow/green) for statement reliability
   - Provide evidence and reasoning for classifications
   - Generate actionable insights and follow-up questions
   - Confidence scoring for all evaluations

## Outpoints (Negative Indicators)
1. Omitted Data - Missing crucial information
2. Altered Sequence - Events out of logical order
3. Dropped Time - Missing timestamps when others have them
4. Falsehood - Demonstrably untrue statements
5. Altered Importance - Exaggeration or minimization
6. Wrong Target - Misplaced blame or focus
7. Wrong Source - Unreliable information origin
8. Contrary Facts - Direct contradictions
9. Added Time - Unnecessary or incorrect time data
10. Added Inapplicable Data - Irrelevant information
11. Incorrectly Included Datum - Data that doesn't belong
12. Assumed Identities Not Identical - False equivalences
13. Assumed Similarities Not Similar - False analogies
14. Assumed Differences Not Different - False distinctions

## Pluspoints (Positive Indicators)
1. Related Facts Known - Supporting evidence exists
2. Events in Correct Sequence - Logical order of events
3. Time Noted - Proper timestamps provided
4. Data Proven Factual - Verified by reliable sources
5. Correct Relative Importance - Proper emphasis
6. Expected Time Period - Appropriate timeframes
7. Adequate Data - Sufficient information provided
8. Applicable Data - Relevant information
9. Correct Source - Reliable information origin
10. Correct Target - Proper attribution
11. Data in Same Classification - Consistent categorization
12. Identities Are Identical - Proper equivalences
13. Similarities Are Similar - Valid comparisons
14. Differences Are Different - Valid distinctions

## Implementation Details
1. LLM Integration
   - Custom-trained "truth-evaluator" model based on Investigations methodology
   - Prompt engineering for each outpoint and pluspoint rule
   - Confidence scoring for evaluation reliability
   - Batch processing for efficient evaluation

2. RAG System
   - Vector database of Investigations knowledge
   - Context retrieval for each evaluation
   - Dynamic prompt enhancement with relevant information
   - Citation of knowledge sources in reasoning

3. Caching System
   - Persistent storage of evaluation results
   - Reduced API calls for repeated evaluations
   - Performance optimization

4. Evaluation Metrics
   - Outpoint and pluspoint detection rates
   - Confidence scoring distribution
   - Context relevance measurement
   - Inter-layer agreement analysis

5. Classification Pipeline
   - Orchestrates all evaluation layers
   - Applies confidence thresholds
   - Combines results from multiple layers
   - Generates final classification with explanation

## Recent Improvements (January 3, 2025)
1. Fixed pipeline integration issues - all layers now work together properly
2. Enhanced contradiction detection with automatic outpoint assignment
3. Improved pattern detection for unsubstantiated claims and vague sources
4. Fixed rule-based analysis to use actual regex engine instead of placeholders
5. Added comprehensive patterns for "everyone knows" type claims
6. Enhanced contradictory term detection for financial/business contexts

## Current System Status
- **Regex Layer**: Fully functional with comprehensive patterns
- **LLM Layer**: Fully functional with RAG integration and caching
- **Truth Graph**: Functional contradiction detection and relationship mapping
- **Pipeline Integration**: Working correctly with proper fallback mechanisms
- **Overall**: Production-ready for rule-based and LLM analysis

## Future Directions
1. Complete DeBERTa classifier implementation with actual model training
2. Multi-modal analysis capabilities
3. Domain-specific knowledge bases and rule sets
4. Collaborative verification mechanisms
5. Enhanced explanation generation
6. Automated follow-up question generation
7. Performance optimization for large datasets
8. Evaluation dashboard for result visualization

