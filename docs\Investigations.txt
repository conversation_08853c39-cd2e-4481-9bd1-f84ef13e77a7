SCIENTOLOGY
 Making the World a Better Place
 Founded and developed by <PERSON><PERSON>, Scientology is an applied
 religious philosophy which offers an exact route through which anyone can
 regain the truth and simplicity of his spiritual self.
 Scientology consists of specific axioms that define the underlying causes and
 principles of existence and a vast area of observations in the humanities, a
 philosophic body that literally applies to the entirety of life.
 This broad body of knowledge resulted in two applications of the subject:
 first, a technology for man to increase his spiritual awareness and attain the
 freedom sought by many great philosophic teachings; and, second, a great
 number of fundamental principles men can use to improve their lives. In fact, in
 this second application, Scientology offers nothing less than practical methods
 to better every aspect of our existence—means to create new ways of life. And
 from this comes the subject matter you are about to read.
 Compiled from the writings of <PERSON><PERSON>, the data presented here is
 but one of the tools which can be found in The Scientology Handbook. A
 comprehensive guide, the handbook contains numerous applications of
 Scientology which can be used to improve many other areas of life.
 In this booklet, the editors have augmented the data with a short
 introduction, practical exercises and examples of successful application.
 Courses to increase your understanding and further materials to broaden
 your knowledge are available at your nearest Scientology church or mission
 Many new phenomena about man and life are described in Scientology, and
 so you may encounter terms in these pages you are not familiar with. These are
 described the first time they appear and in the glossary at the back of the booklet.
 Scientology is for use. It is a practical philosophy, something one does. Using
 this data, you can change conditions.
 Millions of people who want to do something about the conditions they see
 around them have applied this knowledge. They know that life can be improved.
 And they know that Scientology works.
 Use what you read in these pages to help yourself and others and you will
 too.
 CHURCH OF SCIENTOLOGY INTERNATIONAL
 .
 Listings are available at www.scientology.org.
Many people go through life in a rather hit-or-miss fashion, casting
 about for ideas to explain why their projects improve or decline, why they
 are successful or why they are not. Guessing and “hunches,” however, are
 not very reliable. And without the knowledge of how to actually
 investigate situations, good or bad, and get the true facts, a person is set
 adrift in a sea of unevaluated data.
 Accurate investigation is, in fact, a rare commodity. Man’s tendency in
 matters he doesn’t understand is to accept the first proffered explanation,
 no matter how faulty. Thus investigatory technology had not actually
 been practiced or refined. However, L. Ron Hubbard made a breakthrough
 in the subject of logic and reasoning which led to his development of the
 first truly effective way to search for and consistently find the actual
 causes for things.
 Knowing how to investigate gives one the power to navigate through
 the random facts and opinions and emerge with the real reasons behind
 success or failure in any aspect of life. By really finding out why things
 are the way they are, one is therefore able to remedy and improve a
 situation—any situation. This is an invaluable technology for people in
 all walks of life.■
 2
3
 rom day to day and week to week, one can face many
 less-than-desirable circumstances in his life. Somehow
 one manages to slog through these situations, convinced
 there is not much he can do to improve his lot. Perhaps a
 project planned for months at work doesn’t come off
 with the expected success; productivity in the office has
 declined sharply during the past quarter; or the addition
 to one’s house takes longer than first envisioned. Such situations are
 common enough occurrences for many of us.
 But these need not be the usual state of affairs. People can live a happy
 existence and accomplish their goals in any area of life—individually, with the
 family, the job and so on. The aims an individual once visualized for himself
 can be accomplished.
 If such goals are not being attained or if one is in a situation that has
 deteriorated or worsened, there is a valid, locatable cause for this. This
 concept is one people often do not realize—things are actually caused. They
 don’t just happen. There are reasons behind every situation—reasons that
 people themselves can control.
 Without knowing this, man often relies upon “fate,” superstition,
 fortunetelling or astrology to determine his destiny or future. Many just hope
 vainly that nothing else will go wrong or they deceive themselves with the
 belief that life is ordinarily a struggle.
 INVESTIGATION 
AND ITS USE
For example, a farmer with a very poor crop one year has no credible
 explanation for it. He has no concept that he himself caused this condition.
 However, looking into it, one would find that he had earlier failed to keep seed
 grain secure for the spring planting, and thus it fell prey to insects. Not
 knowing this, he might come up with all sorts of odd “reasons” or just blame
 it on bad luck.
 In a factory with low production, management could be shifting
 personnel, hiring new workers, etc., in an attempt to raise productivity before
 the organization goes under. But executives might not have the skills needed
 to really examine the company’s own operations to find the cause of the
 situation. Upon inspection, one could discover that the suppliers of its raw
 materials refused to deliver because the company’s accounting office wasn’t
 paying the bills.
 To look into, handle and improve any such situation in any area of life
 requires skill in investigation—the ability to think logically and get to the
 bottom of things.
 Investigation is the careful discovery and sorting of facts. In investigating,
 one is searching out and examining the particulars of something in an attempt
 to learn the facts, especially in an attempt to find a cause.
 A proper investigation gets to the bottom of the state of affairs facing one.
 For instance, in any organization, one could observe that its production was
 down. This is a nonoptimum situation which should be investigated and the
 cause located. Investigations can also be utilized in an individual’s personal
 life to improve conditions.
 In doing an investigation, you are asking the question, “What don’t I
 understand?” with regard to the existing conditions. You’ll find that two facts
 don’t agree—they contradict themselves and can’t be understood. So you try
 to rationalize these two facts: you question these two facts and you will get
 another point you don’t understand. And when you try to get this point
 understood, you will now find another fact that you don’t understand. And
 someplace along the way, you will find the reason for the circumstances you
 are investigating.
 Any investigation should proceed along these lines. Sometimes many
 questions have to be asked, sometimes it only takes a “What’s that noise?” to
 4
lead one to the source of a difficulty. Here is an example of an investigation
 done on a rapid, emergency basis: An engineer is on duty in a ship’s engine
 room. He has normal but experienced perception: is observing his area. Hears
 a hiss that shouldn’t be—something contradictory to the expected conditions
 in an engine room. Scans the area and sees nothing out of order but a small
 white cloud. Combines sight and hearing. Moves forward to get a better look.
 Sees valve has broken. Shuts off steam line.
 In a nutshell, (a) one finds an imperfect functioning of some portion of an
 organization or whatever he is investigating and then (b) finds something that
 one doesn’t understand about it and then (c) questions the individuals in that
 portion connected with the imperfect functioning or looks into the area to get
 more data.
 Following this sequence isolates the cause of the trouble which can then
 be handled so the area properly operates again. In an organization, one can
 apply just these three steps over and over again, and it will usually be quite
 enough to keep it running quite smoothly.
 Statistics play a role in investigations. A statistic shows the production of
 an activity, area or organization, as compared to an earlier moment in time. It
 reflects whether or not the area is achieving its purpose—if statistics are up, it
 is more closely accomplishing what is intended for the area. In doing an
 investigation, one looks for down statistics. These aren’t understandable, of
 course, so one questions the people concerned. In their answers there will be
 something that doesn’t make sense at all to the person doing the
 investigation—for example, “We can’t pay the bills because Josie has been
 doing a course.” The investigator is only looking for something he himself
 can’t reconcile. So he questions the person who gave this data and Josie.
 Sooner or later the real reason shows up.
 As one is going down the trail of things he can’t understand, one of two
 things will happen. Either it is a dead end and it doesn’t go any further, at
 which time he returns to the main line of the investigation, or it produces
 further material. And if it produces further material, one will find more things
 he can’t understand.
 The trick of this procedure is to find a piece of string sticking out—
 something one can’t understand and, by questioning, pull on it. A small cat
 5
6
 In encountering 
two facts that 
contradict, one 
questions on 
these two facts 
and gets another 
point he doesn’t 
understand. He 
continues on this 
path of things he 
doesn’t get until 
the real reason is 
located.
 FACT FACT
 CONTRADICTION
 QUESTION
 FACT 
NOT UNDERSTOOD
 QUESTION
 FACT 
NOT UNDERSTOOD
 QUESTION
 FACT 
NOT UNDERSTOOD
 QUESTION
 REAL REASON
shows up. Pull on the string by asking more questions. A baby gorilla shows
 up. Pull some more. A tiger appears. Pull again and wow! You’ve got a General
 Sherman tank!
 It isn’t reasonable for people to be lazy or stupid. At the bottom you find
 the real cause of no action in a portion of an organization or continuous upset.
 When you have your “General Sherman tank,” you can take action.
 There’s always a reason behind a bad statistic. Question those involved
 until you have the real reason in view. It will never be “Agnes isn’t bright.” It
 is more likely, Agnes was hired as a typist but never knew how to type. Or the
 executive over the area simply never comes to work.
 The real explanation of a down statistic is always a very easily understood
 thing. If you question enough, you’ll get the real explanation and then you
 can act.
 This technique of investigation, while elementary, is highly effective. It
 can be applied when faced with simple or complex situations to get to the
 bottom of them, and therefore enables one to resolve them and improve
 conditions in life.
 Investigatory skills improve with practice. They can be sharpened and
 made more effective so that one is able to instantly spot something he doesn’t
 understand. This ability is not innate in people but can be easily acquired. To
 make investigations even more rapid and effective, one should be able to
 understand and apply the principles of logic—a subject that until now has not
 only been misunderstood but has been made unnecessarily complex.
 7
8
 LOGIC
 The subject of logic has been under discussion for at least three
 thousand years without any clean breakthrough of real use to those who
 work with data.
 “Logic” means the subject of reasoning. Some in ages past have sought to
 label it a science. But that can be discarded as pretense and pompousness.
 If there were such a “science,” men would be able to think. And they can’t.
 The term itself is utterly forbidding. If you were to read a text on logic, you
 would go quite mad trying to figure it out, much less learn how to think.
 Yet logic or the ability to reason is vital to an organizer or administrator.
 If he cannot think clearly, he will not be able to reach the conclusions vital to
 make correct decisions.
 Many agencies, governments, societies, groups capitalize upon this lack of
 logic and have for a very long time. A population that is unable to think or
 reason can be manipulated easily by falsehoods and wretched causes.
 Thus logic has not been a supported subject, rather the opposite.
 Even Western schools have sought to convince students they should study
 geometry as “that is the way they think.” And of course it isn’t.
 The administrator, the manager, the artisan and the clerk each have a
 considerable use for logic. If they cannot reason, they make costly and
time-consuming errors and can send the entire organization into chaos and
 oblivion.
 Their stuff in trade are data and situations. Unless they can observe and
 think their way through, they can reach wrong conclusions and take incorrect
 actions.
 Modern man thinks mathematics can serve him for logic and most of his
 situations go utterly adrift because of this touching and misplaced confidence.
 The complexity of human problems and the vast number of factors involved
 make mathematics utterly inadequate.
 Computers are at best only crutches to the mind. Yet the chromium-plated
 civilization today has a childish faith in them. It depends on who asks the
 questions and who reads the computer’s answers whether they are of any use
 or not. And even then their answers are often madhouse silly.
 Computers can’t think because the rules of live logic aren’t fully known to
 man and computer builders. One false datum fed into a computer gives one a
 completely wrong answer.
 If people on management and work lines do not know logic, an
 organization can go adrift and require a fabulous amount of genius to hold it
 together and keep it running.
 Whole civilizations vanish because of lack of logic in its rulers, leaders and
 people.
 So this is a very important subject.
 9
33     USA
 Birds of flight
 Unlocking Logic
 Scientology contains a way to unlock logic. This is a breakthrough which
 is no small win. If by it a formidable and almost impossible subject can be
 reduced to simplicity, then correct answers to situations can be far more
 frequent and an organization or a civilization far more effective.
 The breakthrough is a simple one:
 BY ESTABLISHING THE WAYS IN WHICH THINGS BECOME
 ILLOGICAL, ONE CAN THEN ESTABLISH WHAT IS LOGIC.
 In other words, if one has a grasp of what makes things illogical or irrational
 (or crazy, if you please) it is then possible to conceive of what makes things
 logical.
 Illogics
 There are specific ways for a relay of information or a situation to become
 illogical. These are the things which cause one to have an incorrect idea of a
 situation. Each different way is called an outpoint, which is any one datum that
 is offered as true that is in fact found to be illogical. Each one of these is
 described below.
 Omitted Data
 An omitted anything is an outpoint.
 This can be an omitted person, terminal (person who sends, receives and
 relays communication), object, energy, space, time, form, sequence or even an
 omitted scene. Anything that can be omitted that should be there is an outpoint.
 This is easily the most overlooked outpoint as it isn’t there to directly
 attract attention.
 OMITTED DATA
 Joe  Jones
 New York, N.Y.
 10
Altered Sequence
 Any things, events, objects, sizes, in a wrong sequence is an outpoint.
 The number series 3, 7, 1, 2, 4, 6, 5 is an altered sequence, or an incorrect
 sequence.
 Doing step two of a sequence of actions before doing step one can be
 counted on to tangle any sequence of actions.
 The basic outness is no sequence at all. (An outness is a condition or state
 of something being incorrect, wrong or missing.) This leads into FIXED
 IDEAS. It also shows up in what is called disassociation, an insanity. Things
 connected to or similar to each other are not seen as consecutive. Such people
 also jump about subjectwise without relation to an obvious sequence.
 Disassociation is the extreme case where things that are related are not seen
 to be and things that have no relation are conceived to have.
 “Sequence” means linear (in a line) travel either through space or time or
 both.
 A sequence that should be one and isn’t is an outpoint.
 A “sequence” that isn’t but is thought to be one is an outpoint.
 A cart-before-the-horse out of sequence is an outpoint.
 ALTERED SEQUENCE
 11
One’s hardest task sometimes is indicating an inevitable sequence into the
 future that is invisible to another. This is a consequence. “If you saw off the
 limb you are sitting on you will of course fall.” Police try to bring this home
 often to people who have no concept of sequence; so the threat of punishment
 works well on well-behaved citizens and not at all on criminals since they
 often are criminals because they can’t think in sequence—they are simply
 fixated. “If you kill a man you will be hanged,” is an indicated sequence. A
 murderer fixated on revenge cannot think in sequence. One has to think in
 sequences to have correct sequences.
 Therefore, it is far more common than one would at first imagine to see altered
 sequences since persons who do not think in sequence do not see altered sequences
 in their own actions or areas.
 Visualizing sequences and drills in shifting attention can clean this up and
 restore it as a faculty.
 Motion pictures and TV were spotted by a writer as fixating attention and
 not permitting it to travel. Where one had TV-raised children, it would follow,
 one possibly would have people with a tendency to altered sequences or no
 sequences at all.
 Dropped Time
 Time that should be noted and isn’t would be an outpoint of “dropped
 time.” It is a special case of an omitted datum.
 Dropped time has a peculiarly ferocious effect that adds up to utter lunacy.
 DROPPED TIME Mr. and Mrs.
 Bernstein request 
your presence at the 
wedding of their 
daughter Susan to 
Mr. Richard Ellis 
at Townville Church.
 12
A news bulletin from 1814 and one from 1922 read consecutively without
 time assigned produces otherwise undetectable madness.
 A summary report of a situation containing events strung over half a year
 without saying so can provoke a reaction not in keeping with the current
 scene.
 In madmen the present is the dropped time, leaving them in the haunted
 past. Just telling a group of madmen to “come up to present time” will
 produce a few miraculous “cures.” And getting the date of an ache or pain will
 often cause it to vanish.
 Time aberrations (illogicalities) are so strong that dropped time well
 qualifies as an outpoint.
 Falsehood
 When you hear two facts that are contrary, one is a falsehood or both are.
 Propaganda and other activities specialize in falsehoods and provoke great
 disturbance.
 Willful or unintentional, a falsehood is an outpoint. It may be a mistake or
 a calculated or defensive falsehood and it is still an outpoint.
 A false anything qualifies for this outpoint. A false being, terminal, act,
 intention, anything that seeks to be what it isn’t is a falsehood and an
 outpoint.
 Fiction that does not pretend to be anything else is of course not a
 falsehood.
 Jumbo 
Burger
 FALSEHOOD
 13
So the falsehood means “other than it appears” or “other than
 represented.”
 One does not have to concern oneself to define philosophic truth or reality
 to see that something stated or modeled to be one thing is in actual fact
 something else and therefore an outpoint.
 Altered Importance
 An importance shifted from its actual relative importance, up or down, is
 an outpoint.
 Something can be assigned an importance greater than it has.
 Something can be assigned an importance less than it has.
 ALTERED IMPORTANCE
 A number of things of different importances can be assigned a monotone
 of importance.
 These are all outpoints, three versions of the same thing.
 All importances are relative to their actuality.
 Wrong Target
 A mistaken objective wherein one believes he is or should be reaching
 toward A and finds he is or should be reaching toward B is an outpoint.
 This is commonly mistaken identity. It is also mistaken purposes or goals.
 “If we tear down X we will be okay” often results in disclosure that it
 should have been Y.
 14
WRONG TARGET
 Killing the king to be free from taxation leaves the tax collector alive for
 the next regime.
 Injustice is usually a wrong target outpoint.
 Arrest the drug consumer, award the drug company would be an example.
 Military tactics and strategy are almost always an effort to coax the
 selection of a wrong target by the enemy.
 And most dislikes and spontaneous hates in human relations are based on
 mistaken associations of Bill for Pete.
 A large sum of aberration is based on wrong targets, wrong sources, wrong
 causes.
 Incorrectly tell a patient he has ulcers when he hasn’t and he’s hung with
 an outpoint which impedes recovery.
 The industry spent on wrong objectives would light the world for a
 millennium.
 Wrong Source
 “Wrong source” is the other side of the coin of wrong target.
 Information taken from wrong source, orders taken from the wrong
 source, gifts or materiel (supplies) taken from wrong source all add up to
 eventual confusion and possible trouble.
 15
WRONG SOURCE
 LARRY’S BARBER
 I can get you a 
good deal on this 
life insurance 
policy…
 Unwittingly receiving from a wrong source can be very embarrassing or
 confusing, so much so that it is a favorite intelligence trick. Department D in
 East Germany, the Department of Disinformation, had very intricate methods
 of planting false information and disguising its source.
 Technology can come from wrong source. For instance, Leipzig
 University’s school of psychology and psychiatry opened the door to death
 camps in Hitler’s Germany. Using drugs, these men apparently gave Hitler to
 the world as their puppet. At the end of World War II these extremists formed
 the “World Federation of Mental Health,” which enlisted the American
 Psychiatric Association and the American Medical Association and
 established “National Associations for Mental Health” over the world. These
 became the sole advisors to the US government on “mental health, education
 and welfare” and the appointers of all health ministers through the civilized
 world. This source is so wrong that it is destroying man, having already
 destroyed scores of millions.
 Not only taking data from wrong source but officialdom from it can
 therefore be sufficiently aberrated as to result in planetary insanity.
 In a lesser level, taking a report from a known bad hat (corrupt or
 worthless person) and acting upon it is the usual reason for errors made in
 management.
 16
Contrary Facts
 When two statements are made on one subject which are contrary to each
 other, we have “contrary facts.”
 This illogic could be classified as a falsehood, since one of them must be
 false.
 But in investigatory procedure one cannot offhand distinguish which is
 the false fact. Thus it becomes a special outpoint.
 “The company made an above average income that week” and “They
 couldn’t pay the employees” occurring in the same time period gives us one
 or both as false. We may not know which is true but we do know they are
 contrary and can so label it.
 The Amazon 
is the longest 
river in the 
world.
 CONTRARY FACTS
 The Nile is 
the longest 
river in the 
world.
 In interrogation this point is so important that anyone giving two contrary
 facts becomes a prime suspect for further investigation. “I am a Swiss citizen”
 as a statement from someone who has had a German passport found in his
 baggage would be an example.
 When two “facts” are contrary or contradictory, we may not know which
 is true but we do know they can’t both be true.
 Issued by the same organization, even from two different people in that
 organization, two contradictory “facts” qualifies as an outpoint.
 17
ADDED TIME
 Added Time
 In this outpoint we have the reverse of dropped time. In added time we have,
 as the most common example, something taking longer than it possibly could.
 To this degree it is a version of conflicting data—for example, something takes
 three weeks to do but it is reported as taking six months. But added time must
 be called to attention as an outpoint in its own right for there is a tendency to be
 “reasonable” about it and not see that it is an outpoint in itself.
 In its most severe sense, added time becomes a very serious outpoint
 when, for example, two or more events occur at the same moment involving,
 let us say, the same person who could not have experienced both. Time had
 to be added to the physical universe for the data to be true. Like this: “I left for
 Saigon at midnight on April 21, 1962, by ship from San Francisco.” “I took
 over my duties at Saigon on April 30, 1962.” Here we have to add time to the
 physical universe for both events to occur as a ship would take two or three
 weeks to get from San Francisco to “Saigon.”
 Another instance, a true occurrence and better example of added time,
 happened when a checklist of actions it would take a month to complete was
 sent to a junior executive and compliance was received in full in the next
 return mail. The checklist was in her hands only one day! She would have had
 to add twenty-nine days to the physical universe for the compliance report to
 be true. This was also dropped time on her part.
 18
ADDED INAPPLICABLE
 NEW YORK DEMOGRAPHICS
 COMMODITIES IN TEXAS
 OHIO TOXIC TEST RESULTS
 STATE OF OHIO 
HAZARDOUS 
CHEMICAL STUDY
 INTERSTATE POSTAL RATES
 Added Inapplicable Data
 DATA
 Just plain added data does not necessarily constitute an outpoint. It may
 be someone being thorough. But when the data is in no way applicable to the
 scene or situation and is added, it is a definite outpoint.
 Often added data is put there to cover up neglect of duty or mask a real
 situation. It certainly means the person is obscuring something.
 Usually added data also contains other types of outpoints like wrong target
 or added time.
 In using this outpoint be very sure you also understand the word
 inapplicable and see that it is only an outpoint if the data itself does not apply
 to the subject at hand.
 Incorrectly Included Datum
 There is an outpoint called incorrectly included datum, which is a
 companion to the omitted datum as an outpoint.
 This most commonly occurs when, in the mind, the scene itself is missing
 and the first thing needed to classify data (scene) is not there.
 An example is camera storage by someone who has no idea of types of
 cameras. Instead of classifying all the needful bits of a certain camera in one box,
 one inevitably gets the lens hoods of all cameras jumbled into one box marked
 19
INCORRECTLY INCLUDED 
DATUM
 “lens hoods.” To assemble or use the camera one spends hours trying to find its
 parts in boxes neatly labeled “camera backs,” “lenses,” “tripods,” etc.
 Here, when the scene of what a set-up camera looks like and operates like,
 is missing, one gets a closer identification of data than exists. Lens hoods are
 lens hoods. Tripods are tripods. Thus a wrong system of classification occurs
 out of scene ignorance.
 A traveler unable to distinguish one uniform from another “solves” it by
 classifying all uniforms as “porters.” Hands his bag to an arrogant police
 captain and that’s how he spent his vacation, in jail.
 Lack of the scene brings about too tight an identification of one thing with
 another.
 A newly called-up army lieutenant passes right on by an enemy spy
 dressed as one of his own soldiers. An experienced sergeant right behind him
 claps the spy in jail accurately because “he wasn’t wearing ’is ’at the way we
 do in our regiment!”
 Times change data classification. In 1920 anyone with a camera near a
 seaport was a spy. In 1960 anyone not carrying a camera couldn’t be a tourist
 so was watched!
 So the scene for one cultural period is not the scene for another.
 20
21
 There are three other types of outpoints which should be known for use
 in an investigation. These are as follows:
 ASSUMED “IDENTITIES” 
ARE NOT IDENTICAL
 ASSUMED “SIMILARITIES” 
ARE NOT SIMILAR OR 
SAME CLASS OF THING
 “Zippy” Cola is so 
much better than “A1.”
 Zippy 
Cola
 A1 
Cola
 ASSUMED “DIFFERENCES” 
ARE NOT DIFFERENT
 Generic 
Brands 
Mfg. Co.
Handling Data
 There are hundreds of ways these mishandlings of data can then give one
 a completely false picture.
 When basing actions or orders on data which contains one of the above,
 one then makes a mistake.
 REASON DEPENDS ON DATA.
 WHEN DATA IS FAULTY (as above) THE ANSWER WILL BE WRONG
 AND LOOKED UPON AS UNREASONABLE.
 There are a vast number of combinations of these data. More than one (or
 all) may be present in the same report.
 Observation and its communication may contain one of these illogics.
 If so, then any effort to handle the situation will be ineffective in correcting
 or handling it.
 Use
 If any body of data is given the above tests, it is often exposed as an
 invitation to acting illogically.
 To achieve a logical answer one must have logical data.
 Any body of data which contains one or more of the above faults can lead
 one into illogical conclusions.
 The basis of an unreasonable or unworkable order is a conclusion which
 is made illogical by possessing one or more of the above faults.
 22
Pluspoints
 There are one or more conditions which exist when a situation or
 circumstance is logical. These are called pluspoints. A pluspoint is a datum of
 truth found to be true when compared to the following list of logical
 conditions.
 Pluspoints show where logic exists and where things are going right or likely to.
 Where things get better or there is a sudden improvement in an area or
 organization, the cause for this should be found to reinforce what was
 successful. Such an investigation is done by use of pluspoints.
 The pluspoints are as follows:
 RELATED FACTS KNOWN. (All relevant facts known.)
 RELATED FACTS KNOWN
 EVENTS IN CORRECT SEQUENCE. (Events in actual sequence.)
 EVENTS IN 
CORRECT SEQUENCE
 23
24
 TIME NOTED.
 (Time is properly noted.)
 DATA PROVEN FACTUAL. 
(Data must be factual, which is to say, true and valid.)
 CORRECT RELATIVE IMPORTANCE.
 (The important and unimportant are correctly sorted out.)
 TIME NOTED Mr. and Mrs.
 Bernstein request 
your presence at the 
wedding of their 
daughter Susan to 
Mr. Richard Ellis 
at Townville 
Church. 
Saturday June 21 
5:00 pm
 DATA PROVEN 
FACTUAL
 Jumbo 
Burger
 CORRECT RELATIVE IMPORTANCE
25
 EXPECTED TIME PERIOD. (Events occurring or done in the time one would
 reasonably expect them to be.)
 ADEQUATE DATA. (No sectors of omitted data that would
 influence the situation.)
 APPLICABLE DATA. (The data presented or available applies to the matter in
 hand and not something else.)
 EXPECTED TIME
 PERIOD
 ADEQUATE DATA
 Joe Jones
 124 W. 74th Street
 New York, N.Y.
 10047
 APPLICABLE DATA
Postal 
Service
 CORRECT SOURCE. 
(Not wrong source.)
 CORRECT TARGET. 
(Not going in some direction that would be wrong for the situation.)
 DATA IN SAME CLASSIFICATION. (Data from two or more different classes
 of material not introduced as the same class.)
 CORRECT SOURCE
 CORRECT TARGET
 DATA IN SAME CLASSIFICATION
 26
27 # 27
 dub
 20.3 fl oz./600 mi. 20.3 fl oz./600 mi.
 dub
 down under bath
 tea tree
 body soap
 for normal 
to oily skin
 arbre à
 thé savon 
pour
 le corps
 peaux
 normales
 ou grasses
 If your skin
 feels as dry as
 the desert, then
 refresh it while
 cleansing away
 the impurities of
 the day. Go
 down under to
 get Gold Coast
 freshness. 
down under bath
 tea tree
 body soap
 for normal 
to oily skin
 arbre à
 thé savon 
pour
 le corps
 peaux
 normales
 ou grasses
 If your skin
 feels as dry as
 the desert, then
 refresh it while
 cleansing away
 the impurities of
 the day. Go
 down under to
 get Gold Coast
 freshness. 
IDENTITIES ARE IDENTICAL. 
(Not similar or different.)
 SIMILARITIES ARE SIMILAR. 
(Not identical or different.)
 DIFFERENCES ARE DIFFERENT. 
(Not made to be identical or similar.)
 IDENTITIES ARE IDENTICAL
 SIMILARITIES ARE SIMILAR
 DIFFERENCES ARE DIFFERENT
 AIR
 OIL
 GAS
In finding out why things got better so they can be repeated, it is vital to
 use the actual pluspoints by name as above.
 Pluspoints are, after all, what make things go right.
 Not Know
 One can always know something about anything.
 It is a wise man who, confronted with conflicting data, realizes that he
 knows at least one thing—that he doesn’t know.
 Grasping that, he can then take action to find out.
 If he evaluates the data he does find out against the things above, he can
 clarify the situation. Then he can reach a logical conclusion.
 Drills
 It is necessary to work out your own examples of the violations of logic
 described herein.
 By doing so, you will have gained skill in sorting out the data of a situation.
 When you can sort out data and become skilled in it, you will become very
 difficult to fool and you will have taken the first vital step in grasping a correct
 estimate of any situation.
 28
DATA AND 
SITUATION 
ANALYZING
 That one gains an excellent understanding of logic and a good grasp of the
 types of outpoints and pluspoints is vital to investigation. With this as a
 foundation, the two general steps one has to take to “find out what is really
 going on” are:
 1. Analyze the data,
 2. Using the data thus analyzed to analyze the situation.
 The term data is defined as facts, graphs, statements, decisions, actions,
 descriptions, which are supposedly true. Situation is defined as the broad
 general scene on which a body of current data exists.
 The way to analyze data is to compare it to the outpoints and see if any of
 those appear in the data.
 The way to analyze the situation is to put in its smaller areas each of the
 data analyzed as above.
 Doing this gives you the locations of greatest error or disorganization and
 also gives you areas of greatest effectiveness.
 Example: There is trouble in the Refreshment Unit. There are three people
 in the unit. Doing a data analysis on the whole area gives us a number of
 outpoints. Then we assign these to employees A, B and C who work in the unit
 and find B had the most outpoints. This indicates that the trouble in the
 Refreshment Unit is with B. B can be handled in various ways such as training
 him on the duties of his job, his attendance, etc. Note we analyzed the data of
 the main area and assigned it to the bits in the area, then we had an analyzed
 situation and we could handle.
 29
Example: We analyze all the data we have about the Bingo Car Plant. We
 assign the data thus analyzed as out (outpoints) to each function of the Bingo
 Car Plant. We thus pinpoint what function is the worst off. We then handle
 that function in various ways, principally by organizing it and training its
 executives and personnel.
 There are several variations.
 WE OBTAIN AN ANALYSIS OF THE SITUATION BY ANALYZING ALL
 THE DATA WE HAVE AND ASSIGNING THE OUTPOINT DATA TO THE
 AREAS OR PARTS. THE AREA HAVING THE MOST OUTPOINTS IS THE
 TARGET FOR CORRECTION.
 In confronting a broad situation to be handled, we have of course the
 problem of finding out what’s wrong before we can correct it. This is done by
 data analysis followed by situation analysis.
 We do this by grading all the data for outpoints (illogics). We now have a
 long list of outpoints. This is data analysis.
 We sort the outpoints we now have into the principal areas of the scene.
 The majority will appear in one area. This is situation analysis.
 We now know what area to handle.
 Example: Seventy data exist on the general scene. We find twenty-one of
 these data are irrational (outpoints). We slot the twenty-one outpoints into
 the areas they came from or apply to. Sixteen came from area G. We handle
 area G.
 Experience
 The remarkable part of such an exercise is that the data analysis of the data
 of a period of one day compares to three months operating experience.
 Thus data and situation analysis is an instant result where experience
 takes a lot of time.
 30
31
 One obtains an 
analysis of a 
situation by 
analyzing all 
the data one has 
and assigning 
the outpoint 
data to the areas 
or posts. The 
area having the 
most outpoints is 
the target for 
correction.
 Data
 Data
 Data Data
 Data
 Data Irrational Data (Outpoint)
 Data Analysis
 A B C D E F G
 Situation Analysis
The quality of the data analysis depends on one knowing the ideal
 organization and purpose on which the activity is based. This means one has
 to know what its activities are supposed to be from a rational or logical
 viewpoint.
 A clock is supposed to keep running and indicate time and be of practical
 and pleasant design. A clock factory is supposed to make clocks. It is supposed
 to produce enough clocks cheaply enough that are good enough to be in
 demand and to sell for enough to keep the place solvent. It consumes raw
 materials, repairs and replaces its tools and equipment. It hires workmen and
 executives. It has service firms and distributors. That is the sort of thing one
 means by ideal or theoretical structure of the clock company and its
 organization.
 Those are the rational points.
 From the body of actual current today data on the clock company one
 spots the outpoints for a data analysis.
 One assigns the outpoints to the whole as a situation analysis.
 One uses his admin know-how and expertise to repair the most aberrated
 subsection.
 One gets a functioning clock factory that runs closer to the ideal.
 Military, political and PR situations, etc., are handled all in the same way.
 We call these two actions:
 DATA ANALYSIS
 SITUATION ANALYSIS
 32
FAMILIARITY
 If one has no familiarity with how a scene (area) ought to be, one cannot
 easily spot outpoints (illogical data) in it.
 This is what also could be called an ideal scene or situation. If one doesn’t
 know the ideal scene or situation then one is not likely to observe nonideal
 points in it.
 Let us send a farmer to sea. In a mild blow, with the sails and their gear
 creaking and water hitting the hull, he is sure the ship is about to sink. He has
 no familiarity with how it should sound or look so he misses any real
 outpoints and may consider all pluspoints as outpoints.
 Yet on a calm and pretty day he sees a freighter come within five hundred
 feet of the side and go full astern and thinks everything is great.
 An experienced officer may attempt madly to avoid collision and all the
 farmer would think was that the officer was being impolite! The farmer,
 lacking any familiarity with the sea and having no ideal as to what smooth
 running would be, would rarely see real outpoints unless he drowned. Yet an
 experienced sailor, familiar with the scene in all its changing faces sees an
 outpoint in all small illogicals.
 On the other hand, the sailor on the farm would completely miss disease
 in the wheat and an open gate and see no outpoints in a farm that the farmer
 knew was about to go bust.
 The rule is:
 A PERSON MUST HAVE AN IDEAL SCENE WITH WHICH TO
 COMPARE THE EXISTING SCENE.
 If a staff hasn’t got an idea of how a real organization should run, then it
 misses obvious outpoints.
 One sees examples of this when an experienced organization executive
 visiting an organization tries to point out to a green staff (which has no ideal
 or familiarity) what is out. The green staff grudgingly fixes up what he says to
 do but lets go of it the moment he departs. Lacking familiarity and an ideal of
 a perfect organization, the green staff just doesn’t see anything wrong or
 anything right either!
 33
The consequences of this are themselves illogical. One sees an untrained
 executive firing all the producers and letting the bad hats (corrupt or
 worthless people) alone. His erroneous ideal would be a quiet organization,
 let us say. So he dismisses anyone who is noisy or demanding. He ignores
 statistics. He ignores the things he should watch merely because he has a
 faulty ideal and no familiarity of a proper scene.
 Observation Errors
 When the scene is not familiar one has to look hard to become aware of
 things. You’ve noticed tourists doing this. Yet the old resident “sees” far more
 than they do while walking straight ahead down the road.
 It is easy to confuse the novel with the “important fact.” “It was a warm
 day for winter” is a useful fact only when it turns out that actually everything
 froze up on that day or it indicated some other outpoint.
 Most errors in observation are made because one has no ideal for the scene
 or no familiarity with it.
 However there are other error sources.
 “Being reasonable” is the chief offender. People dub in (presume or have
 a false, delusory perception of) a missing piece of a sequence, for instance,
 instead of seeing that it is missing. A false datum is imagined to exist because
 a sequence is wrong or has a missing step.
 It is horrifying to behold how easily people buy dub-in. This is because an
 illogical sequence is uncomfortable. To relieve the discomfort they distort their
 own observation by ignoring the outpoint and concluding something else.
 Accurate Observation
 There are certain conditions necessary for accurate observation.
 First is a means of perception whether by remote communication by
 various communication lines or by direct looking, feeling, experiencing.
 Second is an ideal of how the scene or area should be.
 Third is familiarity with how such scenes are when things are going well
 or poorly.
 Fourth is understanding pluspoints or rightnesses when present.
 34
Fifth is knowing outpoints (all types) when they appear.
 Sixth is rapid ability to analyze data.
 Seventh is the ability to analyze the situation.
 Eighth is the willingness to inspect more closely the area of outness.
 Then one has to have the knowledge and imagination necessary to handle.
 One could call the above the cycle of observation. If one calls handle
 number nine it would be the Cycle of Control.
 If one is trained to conceive all variations of outpoints (illogics) and
 studies up to conceive an ideal and gains familiarity with the scene or type of
 area, his ability to observe and handle things would be considered almost
 supernatural.
 People easily buy 
imaginary data. 
To relieve the 
discomfort they 
distort their own 
observation by 
ignoring the 
outpoint and 
concluding 
something else.
 35
INVESTIGATORY 
ACTIONS
 Correction of things which are not wrong and neglecting things which are
 not right puts the tombstone on any organization or civilization.
 This boils down to correct investigation. It is not a slight skill. It is the basic
 skill behind any intelligent action.
 Suppressive Justice
 When justice goes astray (as it usually does) the things that have occurred are:
 1. Use of  justice for some other purpose than public safety (such as
 maintaining a privileged group or indulging a fixed idea) or
 2. Omitted use of investigatory procedure.
 All suppressive use of the forces of justice can be traced back to one or the
 other of these.
 Aberrations and hate very often find outlet by calling them “justice” or
 “law and order.” This is why it can be said that man cannot be trusted with
 justice.
 This or just plain stupidity bring about a neglect of intelligent
 investigatory procedures. Yet all group sanity depends upon correct and
 unaberrated (rational) investigatory procedures. Only in that way can one
 establish causes of things. And only by establishing causes can one cease to be
 the effect of unwanted situations.
 It is one thing to be able to observe. It is quite another to utilize
 observations so that one can get to the basis of things.
 36
Sequences
 Investigations become necessary in the face of outpoints or pluspoints.
 Investigations can occur out of idle curiosity or particular interest. They
 can also occur to locate the cause of pluspoints.
 Whatever the motive for investigation, the action itself is conducted by
 sequences.
 If one is incapable mentally of tracing a series of events or actions, one
 cannot investigate.
 Altered sequence is a primary block to investigation.
 At first glance, omitted data would seem to be the block. On the contrary,
 it is the end product of an investigation and is what pulls an investigation
 along—one is looking for omitted data.
 An altered sequence of actions defeats any investigation. Examples: We
 will hang him and then conduct a trial. We will assume who did it and then
 find evidence to prove it. A crime should be provoked to find who commits
 them.
 Any time an investigation gets back-to-front, it will not succeed.
 Thus, if an investigator himself has any trouble with seeing or visualizing
 sequences of actions, he will inevitably come up with the wrong answer.
 Reversely, when one sees that someone has come up with a wrong or
 incomplete answer, one can assume that the investigator has trouble with
 sequences of events or, of course, did not really investigate.
 One can’t really credit that Sherlock Holmes would say, “I have here the
 fingerprint of Mr. Murgatroyd on the murder weapon. Have the police arrest
 him. Now, Watson, hand me a magnifying glass and ask Sgt. Doherty to let us
 look over his fingerprint files.”
 If one cannot visualize a series of actions, like a ball bouncing down a
 flight of stairs, or if one cannot relate in proper order several different actions
 with one object into a proper sequence, he will not be able to investigate.
 If one can, that’s fine.
 37
Investigations
 All betterment of life depends on finding out pluspoints and why and
 reinforcing them, locating outpoints and why and eradicating them.
 This is the successful survival pattern of living. A primitive who is going
 to survive does just that and a scientist who is worth anything does just that.
 The fisherman sees sea gulls clustering over a point on the sea. That’s the
 beginning of a short sequence, point number one. He predicts a school of fish,
 point number two. He sails over as sequence point number three. He looks
 down as sequence point number four. He sees fish as point number five. He
 gets out a net as point number six. He circles the school with the net, number
 seven. He draws in the net, number eight. He brings the fish on board, number
 nine. He goes to port, number ten. He sells the fish, number eleven. That’s
 following a pluspoint—cluster of sea gulls.
 A sequence from an outpoint might be: Housewife serves dinner. Nobody
 eats the cake, number one; she tastes it, number two; she recognizes soap in
 it, number three. She goes to kitchen, number four. She looks into cupboard,
 number five. She finds the soapbox upset, number six. She sees the flour
 below it, number seven. She sees cookie jar empty, number eight. She grabs
 young son, number nine. She shows him the setup, number ten. She gets a
 confession, number eleven. And number twelve is too painful to describe.
 Discoveries
 All discoveries are the end product of a sequence of investigatory actions
 that begin with either a pluspoint or an outpoint.
 Thus all knowledge proceeds from pluspoints or outpoints observed.
 And all knowledge depends on an ability to investigate.
 And all investigation is done in correct sequence.
 And all successes depend upon the ability to do these things.
 38
WHYS
 One uses the above knowledge and skill to track down the real reason for
 the positive or nonoptimum situation. This is called a “Why.”
 Why = that basic outness found which will lead to a recovery of statistics.
 Wrong Why = the incorrectly identified outness which when applied does
 not lead to recovery.
 A mere explanation = a “Why” given as the Why that does not open the
 door to any recovery.
 Example: A mere explanation: “The statistics went down because of rainy
 weather that week.” So? So do we now turn off rain? Another mere explanation:
 “The staff became overwhelmed that week.” An order saying “Don’t overwhelm
 staff” would be the possible “solution” of some manager. BUT THE STATISTICS
 WOULDN’T RECOVER.
 The real Why when found and corrected leads straight back to improved
 stats (statistics).
 A wrong Why, corrected, will further depress stats.
 A mere explanation does nothing at all and decay continues.
 Here is a situation as it is followed up:
 The stats of an area were down. Investigation disclosed there had been
 sickness two weeks before. The report came in: “The statistics were down
 because people were sick.” This was a mere explanation. Very reasonable. But
 it solved nothing. What do we do now? Maybe we accept this as the correct
 Why. And give an order, “All people in the area must get a medical exam and
 unhealthy workers will not be accepted and unhealthy ones will be fired.” As
 it’s a correction to a wrong Why, the stats really crash. So that’s not it. Looking
 further we find the real Why. In the area, a boss gives orders to the wrong
 people which, when executed, then hurt their individual stats. We organize
 the place, train the boss and we get a stat recovery and even an improvement.
 39
The correct Why led to a stat recovery. Here is another one. Statistics are
 down in a school. An investigation comes up with a mere explanation: “The
 students were all busy with sports.” So management says “No sports!”
 Statistics go down again. A new investigation comes up with a wrong Why:
 “The students are being taught wrongly.” Management sacks the dean.
 Statistics really crash now. A further, more competent investigation occurs.
 It turns out that there were 140 students and only the dean and one
 instructor! And the dean had other duties! We return the dean to his job and
 hire two more instructors making three. Statistics soar. Because we got the
 right Why.
 Management and organizational catastrophes and successes are all
 explained by these three types of Why. An arbitrary, a false order or datum
 entered into a situation, is probably just a wrong Why held in by law. And if
 so held in, it will crash the place.
 One really has to understand logic to get to the correct Why and must
 really be on his toes not to use and correct a wrong Why.
 In world banking, where inflation occurs, finance regulations or laws are
 probably just one long parade of wrong Whys. The value of the money and its
 usefulness to the citizen deteriorate to such an extent that a whole ideology
 can be built up (as in Sparta by Lycurgus [a Greek lawgiver] who invented
 iron money nobody could lift in order to rid Sparta of money evils) that
 knocks money out entirely and puts nothing but nonsense in its place.
 Organizational troubles are greatly worsened by using mere
 explanations (which lead to no remedies) or wrong Whys (which further
 depress stats). Organizational recoveries come from finding the real Why
 and correcting it.
 The test of the real Why is “When it is corrected, do stats recover?” If they
 do that was it. And any other remedial order given but based on a wrong Why
 would have to be cancelled quickly.
 40
DOING AN 
INVESTIGATION
 When one begins to apply data analysis, he is often still trying to grasp the
 data about data analysis rather than the outpoints in the data. The remedy is
 just become more familiar with the materials of this booklet.
 Further, one may not realize the ease with which one can acquire the
 knowledge of an ideal scene. An outpoint is simply an illogical departure from
 the ideal scene. By comparing the existing scene with the ideal scene, one
 easily sees the outpoints.
 To know the ideal scene, one has only to work out the correct products for
 it. If these aren’t getting out, then there is a departure. One can then find the
 outpoints of the various types and then locate a Why and in that way open the
 door to handling. And by handling, one is simply trying to get the scene to get
 out its products.
 Unless one proceeds in this fashion (from product back to establishment),
 one can’t analyze much of anything. One merely comes up with errors.
 An existing scene is as good as it gets out its products, not as good as it is
 painted or carpeted or given public relations boosts.
 So for any scene, manufacturing or fighting a war or being a hostess at a
 party, there are products.
 People who lead pointless lives are very unhappy people. Even the idler or
 dilettante is happy only when he has a product!
 There is always a product for any scene.
 Standard Action
 A beginner can juggle around and go badly adrift if he doesn’t follow the
 pattern:
 41
42
 ACE
 FURNITURE
 1. Work out exactly what the (person, unit, activity) should be producing.
 2. Work out the ideal scene.
 Loading
 Bay
 ACE
 FURNITURE
 Samson
 REALTORS
 3. Investigate the existing scene.
43
 4. Follow outpoints back from ideal to existing.
 There’s no lumber 
on the shelves.
 Why wasn’t 
any lumber 
delivered?
 Stop 
Order Bill
 ACCOUNTS
 Why weren’t 
these bills paid?
 Overdue
 Notice
44
 Accounts
 Re
 99
 Lumber
 Mill
 5. Locate the real Why that will move the existing toward ideal.
 I’ve never seen them 
before.
 Miss Rogers, 
did you file these 
bills from “99 
Lumber Mill”?
 No, I didn’t know 
where to file bills for a 
company that began 
with a number 
instead of a letter, so I 
stuffed them in a 
drawer.
 I see. Would you 
read this for me, 
please?
 It says, “Accounts 
Re” … um … uh … 
I guess I’m not very 
good at reading.
 Accounts
 Re
 99
 Lumber
 Mill
 ACCOUNTANT
45
 6. Look over existing resources.
 7. Get a bright idea of how to handle.
 Employee Training 
Program
 Adult Community College
 Personnel Department Directive __________________________
 All prospective employees 
must pass a literacy standards 
test before being hired.
 8. Handle or recommend handling so that it stays handled.
This is a very surefire approach.
 If one just notes errors in a scene, with no product or ideal with which to
 compare the existing scene, he will not be doing data analysis and situations
 will deteriorate badly because he is finding wrong Whys.
 Thinking
 One has to be able to think with outpoints. A crude way of saying this is
 “learn to think like an idiot.” One could also add “without abandoning any
 ability to think like a genius.”
 If one can’t tolerate outpoints at all or confront them, one can’t see them.
 A madman can’t tolerate pluspoints and he doesn’t see them either.
 But there can be a lot of pluspoints around and no production. Thus, one
 can be told how great it all is while the place edges over to the point of
 collapse.
 One who listens to people on the scene and takes their Whys runs a grave
 risk. If these were the Whys, then things would be better.
 A far safer way is to talk only insofar as finding what the product is
 concerned and investigating.
 One should observe the existing scene through data or through observers
 or through direct observation.
 One often has to guess what the Why might be. It is doing that which
 brings up the phrase “Learn to think like an idiot.” The Why will be found at
 the end of a trail of outpoints. Each one is an aberration when compared to
 the ideal scene. The biggest idiocy which then explains all the rest and which
 opens the door to improvement toward the ideal scene is the Why.
 One also has to learn to think like a genius with pluspoints.
 Get the big peak period of production (now or in the past). Compare it to
 the existing scene just before.
 Now find the pluspoints that were entered in. Trace these and you arrive
 at the Why as the biggest pluspoint that opened the door to improvement.
 But once more one considers resources available and has to get a bright
 idea.
 So it is the same series of steps as above but with pluspoints.
 46
SUCCESSFUL 
INVESTIGATIONS
 Correct investigations depend on correct Whys. You can understand a real
 Why if you realize this:
 A REAL WHY OPENS THE DOOR TO HANDLING.
 If you write down a Why, ask this question of it: “Does this open the door
 to handling?”
 If it does not, then it is a wrong Why.
 When you have a right Why, handling becomes simple. The more one has
 to beat his brains for a bright idea to handle, the more likely it is that he has a
 wrong Why.
 So if the handling doesn’t leap out at you then THE WHY HAS NOT
 OPENED THE DOOR and is probably wrong.
 A right Why opens the door to improvement, enabling one to work out a
 handling which, if correctly done, will attain the envisioned ideal scene.
 Investigatory Technology can be applied to situations good or bad, large or
 small, dispelling many of life’s puzzles and making real solutions possible.■
 47
48
 PRACTICAL EXERCISES
 Here are some practical exercises to increase your knowledge and skill in
 applying the basic data on investigations.
 1Using a newspaper or newsmagazine, find two data which you don’t
 understand. Then write down the question you would ask to clear up the
 contradiction. Repeat this five other times.
 2For each of the following outpoints, write down three examples that you
 could observe or that could occur in your life:
 Omitted Data    Altered Sequence
 Dropped Time  Falsehood
 Altered Importance    Wrong Target
 Wrong Source    Contrary Facts
 Added Time   Added Inapplicable Data
 Incorrectly Included Datum    Assumed “Identities” Are Not Identical
 Assumed “Similarities” Are Assumed “Differences” 
Not Similar Are Not Different
 3For each of the following pluspoints, write down three examples that
 you could observe or that could occur in your life:
 Related Facts Known     Events in Correct Sequence 
Time Noted   Data Proven Factual 
Correct Relative Importance Expected Time Period
 Adequate Data Applicable Data    
Correct Source Correct Target 
Data in Same Classification Identities Are Identical
 Similarities Are Similar Differences Are Different
 4By observation of your environment or by looking at newspapers,
 magazines, etc., find twenty outpoints. For each, write down the type of
 outpoint.
 5By observation of your environment or by looking at newspapers,
 magazines, etc., find twenty pluspoints. For each, write down the type of
 pluspoint.
 6In your environment, newspapers, magazines and so on, locate two
 conflicting data. Then write down how you would find what you didn’t
 know so as to resolve the conflict between the data. Repeat this three
 more times.
7Using the data in a newspaper or magazine, do a data analysis. Then,
 using this, do a situation analysis. Repeat these steps two more times.
 8Do the following:
 a. Write down an activity with which you have good familiarity.
 b. Write down an ideal scene one could have for that activity.
 c. Repeat steps (a) and (b) four more times for different activities.
 9Describe an example you observed or experienced where someone was
 “being reasonable.” Include the data or circumstances the person was
 faced with, and the outpoint(s) being ignored. Repeat this for two other
 examples.
 10Write down a sequence which describes in proper order several
 different actions with one object. Repeat this for four other sequences.
 11Using a newspaper or newsmagazine, find three examples of a wrong
 Why. For each one, write down the reason it is a wrong Why.
 12Using a newspaper or magazine, find three examples of a mere
 explanation. For each one, write down the reason it is a mere explanation.
 13
 Using an area or activity with which you are very familiar, apply steps
 1–8 of the subsection, “Standard Action” (under “Doing an Investigation”)
 and do the following:
 a. Write down what the person, area or activity should be producing.
 b. Using what you wrote up in step (a), write down its ideal scene.
 c. Write down the existing scene for this area or activity.
 d. Using the materials you studied in this booklet, investigate the
 existing scene. Write down what you find.
 e. Follow outpoints you find in this area or activity back from the
 ideal scene to the existing scene.
 f. Locate the real Why of the area or activity being investigated. Apply
 the materials in the booklet to confirm this is a right Why by asking
 the following question of it: “Does this open the door to handling?”
 g. Based on what you found in steps (a)–(f) above, look over the
 existing resources and get a bright idea of how to handle. Write down
 these resources and your bright idea. Then list out the steps you would
 do to handle the area or activity to move it toward the ideal scene.
 49
RESULTS FROM APPLICATION
 The technology of investigation has
 been successfully used in many different
 areas of human endeavor. One does not
 have to be a professional investigator to
 benefit from this data. Many use it on
 their jobs to find out why there has been a
 slump, or what caused a recent increase
 in statistics in order to ensure the
 expansion of an organization. Use in the
 home makes for increased accord
 amongst family members and a happier
 family. The ability to think clearly and act
 sanely as a result of accurate investigation
 is key to survival. Some examples of the
 use of this technology follow:
 Investigatory Technology can be used
 with amazing success to improve the
 quality of life in any area. Here is an
 example:
 “I moved into a new neighborhood
 where people seemed to be always getting
 sick—routinely coming down with colds
 and other illnesses. Using L. Ron Hubbard’s
 INVESTIGATIONS 
AND EXPANSION
 An organization that 
implemented the tech 
of investigations 
found that it directly 
affected the overall 
statistics of the 
organization
 PERCENT OF OVERALL
 STATISTICS INCREASING
 Investigatory Technology, I narrowed down
 the probable cause area to a pond that was
 heavily infested with insects. Bugs,
 especially flies, easily carry disease. Further
 investigation disclosed the unbelievable: the
 pond was being covertly used as a dump site
 and thousands of gallons of waste were
 being pumped into it weekly! I forced
 officials to handle the situation. The insect
 population immediately died down, illness
 in our community dramatically reduced,
 and the pond reverted to its original natural
 beauty.”
 Having the technology of outpoints and
 the ability to find a right Why is essential
 according to an executive in a Los
 Angeles management company.
 “It makes it possible to perceive what’s
 in front of you as well as what went before
 that—it brings everything into alignment.
 Then you can see the outpoints and
 pluspoints immediately as you have a
 complete understanding of the situation.”
 80%
 75%
 70%
 65%
 60%
 NUMBER OF
 INVESTIGATIONS DONE
 50
 55%
 An attorney in California found that the
 administrative technology of investiga
tions made his job considerably easier.
 “With this data, I find that situations
 I see in my job are easier to understand.
 When I look at a situation I can see the
 outpoints, but I also look for the ideal
 scene and this is invaluable in sorting out
 legal situations. For example, I had a
 client who thought he had an incredible
 problem with his partner. We looked at
 the outpoints and determined that it
 wasn’t the partner at all—the problem
was coming from another part of the
 company entirely! He was then able to
 apply the proper handling to the
 situation, which was of course, quite
 different than the one he had anticipated.”
 Trained in investigatory procedure as
 developed by Mr. Hubbard, an individual
 said the following about its use:
 “I can’t say enough about L. Ron
 Hubbard’s Investigatory Technology. Not
 only have I used it successfully on my job,
 but I use it in everyday situations I
 encounter to make life go more smoothly
 for myself and my friends. Using this
 Investigatory Technology I have found lost
 children, located misplaced money, figured
 out and corrected the reason my computer
 would not work and have even discovered
 the Why behind a terrible tasting dessert!
 Hardly a day goes by without being able to
 use this technology to make life better.”
 A business consultant had a client who
 ran an auto repair business. The business
 was struggling and the client himself was
 not doing well in life. The consultant
 used Investigatory Technology to handle
 this:
 “Initially it didn’t make sense that
 this business was not doing well. My
 client was a smart man and had done well
 in the past. I started looking into this to
 find out what the Why was. Coincident
 with the beginning of the business slump,
 I found that he had employed a ‘silent
 partner’ to do his books. So I looked into
 this fellow carefully and discovered that
 he was not being wholly honest in his
 bookkeeping and, in fact, was ripping my
 client off! Because this man had posed as
 a professional and as someone who knew
 what he was doing, my client had not
 looked into this area at all as a possible
 reason for his failing. Simply by applying
 Mr. Hubbard’s technology on investigations,
 the answer fell into my lap. My client was
 more than relieved. He took back over the
 business in full and his statistics took off. He
 himself is back to battery, and once again I
 marvel at the simplicity of Investigatory
 Technology.”
 51
GLOSSARY
 aberration: a departure from rational
 thought or behavior; irrational thought or
 conduct. It means basically to err, to make
 mistakes, or more specifically to have fixed
 ideas which are not true. The word is also
 used in its scientific sense. It means depar
ture from a straight line. If a line should go
 from A to B, then if it is aberrated it would
 go from A to some other point, to some
 other point, to some other point, to some
 other point, to some other point, and finally
 arrive at B. Taken in this sense, it would also
 mean the lack of straightness or to see
 crookedly as, for example, a man sees a
 horse but thinks he sees an elephant. Aber
rated conduct would be wrong conduct, or
 conduct not supported by reason. Aberra
tion is opposed to sanity, which would be its
 opposite. From the Latin, aberrare, to wan
der from; Latin, ab, away, errare, to wander. 
communication line: the route along
 which a communication travels from one
 person to another. 
confront: to face without flinching or
 avoiding. The ability to confront is actually
 the ability to be there comfortably and
 perceive.
 dub in: presume or have a false, delusory
 perception of.
 outness: a condition or instance of some
thing being wrong, incorrect or missing. 
outpoint: any one of several specific ways
 in which a relay of information or a situation
 52
 can become illogical; any one datum offered
 as true that is in fact found to be illogical. 
pluspoint: any one of several conditions
 which exist when a situation or circum
stance is logical. Pluspoints show where
 logic exists and where things are going right
 or likely to. 
present time: the time which is now and
 becomes the past as rapidly as it is observed.
 It is a term loosely applied to the environ
ment existing in now.
 Scientology: an applied religious philoso
phy developed by L. Ron Hubbard. It is the
 study and handling of the spirit in relation
ship to itself, universes and other life. The
 word Scientology comes from the Latin scio,
 which means “know” and the Greek word
 logos, meaning “the word or outward form
 by which the inward thought is expressed
 and made known.” Thus, Scientology means
 knowing about knowing.
 terminal: a person, point or position which
 can receive, relay or send a communication. 
Why: reason or cause; the real reason for a
 positive or nonoptimum situation. 
win: the accomplishment of any desired
 improvement. Examples of wins would be a
 person increasing his ability to communi
cate, experiencing an increased feeling of
 well-being or gaining more certainty about
 some area of his life.
ABOUT 
L. RON 
HUBBARD
 Born in Tilden, Nebraska on March 13,
 1911, his road of discovery and dedication
 to his fellows began at an early age. By the
 age of nineteen, he had traveled more than
 a quarter of a million miles, examining the
 cultures of Java, Japan, India and the
 Philippines.
 Returning to the United States in 1929,
 Ron resumed his formal education and
 studied mathematics, engineering and the
 then new field of nuclear physics—all
 providing vital tools for continued
 research. To finance that research, Ron
 embarked upon a literary career in the
 early 1930s, and soon became one of the
 most widely read authors of popular
 fiction. Yet never losing sight of his
 primary goal, he continued his mainline
 research through extensive travel and
 expeditions.
 With the advent of World War II, he
 entered the United States Navy as a
 lieutenant (junior grade) and served as
 commander of antisubmarine corvettes.
 Left partially blind and lame from injuries
 sustained 
during combat, he was
 diagnosed as permanently disabled by
 1945. Through application of his theories
 on the mind, however, he was not only able
 to help fellow servicemen, but also to
 regain his own health.
 After five more years of intensive
 research, Ron’s discoveries were presented
 to the world in Dianetics: The Modern
 Science of Mental Health. The first popular
 handbook on the human mind expressly
 written for the man in the street, Dianetics
 ushered in a new era of hope for mankind
 and a new phase of life for its author. He
 did, however, not cease his research, and as
 breakthrough after breakthrough was
 carefully codified through late 1951, the
 applied religious philosophy of Scientology
 was born.
 Because Scientology explains the
 whole of life, there is no aspect of
 man’s existence that L. Ron Hubbard’s
 subsequent work did not address. Residing
 variously in the United States and England,
 his continued research brought forth
 solutions to such social ills as declining
 educational standards and pandemic drug
 abuse.
 All told, L. Ron Hubbard’s works on
 Scientology and Dianetics total forty
 million words of recorded lectures, books
 and writings. Together, these constitute
 the legacy of a lifetime that ended on
 January 24, 1986. Yet the passing of L. Ron
 Hubbard in no way constituted an end; for
 with a hundred million of his books in
 circulation and millions of people daily
 applying his technologies for betterment, it
 can truly be said the world still has no
 greater friend.■
 53
Bridge Publications, Inc.
 4751 Fountain Avenue, Los Angeles, CA 90029
 ISBN 0-88404-925-6
 © 1994, 2001 L. Ron Hubbard Library. All Rights Reserved.
 Any unauthorized copying, translation, duplication, 
importation or distribution, in whole or in part, by any 
means, including electronic copying, storage or 
transmission, is a violation of applicable laws.
 Scientology, Dianetics, Celebrity Centre, L. Ron Hubbard, Flag, 
Freewinds, the L. Ron Hubbard Signature, the Scientology 
Cross (rounded) and the Scientology Cross (pointed) are 
trademarks and service marks owned by Religious 
Technology Center and are used with its permission. 
54
 NEW ERA is a trademark and service mark.
 Bridge Publications, Inc. is a registered trademark and 
service mark in California and it is owned by 
Bridge Publications, Inc.
 Printed in the United States of America
 ®
 An L. RON HUBBARD Publication