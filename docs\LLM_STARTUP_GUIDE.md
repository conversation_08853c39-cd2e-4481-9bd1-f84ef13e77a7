# LLM Server Startup Guide

## 🚀 How to Start the Local LLM Server

**IMPORTANT**: Use this exact process every time to start Ollama with the truth-evaluator model.

### **Step 1: Run the Startup Script**

Execute the main startup script:
```cmd
E:\startlocalLLMServer.bat
```

### **Step 2: What This Script Does**

The `startlocalLLMServer.bat` script performs these actions:

1. **Sets Environment Variables**:
   - `OLLAMA_MODELS=E:\Ollama\models` (points to models on E: drive)
   - `OLLAMA_MODEL_KEEP_ALIVE=30m` (keeps models loaded for 30 minutes)
   - `USERPROFILE=E:\Ollama` (forces config to E: drive)
   - `HOME=E:\Ollama` (forces home directory to E: drive)

2. **Starts Ollama Server**:
   - Launches `E:\Ollama\ollama.exe serve` in separate window
   - Waits 10 seconds for server to bind to port 11434

3. **Loads truth-evaluator Model**:
   - Runs `ollama run truth-evaluator` to load the custom model
   - Model stays loaded for 30 minutes (configurable)

4. **Provides Shutdown Control**:
   - Waits for user keypress to shutdown
   - Kills all ollama.exe processes when done

### **Step 3: Verify Server is Running**

Check that Ollama processes are active:
```cmd
tasklist | findstr ollama
```

You should see **3 ollama.exe processes**:
- Main server process (~96MB)
- Worker process (~55MB) 
- Model process (~4.3GB for truth-evaluator)

### **Step 4: Test Connection**

Run the connection test:
```cmd
python quick_ollama_test.py
```

Expected output:
```
Testing Ollama connection...
Status: 200
✅ Ollama is running!
Available models: 3
  - truth-evaluator:latest      
  - dolphin-llama3:latest       
  - dolphin3:latest
```

### **Step 5: Test truth-evaluator Model**

The truth-evaluator model will be slow (30+ seconds per response) on your computer. This is expected.

## 📋 Available Models

Your Ollama installation has these models:

1. **truth-evaluator:latest** - Custom L. Ron Hubbard Investigations model
   - Based on dolphin-llama3
   - Trained on 14 outpoints and 14 pluspoints
   - Used by the Truth Algorithm pipeline
   - **Response time**: 30+ seconds (slow computer)

2. **dolphin-llama3:latest** - Base model
   - Foundation model for truth-evaluator
   - General purpose LLM

3. **dolphin3:latest** - Alternative base model

## 🔧 Alternative Startup Script

If the main script doesn't work, try the alternative:
```cmd
E:\start.bat
```

This script:
- Starts Ollama server
- Waits for port 11434 to be listening
- Also starts AnythingLLM interface

## ⚠️ Important Notes

### **Expected Behavior**
- **Slow Responses**: truth-evaluator takes 30+ seconds per query (normal for your hardware)
- **Memory Usage**: Model process uses ~4.3GB RAM when loaded
- **Timeout Handling**: Python scripts may timeout waiting for responses (increase timeout values)

### **Troubleshooting**

**If Ollama won't start**:
1. Check if processes are already running: `tasklist | findstr ollama`
2. Kill existing processes: `taskkill /F /IM ollama.exe`
3. Try running the startup script again

**If models aren't available**:
1. Verify model files exist in `E:\Ollama\models`
2. Recreate truth-evaluator model: `ollama create truth-evaluator -f Modelfile`

**If connection fails**:
1. Check Windows Firewall settings for port 11434
2. Verify no other service is using port 11434
3. Try restarting the Ollama server

### **Integration with Truth Algorithm**

Once the LLM server is running, you can:

1. **Test the complete pipeline**:
   ```cmd
   python test_complete_pipeline.py
   ```

2. **Run the full Truth Algorithm**:
   ```cmd
   python truth_algorithm.py sample.json -v
   ```

3. **Test individual components**:
   ```cmd
   python test_ollama_connection.py
   ```

## 📁 Related Files

- **Startup Scripts**: `E:\startlocalLLMServer.bat`, `E:\start.bat`
- **Model Definition**: `Modelfile` (in project root)
- **Test Scripts**: `quick_ollama_test.py`, `test_ollama_connection.py`
- **Pipeline Test**: `test_complete_pipeline.py`

---

**Remember**: Always use `E:\startlocalLLMServer.bat` to ensure consistent environment setup and model loading.
