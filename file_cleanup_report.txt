🧹 FILE CLEANUP STRATEGY REPORT
==================================================
Generated: 2025-08-24T22:21:39.721568

📊 TOTAL FILES ANALYZED: 474

📁 SAFE TO REMOVE: 50 files (5,257,784,875 bytes)
   📄 models/bert-test/checkpoint-12/config.json
      Reason: Training checkpoints (keep latest only)
      Size: 1,831 bytes
   📄 models/bert-test/checkpoint-12/model.safetensors
      Reason: Training checkpoints (keep latest only)
      Size: 438,004,788 bytes
   📄 models/bert-test/checkpoint-12/optimizer.pt
      Reason: Training checkpoints (keep latest only)
      Size: 876,125,050 bytes
   📄 models/bert-test/checkpoint-12/rng_state.pth
      Reason: Training checkpoints (keep latest only)
      Size: 13,990 bytes
   📄 models/bert-test/checkpoint-12/scheduler.pt
      Reason: Training checkpoints (keep latest only)
      Size: 1,064 bytes
   ... and 45 more files

📁 ARCHIVE CANDIDATES: 7 files (1,270,110,409 bytes)
   📄 data/ai_batch_results_20250708_232449.json
      Reason: Old AI batch results
      Size: 21,486 bytes
   📄 data/ai_batch_results_20250708_235154.json
      Reason: Old AI batch results
      Size: 55,214,048 bytes
   📄 data/ai_batch_results_20250709_014454.json
      Reason: Old AI batch results
      Size: 18,407,069 bytes
   📄 data/ai_batch_results_20250709_015354.json
      Reason: Old AI batch results
      Size: 1,196,405,594 bytes
   📄 data/statements_to_label_backup_20250709_153655.csv
      Reason: Old backup files from July
      Size: 23,162 bytes
   ... and 2 more files

📁 MARK AS LEGACY: 11 files (438,989,844 bytes)
   📄 data/enhanced_training_data.csv
      Reason: Replaced by complete_28_category_dataset.csv
      Size: 22,558 bytes
   📄 models/train_deberta.py
      Reason: Original training script
      Size: 10,327 bytes
   📄 models/bert-test/config.json
      Reason: Round 1 model (17 categories only)
      Size: 1,831 bytes
   📄 models/bert-test/label_map.json
      Reason: Round 1 model (17 categories only)
      Size: 489 bytes
   📄 models/bert-test/model.safetensors
      Reason: Round 1 model (17 categories only)
      Size: 438,004,788 bytes
   ... and 6 more files

📁 KEEP ACTIVE: 11 files (439,059,762 bytes)
   📄 base_knowledge_db.py
      Reason: Data quality tool
      Size: 5,912 bytes
   📄 SESSION_STATUS.md
      Reason: Project status
      Size: 15,818 bytes
   📄 truth_algorithm.py
      Reason: Main algorithm
      Size: 18,050 bytes
   📄 data/complete_28_category_dataset.csv
      Reason: Current training data
      Size: 29,503 bytes
   📄 models/round2-simple/config.json
      Reason: Current model
      Size: 1,993 bytes
   ... and 6 more files

📁 NEEDS REVIEW: 395 files (1,685,600,811 bytes)
   📄 analyze_data_quality.py
      Reason: Uncategorized - manual review needed
      Size: 11,848 bytes
   📄 analyze_training_data.py
      Reason: Uncategorized - manual review needed
      Size: 8,015 bytes
   📄 base_knowledge_database.json
      Reason: Uncategorized - manual review needed
      Size: 7,632 bytes
   📄 check_model_status.py
      Reason: Uncategorized - manual review needed
      Size: 3,294 bytes
   📄 COMMIT_SUMMARY.md
      Reason: Uncategorized - manual review needed
      Size: 4,832 bytes
   ... and 390 more files

🎯 CLEANUP RECOMMENDATIONS:

1. SAFE TO REMOVE (immediate action):
   - Python cache files (__pycache__, .pyc)
   - Temporary files
   - Old training checkpoints (keep latest only)

2. ARCHIVE CANDIDATES (move to archive folder):
   - Old backup files from July
   - Historical AI batch results
   - Superseded data files

3. MARK AS LEGACY (update documentation):
   - Round 1 model (17 categories)
   - Superseded training data
   - Old training scripts

4. IMPLEMENTATION STRATEGY:
   a. Create 'archive' directory for historical files
   b. Update FILE_REGISTRY.md with legacy status
   c. Remove safe files (cache, temp)
   d. Move archive candidates to archive/
   e. Document legacy files clearly