#!/usr/bin/env python3
"""
File Cleanup Strategy
Safely identify and categorize files for cleanup based on analysis
"""
import os
import json
from datetime import datetime, timedelta
from pathlib import Path

def analyze_file_usage():
    """Analyze files to determine cleanup strategy"""
    print("🧹 FILE CLEANUP STRATEGY ANALYSIS")
    print("="*50)
    
    cleanup_strategy = {
        'safe_to_remove': [],
        'archive_candidates': [],
        'mark_as_legacy': [],
        'keep_active': [],
        'needs_review': []
    }
    
    # Define cleanup rules
    cleanup_rules = {
        'safe_to_remove': [
            # Temporary and cache files
            {'pattern': '__pycache__', 'reason': 'Python cache directory'},
            {'pattern': '.pyc', 'reason': 'Compiled Python files'},
            {'pattern': '.tmp', 'reason': 'Temporary files'},
            {'pattern': 'checkpoint-', 'reason': 'Training checkpoints (keep latest only)'},
        ],
        'archive_candidates': [
            # Old backup files
            {'pattern': 'backup_2025070', 'reason': 'Old backup files from July'},
            {'pattern': 'ai_batch_results_2025070', 'reason': 'Old AI batch results'},
            {'pattern': 'statements_to_label_backup', 'reason': 'Backup labeling files'},
        ],
        'mark_as_legacy': [
            # Files from previous rounds/versions
            {'pattern': 'enhanced_training_data.csv', 'reason': 'Replaced by complete_28_category_dataset.csv'},
            {'pattern': 'models/bert-test', 'reason': 'Round 1 model (17 categories only)'},
            {'pattern': 'train_deberta.py', 'reason': 'Original training script'},
        ],
        'keep_active': [
            # Critical current files
            {'pattern': 'truth_algorithm.py', 'reason': 'Main algorithm'},
            {'pattern': 'complete_28_category_dataset.csv', 'reason': 'Current training data'},
            {'pattern': 'models/round2-simple', 'reason': 'Current model'},
            {'pattern': 'base_knowledge_db.py', 'reason': 'Data quality tool'},
            {'pattern': 'SESSION_STATUS.md', 'reason': 'Project status'},
        ]
    }
    
    # Scan all files
    all_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            file_path = os.path.relpath(os.path.join(root, file), '.').replace('\\', '/')
            all_files.append(file_path)
    
    print(f"📊 Analyzing {len(all_files)} files...")
    
    # Categorize files
    for file_path in all_files:
        categorized = False
        
        # Check each cleanup rule
        for category, rules in cleanup_rules.items():
            for rule in rules:
                if rule['pattern'] in file_path:
                    cleanup_strategy[category].append({
                        'file': file_path,
                        'reason': rule['reason'],
                        'size': get_file_size(file_path)
                    })
                    categorized = True
                    break
            if categorized:
                break
        
        # If not categorized, needs review
        if not categorized:
            cleanup_strategy['needs_review'].append({
                'file': file_path,
                'reason': 'Uncategorized - manual review needed',
                'size': get_file_size(file_path)
            })
    
    return cleanup_strategy

def get_file_size(file_path):
    """Get file size safely"""
    try:
        return os.path.getsize(file_path)
    except:
        return 0

def generate_cleanup_report(cleanup_strategy):
    """Generate cleanup report"""
    report = []
    report.append("🧹 FILE CLEANUP STRATEGY REPORT")
    report.append("="*50)
    report.append(f"Generated: {datetime.now().isoformat()}")
    report.append("")
    
    total_files = sum(len(files) for files in cleanup_strategy.values())
    report.append(f"📊 TOTAL FILES ANALYZED: {total_files}")
    report.append("")
    
    for category, files in cleanup_strategy.items():
        if files:
            total_size = sum(f['size'] for f in files)
            report.append(f"📁 {category.upper().replace('_', ' ')}: {len(files)} files ({total_size:,} bytes)")
            
            # Show examples
            for file_info in files[:5]:  # Show first 5
                report.append(f"   📄 {file_info['file']}")
                report.append(f"      Reason: {file_info['reason']}")
                report.append(f"      Size: {file_info['size']:,} bytes")
            
            if len(files) > 5:
                report.append(f"   ... and {len(files) - 5} more files")
            report.append("")
    
    # Recommendations
    report.append("🎯 CLEANUP RECOMMENDATIONS:")
    report.append("")
    
    if cleanup_strategy['safe_to_remove']:
        report.append("1. SAFE TO REMOVE (immediate action):")
        report.append("   - Python cache files (__pycache__, .pyc)")
        report.append("   - Temporary files")
        report.append("   - Old training checkpoints (keep latest only)")
        report.append("")
    
    if cleanup_strategy['archive_candidates']:
        report.append("2. ARCHIVE CANDIDATES (move to archive folder):")
        report.append("   - Old backup files from July")
        report.append("   - Historical AI batch results")
        report.append("   - Superseded data files")
        report.append("")
    
    if cleanup_strategy['mark_as_legacy']:
        report.append("3. MARK AS LEGACY (update documentation):")
        report.append("   - Round 1 model (17 categories)")
        report.append("   - Superseded training data")
        report.append("   - Old training scripts")
        report.append("")
    
    report.append("4. IMPLEMENTATION STRATEGY:")
    report.append("   a. Create 'archive' directory for historical files")
    report.append("   b. Update FILE_REGISTRY.md with legacy status")
    report.append("   c. Remove safe files (cache, temp)")
    report.append("   d. Move archive candidates to archive/")
    report.append("   e. Document legacy files clearly")
    
    return '\n'.join(report)

def create_cleanup_script():
    """Create a safe cleanup script"""
    script_content = '''#!/usr/bin/env python3
"""
SAFE FILE CLEANUP SCRIPT
Only removes files that are definitely safe to remove
"""
import os
import shutil
from pathlib import Path

def safe_cleanup():
    """Perform safe cleanup operations"""
    print("🧹 PERFORMING SAFE CLEANUP")
    print("="*30)
    
    removed_count = 0
    
    # Remove __pycache__ directories
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            print(f"Removing: {pycache_path}")
            shutil.rmtree(pycache_path)
            dirs.remove('__pycache__')
            removed_count += 1
    
    # Remove .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                file_path = os.path.join(root, file)
                print(f"Removing: {file_path}")
                os.remove(file_path)
                removed_count += 1
    
    print(f"✅ Cleanup complete! Removed {removed_count} items")

if __name__ == "__main__":
    safe_cleanup()
'''
    
    with open('safe_cleanup.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("📝 Created safe_cleanup.py script")

def main():
    print("🧹 FILE CLEANUP STRATEGY")
    print("="*40)
    
    # Analyze files
    cleanup_strategy = analyze_file_usage()
    
    # Generate report
    report = generate_cleanup_report(cleanup_strategy)
    
    # Save report
    with open('file_cleanup_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    # Create safe cleanup script
    create_cleanup_script()
    
    # Save strategy as JSON for programmatic use
    with open('cleanup_strategy.json', 'w', encoding='utf-8') as f:
        json.dump(cleanup_strategy, f, indent=2)
    
    print("✅ Cleanup analysis complete!")
    print("📄 Files generated:")
    print("   - file_cleanup_report.txt")
    print("   - cleanup_strategy.json")
    print("   - safe_cleanup.py")
    
    # Summary
    total_safe = len(cleanup_strategy['safe_to_remove'])
    total_archive = len(cleanup_strategy['archive_candidates'])
    total_legacy = len(cleanup_strategy['mark_as_legacy'])
    
    print(f"\n📊 SUMMARY:")
    print(f"   Safe to remove: {total_safe} files")
    print(f"   Archive candidates: {total_archive} files")
    print(f"   Mark as legacy: {total_legacy} files")
    print(f"   Keep active: {len(cleanup_strategy['keep_active'])} files")

if __name__ == "__main__":
    main()
