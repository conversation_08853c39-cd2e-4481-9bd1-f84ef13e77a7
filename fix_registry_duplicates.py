#!/usr/bin/env python3
"""
Fix duplicates and ensure complete documentation in FILE_REGISTRY.md
"""
import re
from collections import defaultdict

def analyze_registry_duplicates():
    """Find duplicate entries in FILE_REGISTRY.md"""
    print("🔍 ANALYZING FILE_REGISTRY.md FOR DUPLICATES")
    print("="*50)
    
    try:
        with open('docs/FILE_REGISTRY.md', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ FILE_REGISTRY.md not found!")
        return None
    
    lines = content.split('\n')
    
    # Find all file entries
    file_entries = {}
    duplicates = []
    
    for i, line in enumerate(lines):
        # Look for file entries
        if '**File**:' in line or '**Directory**:' in line:
            # Extract the file/directory name
            match = re.search(r'`([^`]+)`', line)
            if match:
                file_path = match.group(1)
                
                if file_path in file_entries:
                    duplicates.append({
                        'file': file_path,
                        'first_line': file_entries[file_path],
                        'duplicate_line': i + 1,
                        'first_content': lines[file_entries[file_path] - 1],
                        'duplicate_content': line
                    })
                else:
                    file_entries[file_path] = i + 1
    
    print(f"📊 ANALYSIS RESULTS:")
    print(f"   Total file entries found: {len(file_entries)}")
    print(f"   Duplicate entries found: {len(duplicates)}")
    
    if duplicates:
        print(f"\n🚨 DUPLICATES FOUND:")
        for dup in duplicates:
            print(f"\n   File: {dup['file']}")
            print(f"   First occurrence: Line {dup['first_line']}")
            print(f"   Duplicate: Line {dup['duplicate_line']}")
            print(f"   First: {dup['first_content'][:80]}...")
            print(f"   Duplicate: {dup['duplicate_content'][:80]}...")
    else:
        print(f"\n✅ NO DUPLICATES FOUND")
    
    return duplicates, file_entries, content

def find_missing_important_files():
    """Find important files that should be documented"""
    important_files = [
        # Cleanup and maintenance tools
        'validate_file_registry.py',
        'maintain_file_registry.py', 
        'file_cleanup_strategy.py',
        'implement_cleanup.py',
        'safe_cleanup.py',
        'fix_registry_duplicates.py',
        
        # Results and reports
        'file_maintenance_report.txt',
        'registry_update.md',
        'file_cleanup_report.txt',
        'cleanup_summary.json',
        'legacy_files_documentation.md',
        
        # Archive directory
        'archive/',
        
        # Recent additions
        'COMMIT_SUMMARY.md',
        
        # Data quality tools
        'quick_round2_test.py',
        'test_simple_statement.json',
    ]
    
    # Check which are missing from registry
    try:
        with open('docs/FILE_REGISTRY.md', 'r', encoding='utf-8') as f:
            registry_content = f.read()
    except FileNotFoundError:
        return important_files
    
    missing = []
    for file_path in important_files:
        if file_path not in registry_content:
            missing.append(file_path)
    
    return missing

def create_registry_additions(missing_files):
    """Create registry entries for missing files"""
    additions = []
    additions.append("\n## 📁 **ADDITIONAL TOOLS & RESULTS (AUTO-DETECTED)**\n")
    
    # Group files by category
    categories = {
        'Registry Maintenance': [
            'validate_file_registry.py',
            'maintain_file_registry.py',
            'fix_registry_duplicates.py'
        ],
        'Cleanup Tools': [
            'file_cleanup_strategy.py', 
            'implement_cleanup.py',
            'safe_cleanup.py'
        ],
        'Generated Reports': [
            'file_maintenance_report.txt',
            'registry_update.md',
            'file_cleanup_report.txt',
            'cleanup_summary.json',
            'legacy_files_documentation.md'
        ],
        'Archive & Organization': [
            'archive/'
        ],
        'Additional Documentation': [
            'COMMIT_SUMMARY.md'
        ],
        'Testing Utilities': [
            'quick_round2_test.py',
            'test_simple_statement.json'
        ]
    }
    
    for category, files in categories.items():
        category_files = [f for f in files if f in missing_files]
        if category_files:
            additions.append(f"### **{category}**\n")
            
            for file_path in category_files:
                if file_path.endswith('.py'):
                    status = "✅ **ACTIVE**"
                    purpose = "Auto-detected Python script"
                elif file_path.endswith('.json'):
                    status = "📊 **RESULTS**"
                    purpose = "Auto-detected results file"
                elif file_path.endswith('.txt') or file_path.endswith('.md'):
                    status = "📄 **DOCUMENTATION**"
                    purpose = "Auto-detected documentation"
                elif file_path.endswith('/'):
                    status = "📁 **DIRECTORY**"
                    purpose = "Auto-detected directory"
                else:
                    status = "📄 **FILE**"
                    purpose = "Auto-detected file"
                
                additions.append(f"- **File**: `{file_path}` {status}")
                additions.append(f"- **Purpose**: {purpose} - needs manual description")
                additions.append(f"- **Status**: Recently added, requires documentation update")
                additions.append("")
    
    return '\n'.join(additions)

def main():
    print("🔧 FIXING FILE_REGISTRY.md DUPLICATES AND COMPLETENESS")
    print("="*60)
    
    # Analyze duplicates
    result = analyze_registry_duplicates()
    if result is None:
        return 1
    
    duplicates, file_entries, content = result
    
    # Find missing important files
    missing_files = find_missing_important_files()
    
    print(f"\n📋 MISSING IMPORTANT FILES:")
    print(f"   Found {len(missing_files)} missing files:")
    for file_path in missing_files[:10]:  # Show first 10
        print(f"   📄 {file_path}")
    if len(missing_files) > 10:
        print(f"   ... and {len(missing_files) - 10} more")
    
    # Create additions for missing files
    if missing_files:
        additions = create_registry_additions(missing_files)
        
        with open('registry_additions.md', 'w', encoding='utf-8') as f:
            f.write("# Registry Additions\n")
            f.write("*Auto-generated by fix_registry_duplicates.py*\n")
            f.write(additions)
        
        print(f"\n📝 Created registry_additions.md with {len(missing_files)} missing files")
    
    # Summary and recommendations
    print(f"\n🎯 RECOMMENDATIONS:")
    
    if duplicates:
        print(f"1. 🚨 REMOVE DUPLICATES: {len(duplicates)} duplicate entries found")
        print(f"   - Manually review and remove duplicate lines")
        print(f"   - Keep the most complete/accurate version")
    else:
        print(f"1. ✅ NO DUPLICATES: Registry is clean")
    
    if missing_files:
        print(f"2. 📝 ADD MISSING FILES: {len(missing_files)} important files not documented")
        print(f"   - Review registry_additions.md")
        print(f"   - Add important files to FILE_REGISTRY.md")
        print(f"   - Update descriptions manually")
    else:
        print(f"2. ✅ COMPLETE: All important files documented")
    
    print(f"\n📊 REGISTRY STATUS:")
    print(f"   Current entries: {len(file_entries)}")
    print(f"   Duplicates: {len(duplicates)}")
    print(f"   Missing important files: {len(missing_files)}")
    
    if not duplicates and not missing_files:
        print(f"\n🎉 FILE_REGISTRY.md IS COMPLETE AND ACCURATE!")
    else:
        print(f"\n⚠️ FILE_REGISTRY.md NEEDS UPDATES")
    
    return 0

if __name__ == "__main__":
    exit(main())
