#!/usr/bin/env python3
"""
Implement Cleanup Strategy
Safely implement the cleanup recommendations with proper documentation
"""
import os
import shutil
import json
from datetime import datetime
from pathlib import Path

def create_archive_directory():
    """Create archive directory for historical files"""
    archive_dir = Path("archive")
    if not archive_dir.exists():
        archive_dir.mkdir()
        print(f"✅ Created archive directory: {archive_dir}")
    
    # Create subdirectories
    subdirs = ["old_backups", "ai_batch_results", "deprecated_models", "legacy_data"]
    for subdir in subdirs:
        subdir_path = archive_dir / subdir
        if not subdir_path.exists():
            subdir_path.mkdir()
            print(f"✅ Created archive subdirectory: {subdir_path}")
    
    return archive_dir

def load_cleanup_strategy():
    """Load the cleanup strategy from JSON"""
    try:
        with open('cleanup_strategy.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ cleanup_strategy.json not found. Run file_cleanup_strategy.py first.")
        return None

def archive_old_files(cleanup_strategy, archive_dir):
    """Archive old backup files and AI batch results"""
    print("\n📦 ARCHIVING OLD FILES")
    print("="*30)
    
    archived_count = 0
    archived_size = 0
    
    archive_candidates = cleanup_strategy.get('archive_candidates', [])
    
    for file_info in archive_candidates:
        file_path = Path(file_info['file'])
        
        if file_path.exists():
            # Determine archive subdirectory
            if 'ai_batch_results' in file_path.name:
                dest_dir = archive_dir / "ai_batch_results"
            elif 'backup' in file_path.name:
                dest_dir = archive_dir / "old_backups"
            else:
                dest_dir = archive_dir / "legacy_data"
            
            dest_path = dest_dir / file_path.name
            
            try:
                shutil.move(str(file_path), str(dest_path))
                print(f"📦 Archived: {file_path} → {dest_path}")
                archived_count += 1
                archived_size += file_info['size']
            except Exception as e:
                print(f"❌ Failed to archive {file_path}: {e}")
    
    print(f"\n✅ Archived {archived_count} files ({archived_size:,} bytes)")
    return archived_count, archived_size

def remove_old_checkpoints():
    """Remove old training checkpoints (keep latest only)"""
    print("\n🗑️ REMOVING OLD TRAINING CHECKPOINTS")
    print("="*40)
    
    removed_count = 0
    removed_size = 0
    
    # Find checkpoint directories
    checkpoint_dirs = []
    for root, dirs, files in os.walk('models'):
        for dir_name in dirs:
            if dir_name.startswith('checkpoint-'):
                checkpoint_dirs.append(Path(root) / dir_name)
    
    if not checkpoint_dirs:
        print("✅ No old checkpoints found")
        return 0, 0
    
    # Group by parent directory and keep only the latest
    checkpoint_groups = {}
    for checkpoint_dir in checkpoint_dirs:
        parent = checkpoint_dir.parent
        if parent not in checkpoint_groups:
            checkpoint_groups[parent] = []
        checkpoint_groups[parent].append(checkpoint_dir)
    
    for parent, checkpoints in checkpoint_groups.items():
        if len(checkpoints) > 1:
            # Sort by checkpoint number and keep the latest
            checkpoints.sort(key=lambda x: int(x.name.split('-')[1]))
            checkpoints_to_remove = checkpoints[:-1]  # Remove all but the latest
            
            for checkpoint_dir in checkpoints_to_remove:
                try:
                    # Calculate size before removal
                    dir_size = sum(f.stat().st_size for f in checkpoint_dir.rglob('*') if f.is_file())
                    
                    shutil.rmtree(checkpoint_dir)
                    print(f"🗑️ Removed: {checkpoint_dir} ({dir_size:,} bytes)")
                    removed_count += 1
                    removed_size += dir_size
                except Exception as e:
                    print(f"❌ Failed to remove {checkpoint_dir}: {e}")
    
    print(f"\n✅ Removed {removed_count} old checkpoints ({removed_size:,} bytes)")
    return removed_count, removed_size

def update_legacy_documentation(cleanup_strategy):
    """Update FILE_REGISTRY.md to mark legacy files"""
    print("\n📝 UPDATING LEGACY DOCUMENTATION")
    print("="*35)
    
    legacy_files = cleanup_strategy.get('mark_as_legacy', [])
    
    # Create legacy entries for FILE_REGISTRY.md
    legacy_entries = []
    legacy_entries.append("\n### **Legacy Files (Deprecated)**\n")
    
    for file_info in legacy_files:
        file_path = file_info['file']
        reason = file_info['reason']
        
        if file_path.endswith('.csv'):
            status = "❌ **DEPRECATED**"
        elif 'models/' in file_path:
            status = "🏛️ **LEGACY**"
        else:
            status = "📜 **HISTORICAL**"
        
        legacy_entries.append(f"- **File**: `{file_path}` {status}")
        legacy_entries.append(f"- **Purpose**: {reason}")
        legacy_entries.append(f"- **Status**: Legacy - superseded by newer implementation")
        legacy_entries.append("")
    
    # Save legacy documentation
    legacy_content = '\n'.join(legacy_entries)
    with open('legacy_files_documentation.md', 'w', encoding='utf-8') as f:
        f.write("# Legacy Files Documentation\n")
        f.write("*Auto-generated by implement_cleanup.py*\n")
        f.write(legacy_content)
    
    print(f"✅ Created legacy_files_documentation.md")
    print(f"📋 Documented {len(legacy_files)} legacy files")

def create_cleanup_summary(archived_count, archived_size, removed_count, removed_size):
    """Create cleanup summary report"""
    summary = {
        "cleanup_date": datetime.now().isoformat(),
        "actions_performed": {
            "archived_files": {
                "count": archived_count,
                "size_bytes": archived_size,
                "location": "archive/"
            },
            "removed_checkpoints": {
                "count": removed_count,
                "size_bytes": removed_size,
                "reason": "Old training checkpoints (kept latest only)"
            }
        },
        "space_saved": archived_size + removed_size,
        "files_preserved": {
            "critical_files": "All critical files preserved",
            "current_model": "models/round2-simple/ (active)",
            "training_data": "data/complete_28_category_dataset.csv (active)",
            "documentation": "All documentation preserved"
        },
        "next_steps": [
            "Review legacy_files_documentation.md",
            "Update FILE_REGISTRY.md with legacy status",
            "Proceed with data validation"
        ]
    }
    
    with open('cleanup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)
    
    return summary

def main():
    print("🧹 IMPLEMENTING CLEANUP STRATEGY")
    print("="*40)
    print(f"Started: {datetime.now().isoformat()}")
    print()
    
    # Load cleanup strategy
    cleanup_strategy = load_cleanup_strategy()
    if not cleanup_strategy:
        return 1
    
    # Create archive directory
    archive_dir = create_archive_directory()
    
    # Archive old files
    archived_count, archived_size = archive_old_files(cleanup_strategy, archive_dir)
    
    # Remove old checkpoints
    removed_count, removed_size = remove_old_checkpoints()
    
    # Update legacy documentation
    update_legacy_documentation(cleanup_strategy)
    
    # Create summary
    summary = create_cleanup_summary(archived_count, archived_size, removed_count, removed_size)
    
    # Final report
    print("\n" + "="*50)
    print("🎉 CLEANUP IMPLEMENTATION COMPLETE")
    print("="*50)
    
    total_space_saved = summary['space_saved']
    print(f"📊 SUMMARY:")
    print(f"   Files archived: {archived_count} ({archived_size:,} bytes)")
    print(f"   Checkpoints removed: {removed_count} ({removed_size:,} bytes)")
    print(f"   Total space saved: {total_space_saved:,} bytes ({total_space_saved/1024/1024:.1f} MB)")
    
    print(f"\n📄 FILES CREATED:")
    print(f"   - archive/ (directory with organized old files)")
    print(f"   - legacy_files_documentation.md")
    print(f"   - cleanup_summary.json")
    
    print(f"\n✅ PROJECT STATE:")
    print(f"   - All critical files preserved")
    print(f"   - Old files safely archived")
    print(f"   - Legacy files documented")
    print(f"   - Ready for data validation")
    
    return 0

if __name__ == "__main__":
    exit(main())
