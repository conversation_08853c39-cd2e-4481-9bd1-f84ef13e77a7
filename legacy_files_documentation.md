# Legacy Files Documentation
*Auto-generated by implement_cleanup.py*

### **Legacy Files (Deprecated)**

- **File**: `data/enhanced_training_data.csv` ❌ **DEPRECATED**
- **Purpose**: Replaced by complete_28_category_dataset.csv
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/train_deberta.py` 🏛️ **LEGACY**
- **Purpose**: Original training script
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/config.json` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/label_map.json` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/model.safetensors` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/special_tokens_map.json` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/threshold_config.json` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/tokenizer.json` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/tokenizer_config.json` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/training_args.bin` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation

- **File**: `models/bert-test/vocab.txt` 🏛️ **LEGACY**
- **Purpose**: Round 1 model (17 categories only)
- **Status**: Legacy - superseded by newer implementation
