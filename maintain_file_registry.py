#!/usr/bin/env python3
"""
Automated File Registry Maintenance Tool
- Automatically updates FILE_REGISTRY.md with new files
- Identifies files for potential cleanup
- Categorizes files by purpose and status
"""
import os
import re
import json
from datetime import datetime
from pathlib import Path

class FileRegistryMaintainer:
    def __init__(self):
        self.registry_path = 'docs/FILE_REGISTRY.md'
        self.ignore_patterns = [
            r'__pycache__',
            r'\.pyc$',
            r'\.git',
            r'checkpoint-\d+',
            r'\.log$',
            r'\.tmp$'
        ]
        
        # File categories for automatic classification
        self.file_categories = {
            'core_algorithm': {
                'patterns': [r'truth_algorithm\.py', r'pipeline/.*\.py', r'models/.*\.py', r'rules/.*\.py'],
                'status': 'ACTIVE'
            },
            'data_quality': {
                'patterns': [r'base_knowledge.*\.py', r'nuanced_evaluation.*\.py', r'analyze_data.*\.py', r'manual_.*review.*\.py'],
                'status': 'ACTIVE'
            },
            'training_scripts': {
                'patterns': [r'train_.*\.py', r'simple_round.*\.py', r'fix_.*categories.*\.py'],
                'status': 'ACTIVE'
            },
            'testing': {
                'patterns': [r'test_.*\.py', r'check_.*\.py', r'monitor_.*\.py', r'validate_.*\.py'],
                'status': 'ACTIVE'
            },
            'data_files': {
                'patterns': [r'data/.*\.csv', r'.*\.json$'],
                'status': 'VARIES'
            },
            'documentation': {
                'patterns': [r'docs/.*\.md', r'.*\.md$', r'docs/.*\.txt'],
                'status': 'ACTIVE'
            },
            'utilities': {
                'patterns': [r'utils/.*\.py'],
                'status': 'UTILITY'
            },
            'legacy_data': {
                'patterns': [r'data/.*backup.*', r'data/ai_batch_results.*', r'.*_backup_.*'],
                'status': 'LEGACY'
            },
            'results': {
                'patterns': [r'.*_results\.json', r'.*_analysis\.json', r'.*_report\.json'],
                'status': 'RESULTS'
            }
        }
    
    def scan_directory(self):
        """Scan directory and categorize all files"""
        all_files = {}
        
        for root, dirs, files in os.walk('.'):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if not any(re.search(pattern, d) for pattern in self.ignore_patterns)]
            
            for file in files:
                # Skip ignored files
                if any(re.search(pattern, file) for pattern in self.ignore_patterns):
                    continue
                
                rel_path = os.path.relpath(os.path.join(root, file), '.').replace('\\', '/')
                file_info = self.categorize_file(rel_path)
                all_files[rel_path] = file_info
        
        return all_files
    
    def categorize_file(self, file_path):
        """Categorize a file based on patterns"""
        file_info = {
            'path': file_path,
            'category': 'uncategorized',
            'status': 'UNKNOWN',
            'size': 0,
            'modified': None
        }
        
        try:
            stat = os.stat(file_path)
            file_info['size'] = stat.st_size
            file_info['modified'] = datetime.fromtimestamp(stat.st_mtime).isoformat()
        except:
            pass
        
        # Categorize based on patterns
        for category, config in self.file_categories.items():
            if any(re.search(pattern, file_path) for pattern in config['patterns']):
                file_info['category'] = category
                file_info['status'] = config['status']
                break
        
        return file_info
    
    def identify_cleanup_candidates(self, all_files):
        """Identify files that might be candidates for cleanup"""
        cleanup_candidates = {
            'definitely_safe_to_remove': [],
            'probably_safe_to_remove': [],
            'review_needed': [],
            'keep_but_document_as_legacy': []
        }
        
        for file_path, file_info in all_files.items():
            # Definitely safe to remove
            if any(pattern in file_path for pattern in ['__pycache__', '.pyc', '.tmp']):
                cleanup_candidates['definitely_safe_to_remove'].append(file_path)
            
            # Probably safe to remove (old backups, temp results)
            elif file_info['category'] == 'legacy_data' and 'backup' in file_path:
                if file_info['size'] < 1000000:  # Less than 1MB
                    cleanup_candidates['probably_safe_to_remove'].append(file_path)
                else:
                    cleanup_candidates['review_needed'].append(file_path)
            
            # Keep but document as legacy
            elif file_info['category'] == 'legacy_data':
                cleanup_candidates['keep_but_document_as_legacy'].append(file_path)
            
            # Old result files
            elif file_info['category'] == 'results' and file_info['modified']:
                # If older than 7 days and small
                mod_date = datetime.fromisoformat(file_info['modified'])
                days_old = (datetime.now() - mod_date).days
                if days_old > 7 and file_info['size'] < 100000:  # Less than 100KB
                    cleanup_candidates['probably_safe_to_remove'].append(file_path)
        
        return cleanup_candidates
    
    def generate_registry_update(self, all_files):
        """Generate updated registry content for new files"""
        current_registry = self.load_current_registry()
        documented_files = self.extract_documented_files(current_registry)
        
        new_files = []
        for file_path, file_info in all_files.items():
            if file_path not in documented_files and file_info['category'] != 'uncategorized':
                new_files.append((file_path, file_info))
        
        if not new_files:
            return None
        
        # Group new files by category
        new_by_category = {}
        for file_path, file_info in new_files:
            category = file_info['category']
            if category not in new_by_category:
                new_by_category[category] = []
            new_by_category[category].append((file_path, file_info))
        
        # Generate registry entries
        registry_additions = []
        registry_additions.append("## 📁 **NEW FILES DETECTED**\n")
        registry_additions.append("*Auto-generated by maintain_file_registry.py*\n")
        
        for category, files in new_by_category.items():
            category_title = category.replace('_', ' ').title()
            registry_additions.append(f"### **{category_title}**\n")
            
            for file_path, file_info in files:
                status_icon = "✅" if file_info['status'] == 'ACTIVE' else "📁"
                registry_additions.append(f"- **File**: `{file_path}` {status_icon} **{file_info['status']}**")
                registry_additions.append(f"- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]")
                registry_additions.append(f"- **Category**: {category}")
                registry_additions.append(f"- **Size**: {file_info['size']} bytes")
                registry_additions.append(f"- **Status**: Newly detected, needs documentation\n")
        
        return '\n'.join(registry_additions)
    
    def load_current_registry(self):
        """Load current registry content"""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            return ""
    
    def extract_documented_files(self, registry_content):
        """Extract list of files already documented in registry"""
        documented = set()
        
        # Look for file patterns in registry
        patterns = [
            r'\*\*File\*\*:\s*`([^`]+)`',
            r'\*\*Directory\*\*:\s*`([^`]+)`',
            r'`([^`]+\.py)`',
            r'`([^`]+\.csv)`',
            r'`([^`]+\.json)`',
            r'`([^`]+\.md)`',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, registry_content)
            for match in matches:
                if match and not match.startswith('python '):
                    documented.add(match.strip())
        
        return documented
    
    def generate_report(self, all_files, cleanup_candidates, registry_update):
        """Generate comprehensive maintenance report"""
        report = []
        report.append("🔧 FILE REGISTRY MAINTENANCE REPORT")
        report.append("=" * 50)
        report.append(f"Generated: {datetime.now().isoformat()}")
        report.append(f"Total files scanned: {len(all_files)}")
        report.append("")
        
        # Category breakdown
        category_counts = {}
        for file_info in all_files.values():
            category = file_info['category']
            category_counts[category] = category_counts.get(category, 0) + 1
        
        report.append("📊 FILES BY CATEGORY:")
        for category, count in sorted(category_counts.items()):
            report.append(f"   {category}: {count} files")
        report.append("")
        
        # Cleanup recommendations
        report.append("🧹 CLEANUP RECOMMENDATIONS:")
        for cleanup_type, files in cleanup_candidates.items():
            if files:
                report.append(f"\n{cleanup_type.replace('_', ' ').title()}: {len(files)} files")
                for file_path in files[:5]:  # Show first 5
                    report.append(f"   - {file_path}")
                if len(files) > 5:
                    report.append(f"   ... and {len(files) - 5} more")
        
        # New files to document
        if registry_update:
            report.append("\n📝 NEW FILES NEED DOCUMENTATION:")
            report.append("   See registry_update.md for details")
        else:
            report.append("\n✅ NO NEW FILES TO DOCUMENT")
        
        return '\n'.join(report)

def main():
    print("🔧 AUTOMATED FILE REGISTRY MAINTENANCE")
    print("=" * 50)
    
    maintainer = FileRegistryMaintainer()
    
    # Scan directory
    print("📂 Scanning directory structure...")
    all_files = maintainer.scan_directory()
    print(f"   Found {len(all_files)} files")
    
    # Identify cleanup candidates
    print("🧹 Identifying cleanup candidates...")
    cleanup_candidates = maintainer.identify_cleanup_candidates(all_files)
    total_cleanup = sum(len(files) for files in cleanup_candidates.values())
    print(f"   Found {total_cleanup} files for potential cleanup")
    
    # Generate registry update
    print("📝 Checking for new files to document...")
    registry_update = maintainer.generate_registry_update(all_files)
    if registry_update:
        with open('registry_update.md', 'w', encoding='utf-8') as f:
            f.write(registry_update)
        print("   ✅ New files found - registry_update.md created")
    else:
        print("   ✅ No new files to document")
    
    # Generate report
    print("📊 Generating maintenance report...")
    report = maintainer.generate_report(all_files, cleanup_candidates, registry_update)
    
    with open('file_maintenance_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ Maintenance complete!")
    print("📄 Reports generated:")
    print("   - file_maintenance_report.txt")
    if registry_update:
        print("   - registry_update.md")
    
    # Show summary
    print(f"\n📊 SUMMARY:")
    print(f"   Total files: {len(all_files)}")
    print(f"   Cleanup candidates: {total_cleanup}")
    print(f"   New files to document: {'Yes' if registry_update else 'No'}")

if __name__ == "__main__":
    main()
