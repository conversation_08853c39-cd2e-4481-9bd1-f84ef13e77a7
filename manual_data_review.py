#!/usr/bin/env python3
"""
Manual Data Review Tool
Review training data quality against <PERSON><PERSON> definitions
Focus on accuracy and nuance
"""
import pandas as pd
import json
from base_knowledge_db import BaseKnowledgeDB

def load_lrh_definitions():
    """Load core <PERSON><PERSON> definitions"""
    return {
        "OMITTED_DATA_OUT": "Missing crucial information, vague statements",
        "CONTRARY_FACTS_OUT": "Two contradictory facts that can't both be true", 
        "FALSEHOOD_OUT": "Demonstrably untrue statements",
        "WRONG_SOURCE_OUT": "Unreliable or vague attribution",
        "DROPPED_TIME_OUT": "Missing timestamps when time is relevant",
        "ALTERED_IMPORTANCE_OUT": "Exaggerated or minimized claims",
        
        "DATA_PROVEN_FACTUAL_PLUS": "Verified, factual information",
        "CORRECT_SOURCE_PLUS": "Reliable, properly attributed sources",
        "TIME_NOTED_PLUS": "Proper timestamps provided",
        "ADEQUATE_DATA_PLUS": "Sufficient information for understanding"
    }

def review_sample_statements():
    """Review a sample of statements for quality"""
    print("🔍 MANUAL DATA REVIEW - SAMPLE STATEMENTS")
    print("="*60)
    
    # Load current data
    df = pd.read_csv("data/complete_28_category_dataset.csv")
    labeled_df = df[df['label'].notna() & (df['label'] != '')]
    
    # Initialize knowledge database
    knowledge_db = BaseKnowledgeDB()
    definitions = load_lrh_definitions()
    
    print(f"📊 Total labeled statements: {len(labeled_df)}")
    print(f"🎯 Reviewing sample for quality assessment")
    
    # Sample statements for review
    sample_indices = [0, 10, 20, 30, 40]  # Review every 10th statement
    
    review_results = []
    
    for i, idx in enumerate(sample_indices):
        if idx >= len(labeled_df):
            continue
            
        row = labeled_df.iloc[idx]
        text = row['text']
        current_labels = row['label'].split(',') if row['label'] else []
        current_labels = [label.strip() for label in current_labels]
        
        print(f"\n" + "="*80)
        print(f"REVIEW {i+1}/5 - Statement Index {idx}")
        print(f"TEXT: {text}")
        print(f"CURRENT LABELS: {', '.join(current_labels)}")
        print("="*80)
        
        # Check against base knowledge
        contradictions = knowledge_db.check_contradiction(text)
        
        print(f"\n🔍 BASE KNOWLEDGE CHECK:")
        if contradictions:
            for contradiction in contradictions:
                print(f"   ⚠️ {contradiction['type']}: {contradiction['fact']}")
                if contradiction.get('needs_context'):
                    print(f"   🔍 Context needed: {contradiction['needs_context']}")
        else:
            print(f"   ✅ No base knowledge contradictions")
        
        # Manual evaluation
        print(f"\n📋 MANUAL EVALUATION:")
        
        # Check each current label
        evaluation = {
            "correct_labels": [],
            "questionable_labels": [],
            "missing_labels": [],
            "overall_quality": "unknown"
        }
        
        for label in current_labels:
            if label in definitions:
                definition = definitions[label]
                print(f"\n   {label}:")
                print(f"   Definition: {definition}")
                
                # Simple heuristic checks
                if label == "OMITTED_DATA_OUT":
                    if any(term in text.lower() for term in ["sources say", "reportedly", "some people"]):
                        evaluation["correct_labels"].append(label)
                        print(f"   ✅ CORRECT - Contains vague attribution")
                    else:
                        evaluation["questionable_labels"].append(label)
                        print(f"   ❓ QUESTIONABLE - No obvious omitted data")
                
                elif label == "CONTRARY_FACTS_OUT":
                    if contradictions:
                        evaluation["correct_labels"].append(label)
                        print(f"   ✅ CORRECT - Base knowledge contradiction found")
                    elif "but" in text.lower() or "however" in text.lower():
                        evaluation["correct_labels"].append(label)
                        print(f"   ✅ CORRECT - Contains contradictory elements")
                    else:
                        evaluation["questionable_labels"].append(label)
                        print(f"   ❓ QUESTIONABLE - No obvious contradiction")
                
                elif label == "DATA_PROVEN_FACTUAL_PLUS":
                    if any(term in text.lower() for term in ["according to", "study by", "official"]):
                        evaluation["correct_labels"].append(label)
                        print(f"   ✅ CORRECT - Contains factual attribution")
                    else:
                        evaluation["questionable_labels"].append(label)
                        print(f"   ❓ QUESTIONABLE - No clear factual indicators")
                
                elif label == "TIME_NOTED_PLUS":
                    if any(term in text for term in ["2024", "2023", "January", "March", "PM", "AM"]):
                        evaluation["correct_labels"].append(label)
                        print(f"   ✅ CORRECT - Contains time reference")
                    else:
                        evaluation["questionable_labels"].append(label)
                        print(f"   ❓ QUESTIONABLE - No clear time reference")
                
                else:
                    evaluation["correct_labels"].append(label)
                    print(f"   ➡️ ASSUMED CORRECT - Manual review needed")
            
            else:
                evaluation["questionable_labels"].append(label)
                print(f"   ❌ UNKNOWN LABEL: {label}")
        
        # Check for missing obvious labels
        text_lower = text.lower()
        
        if any(term in text_lower for term in ["sources say", "reportedly"]) and "OMITTED_DATA_OUT" not in current_labels:
            evaluation["missing_labels"].append("OMITTED_DATA_OUT")
            print(f"\n   ❌ MISSING: OMITTED_DATA_OUT (vague attribution)")
        
        if any(term in text_lower for term in ["according to", "study by"]) and "CORRECT_SOURCE_PLUS" not in current_labels:
            evaluation["missing_labels"].append("CORRECT_SOURCE_PLUS")
            print(f"\n   ❌ MISSING: CORRECT_SOURCE_PLUS (good attribution)")
        
        # Overall quality assessment
        correct_count = len(evaluation["correct_labels"])
        questionable_count = len(evaluation["questionable_labels"])
        missing_count = len(evaluation["missing_labels"])
        
        if questionable_count == 0 and missing_count == 0:
            evaluation["overall_quality"] = "GOOD"
        elif questionable_count <= 1 and missing_count <= 1:
            evaluation["overall_quality"] = "FAIR"
        else:
            evaluation["overall_quality"] = "POOR"
        
        print(f"\n🎯 QUALITY ASSESSMENT: {evaluation['overall_quality']}")
        print(f"   Correct labels: {correct_count}")
        print(f"   Questionable labels: {questionable_count}")
        print(f"   Missing labels: {missing_count}")
        
        review_results.append({
            "index": idx,
            "text": text,
            "current_labels": current_labels,
            "evaluation": evaluation
        })
    
    # Summary
    print(f"\n" + "="*80)
    print(f"REVIEW SUMMARY")
    print(f"="*80)
    
    quality_counts = {"GOOD": 0, "FAIR": 0, "POOR": 0}
    total_questionable = 0
    total_missing = 0
    
    for result in review_results:
        quality = result["evaluation"]["overall_quality"]
        quality_counts[quality] += 1
        total_questionable += len(result["evaluation"]["questionable_labels"])
        total_missing += len(result["evaluation"]["missing_labels"])
    
    print(f"📊 Quality Distribution:")
    for quality, count in quality_counts.items():
        percentage = count / len(review_results) * 100
        print(f"   {quality}: {count}/{len(review_results)} ({percentage:.1f}%)")
    
    print(f"\n🚨 Issues Found:")
    print(f"   Questionable labels: {total_questionable}")
    print(f"   Missing labels: {total_missing}")
    
    estimated_accuracy = (quality_counts["GOOD"] + quality_counts["FAIR"] * 0.5) / len(review_results)
    print(f"\n🎯 Estimated Data Quality: {estimated_accuracy*100:.1f}%")
    
    return review_results

def create_improvement_recommendations(review_results):
    """Create specific recommendations for data improvement"""
    print(f"\n🎯 DATA IMPROVEMENT RECOMMENDATIONS")
    print("="*50)
    
    # Analyze common issues
    common_issues = {
        "missing_omitted_data": 0,
        "missing_source_attribution": 0,
        "questionable_contradictions": 0,
        "unknown_labels": 0
    }
    
    for result in review_results:
        evaluation = result["evaluation"]
        
        if "OMITTED_DATA_OUT" in evaluation["missing_labels"]:
            common_issues["missing_omitted_data"] += 1
        
        if "CORRECT_SOURCE_PLUS" in evaluation["missing_labels"]:
            common_issues["missing_source_attribution"] += 1
        
        if "CONTRARY_FACTS_OUT" in evaluation["questionable_labels"]:
            common_issues["questionable_contradictions"] += 1
    
    print(f"📋 Priority Fixes:")
    
    if common_issues["missing_omitted_data"] > 0:
        print(f"1. 🔍 Add OMITTED_DATA_OUT for vague statements ({common_issues['missing_omitted_data']} cases)")
        print(f"   Look for: 'sources say', 'reportedly', 'some people'")
    
    if common_issues["missing_source_attribution"] > 0:
        print(f"2. ✅ Add CORRECT_SOURCE_PLUS for good attribution ({common_issues['missing_source_attribution']} cases)")
        print(f"   Look for: 'according to', 'study by', 'official report'")
    
    if common_issues["questionable_contradictions"] > 0:
        print(f"3. ❓ Review CONTRARY_FACTS_OUT labels ({common_issues['questionable_contradictions']} cases)")
        print(f"   Ensure real contradictions, not just contrasts")
    
    print(f"\n🎯 RECOMMENDED PROCESS:")
    print(f"1. Use base knowledge database for contradiction detection")
    print(f"2. Apply L. Ron Hubbard definitions strictly")
    print(f"3. Focus on obvious cases first (vague sources, clear contradictions)")
    print(f"4. Review questionable labels manually")
    print(f"5. Create corrected dataset incrementally")

def main():
    print("🔍 MANUAL DATA REVIEW TOOL")
    print("="*40)
    print("Reviewing training data quality against L. Ron Hubbard methodology")
    print()
    
    # Perform sample review
    review_results = review_sample_statements()
    
    # Create recommendations
    create_improvement_recommendations(review_results)
    
    # Save results
    with open('manual_review_results.json', 'w') as f:
        json.dump(review_results, f, indent=2)
    
    print(f"\n💾 Review results saved to: manual_review_results.json")
    print(f"\n✅ Manual review complete!")
    print(f"🎯 Use these insights to improve data quality before Round 3 training")

if __name__ == "__main__":
    main()
