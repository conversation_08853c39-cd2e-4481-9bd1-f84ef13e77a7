#!/usr/bin/env python3
"""
Error Analysis for DeBERTa Classifier - Round 2 Improvements
Analyzes model performance and identifies improvement opportunities
"""
import pandas as pd
import numpy as np
import json
import argparse
from pathlib import Path
from collections import defaultdict, Counter
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification

def load_model_and_data(model_dir, test_file):
    """Load the trained model and test data"""
    print(f"📂 Loading model from {model_dir}...")
    
    try:
        # Load model
        tokenizer = AutoTokenizer.from_pretrained(model_dir)
        model = AutoModelForSequenceClassification.from_pretrained(model_dir)
        print(f"✅ Model loaded successfully")
        
        # Load test data
        print(f"📂 Loading test data from {test_file}...")
        df = pd.read_csv(test_file)
        print(f"✅ Loaded {len(df)} statements")
        
        return model, tokenizer, df
        
    except Exception as e:
        print(f"❌ Error loading model or data: {e}")
        raise

def analyze_confidence_distribution(model, tokenizer, df, label_map):
    """Analyze confidence score distribution across all predictions"""
    print("\n📊 Analyzing confidence distribution...")
    
    confidences = []
    predictions = []
    true_labels = []
    texts = []
    
    model.eval()
    processed = 0
    
    with torch.no_grad():
        for _, row in df.iterrows():
            # Skip rows without labels (check both 'label' and 'labels' columns)
            label_col = 'label' if 'label' in df.columns else 'labels'
            if label_col not in df.columns or pd.isna(row[label_col]) or row[label_col] == '':
                continue
                
            try:
                # Tokenize
                inputs = tokenizer(
                    row['text'], 
                    return_tensors='pt', 
                    truncation=True, 
                    padding=True, 
                    max_length=512
                )
                
                # Get prediction
                outputs = model(**inputs)
                probs = torch.softmax(outputs.logits, dim=-1)
                confidence = torch.max(probs).item()
                predicted_class = torch.argmax(probs).item()
                
                confidences.append(confidence)
                predictions.append(predicted_class)
                texts.append(row['text'])
                
                # Parse true labels
                true_label_names = [label.strip() for label in row[label_col].split(',')]
                true_labels.append(true_label_names)
                
                processed += 1
                
            except Exception as e:
                print(f"⚠️ Error processing row {row.name}: {e}")
                continue
    
    print(f"✅ Processed {processed} statements")
    return confidences, predictions, true_labels, texts

def analyze_category_performance(confidences, predictions, true_labels, texts, label_map):
    """Analyze performance by category with detailed statistics"""
    print("\n📈 Analyzing category performance...")
    
    # Reverse label map for lookup
    id_to_label = {v: k for k, v in label_map.items()}
    
    category_stats = defaultdict(lambda: {
        'confidences': [],
        'correct': [],
        'examples': []
    })
    
    # Process each prediction
    for conf, pred, true_lbls, text in zip(confidences, predictions, true_labels, texts):
        predicted_label = id_to_label.get(pred, f"unknown_{pred}")
        
        # Track confidence and examples
        category_stats[predicted_label]['confidences'].append(conf)
        category_stats[predicted_label]['examples'].append({
            'text': text[:100] + '...' if len(text) > 100 else text,
            'confidence': conf,
            'true_labels': true_lbls
        })
        
        # Track accuracy
        is_correct = predicted_label in true_lbls
        category_stats[predicted_label]['correct'].append(is_correct)
    
    # Calculate final statistics
    results = {}
    for category, data in category_stats.items():
        if data['confidences']:  # Only process categories with data
            confidences = data['confidences']
            correct = data['correct']
            
            results[category] = {
                'count': len(confidences),
                'avg_confidence': float(np.mean(confidences)),
                'min_confidence': float(np.min(confidences)),
                'max_confidence': float(np.max(confidences)),
                'std_confidence': float(np.std(confidences)),
                'accuracy': float(np.mean(correct)),
                'correct_count': sum(correct),
                'examples': data['examples'][:3]  # Keep top 3 examples
            }
    
    return results

def identify_weak_categories(category_performance, min_confidence=0.25, min_accuracy=0.4):
    """Identify categories that need improvement with detailed analysis"""
    print(f"\n🎯 Identifying weak categories...")
    print(f"   Thresholds: confidence < {min_confidence}, accuracy < {min_accuracy}")
    
    weak_categories = []
    strong_categories = []
    
    for category, stats in category_performance.items():
        issues = []
        
        # Check various weakness criteria
        if stats['avg_confidence'] < min_confidence:
            issues.append(f"Low confidence ({stats['avg_confidence']:.3f})")
        
        if stats['accuracy'] < min_accuracy:
            issues.append(f"Low accuracy ({stats['accuracy']:.3f})")
        
        if stats['count'] < 3:
            issues.append(f"Too few examples ({stats['count']})")
        
        if stats['std_confidence'] > 0.3:
            issues.append(f"High confidence variance ({stats['std_confidence']:.3f})")
        
        # Categorize
        if issues:
            weak_categories.append({
                'category': category,
                'issues': issues,
                'stats': stats,
                'priority': len(issues) + (1 if stats['count'] < 2 else 0)  # Priority score
            })
        else:
            strong_categories.append({
                'category': category,
                'stats': stats
            })
    
    # Sort by priority (highest first)
    weak_categories.sort(key=lambda x: x['priority'], reverse=True)
    
    print(f"   Found {len(weak_categories)} weak categories, {len(strong_categories)} strong categories")
    
    return weak_categories, strong_categories

def generate_improvement_recommendations(weak_categories, strong_categories, overall_stats):
    """Generate specific, actionable improvement recommendations"""
    print("\n💡 Generating improvement recommendations...")
    
    recommendations = {
        'immediate_actions': [],
        'threshold_tuning': [],
        'data_augmentation': [],
        'model_optimization': [],
        'architecture_changes': []
    }
    
    # Overall performance analysis
    avg_confidence = overall_stats['avg_confidence']
    
    # Immediate actions based on overall performance
    if avg_confidence < 0.2:
        recommendations['immediate_actions'].append(
            "🚨 CRITICAL: Overall confidence very low - consider lowering threshold to 0.1"
        )
        recommendations['model_optimization'].append(
            "Increase training epochs from 3 to 5-8 for better convergence"
        )
    elif avg_confidence < 0.3:
        recommendations['threshold_tuning'].append(
            f"Lower confidence threshold from 0.3 to 0.2 (current avg: {avg_confidence:.3f})"
        )
    
    # Category-specific recommendations
    high_priority_categories = [cat for cat in weak_categories if cat['priority'] >= 3]
    
    if high_priority_categories:
        recommendations['immediate_actions'].append(
            f"Focus on {len(high_priority_categories)} high-priority categories: " +
            ", ".join([cat['category'] for cat in high_priority_categories[:3]])
        )
    
    # Data augmentation needs
    few_examples = [cat for cat in weak_categories if cat['stats']['count'] < 5]
    if few_examples:
        recommendations['data_augmentation'].extend([
            f"Add training examples for '{cat['category']}' (currently {cat['stats']['count']})"
            for cat in few_examples[:5]
        ])
    
    # Model optimization based on patterns
    low_accuracy = [cat for cat in weak_categories if cat['stats']['accuracy'] < 0.3]
    if len(low_accuracy) > 3:
        recommendations['model_optimization'].append(
            "Multiple categories have low accuracy - consider adjusting learning rate or model architecture"
        )
    
    # Architecture recommendations
    if len(weak_categories) > len(strong_categories):
        recommendations['architecture_changes'].append(
            "Consider switching to a larger base model (e.g., deberta-v3-base instead of bert-base)"
        )
    
    return recommendations

def calculate_overall_stats(category_performance):
    """Calculate overall performance statistics"""
    all_confidences = []
    all_accuracies = []
    total_examples = 0
    
    for stats in category_performance.values():
        all_confidences.extend([stats['avg_confidence']] * stats['count'])
        all_accuracies.extend([stats['accuracy']] * stats['count'])
        total_examples += stats['count']
    
    return {
        'avg_confidence': float(np.mean(all_confidences)) if all_confidences else 0.0,
        'std_confidence': float(np.std(all_confidences)) if all_confidences else 0.0,
        'avg_accuracy': float(np.mean(all_accuracies)) if all_accuracies else 0.0,
        'total_examples': total_examples,
        'total_categories': len(category_performance)
    }

def save_analysis_report(output_file, category_performance, weak_categories, strong_categories, 
                        recommendations, overall_stats, confidences):
    """Save comprehensive analysis report with actionable insights"""
    print(f"\n💾 Saving analysis report to {output_file}...")
    
    report = {
        'metadata': {
            'timestamp': pd.Timestamp.now().isoformat(),
            'analysis_version': '2.0',
            'model_type': 'BERT-based baseline'
        },
        'overall_performance': {
            **overall_stats,
            'confidence_distribution': {
                'min': float(np.min(confidences)),
                'max': float(np.max(confidences)),
                'mean': float(np.mean(confidences)),
                'median': float(np.median(confidences)),
                'std': float(np.std(confidences)),
                'percentiles': {
                    '25th': float(np.percentile(confidences, 25)),
                    '75th': float(np.percentile(confidences, 75)),
                    '90th': float(np.percentile(confidences, 90))
                }
            }
        },
        'category_analysis': {
            'weak_categories': weak_categories,
            'strong_categories': strong_categories,
            'detailed_performance': category_performance
        },
        'recommendations': recommendations,
        'next_steps': {
            'priority_1': 'Implement threshold tuning recommendations',
            'priority_2': 'Address high-priority weak categories',
            'priority_3': 'Begin data augmentation for categories with few examples',
            'priority_4': 'Consider model architecture improvements'
        }
    }
    
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    return report

def print_summary(report):
    """Print a comprehensive summary of the analysis"""
    print("\n" + "="*70)
    print("📋 ROUND 2 ERROR ANALYSIS SUMMARY")
    print("="*70)

    overall = report['overall_performance']
    weak_cats = report['category_analysis']['weak_categories']
    strong_cats = report['category_analysis']['strong_categories']

    print(f"📊 Overall Performance:")
    print(f"  Average Confidence: {overall['avg_confidence']:.3f}")
    print(f"  Average Accuracy: {overall['avg_accuracy']:.3f}")
    print(f"  Total Categories: {overall['total_categories']}")
    print(f"  Total Examples: {overall['total_examples']}")

    conf_dist = overall['confidence_distribution']
    print(f"\n📈 Confidence Distribution:")
    print(f"  Range: {conf_dist['min']:.3f} - {conf_dist['max']:.3f}")
    print(f"  Median: {conf_dist['median']:.3f}")
    print(f"  90th Percentile: {conf_dist['percentiles']['90th']:.3f}")

    print(f"\n🎯 Category Analysis:")
    print(f"  Strong Categories: {len(strong_cats)}")
    print(f"  Weak Categories: {len(weak_cats)}")

    if weak_cats:
        print(f"\n⚠️ Top Weak Categories:")
        for i, weak_cat in enumerate(weak_cats[:5], 1):
            issues = ', '.join(weak_cat['issues'])
            print(f"  {i}. {weak_cat['category']}: {issues}")

    print(f"\n💡 Key Recommendations:")
    for category, recs in report['recommendations'].items():
        if recs:
            print(f"  {category.replace('_', ' ').title()}:")
            for rec in recs[:2]:  # Show top 2
                print(f"    • {rec}")

    print(f"\n🚀 Next Steps:")
    for priority, action in report['next_steps'].items():
        print(f"  {priority.replace('_', ' ').title()}: {action}")

def main():
    parser = argparse.ArgumentParser(description='Analyze DeBERTa model errors and performance for Round 2 improvements')
    parser.add_argument('--model_dir', default='models/bert-test', help='Directory containing the trained model')
    parser.add_argument('--test_file', default='data/statements_to_label.csv', help='CSV file with test data')
    parser.add_argument('--output', default='round2_error_analysis.json', help='Output file for analysis report')
    parser.add_argument('--confidence_threshold', type=float, default=0.25, help='Confidence threshold for weak category identification')
    parser.add_argument('--accuracy_threshold', type=float, default=0.4, help='Accuracy threshold for weak category identification')

    args = parser.parse_args()

    print("🔍 ROUND 2 DEBERTA ERROR ANALYSIS")
    print("="*50)
    print(f"Model Directory: {args.model_dir}")
    print(f"Test Data: {args.test_file}")
    print(f"Output: {args.output}")

    try:
        # Load model and data
        model, tokenizer, df = load_model_and_data(args.model_dir, args.test_file)

        # Load label mapping
        label_map_file = Path(args.model_dir) / 'label_map.json'
        if label_map_file.exists():
            with open(label_map_file) as f:
                label_map = json.load(f)
            print(f"✅ Loaded label mapping with {len(label_map)} categories")
        else:
            print("⚠️ No label_map.json found, creating basic mapping...")
            unique_labels = set()
            for _, row in df.iterrows():
                label_col = 'label' if 'label' in df.columns else 'labels'
                if label_col in df.columns and pd.notna(row[label_col]) and row[label_col] != '':
                    labels = [label.strip() for label in row[label_col].split(',')]
                    unique_labels.update(labels)
            label_map = {label: i for i, label in enumerate(sorted(unique_labels))}
            print(f"✅ Created label mapping with {len(label_map)} categories")

        # Analyze confidence distribution
        confidences, predictions, true_labels, texts = analyze_confidence_distribution(model, tokenizer, df, label_map)

        # Analyze category performance
        category_performance = analyze_category_performance(confidences, predictions, true_labels, texts, label_map)

        # Calculate overall statistics
        overall_stats = calculate_overall_stats(category_performance)

        # Identify weak categories
        weak_categories, strong_categories = identify_weak_categories(
            category_performance,
            args.confidence_threshold,
            args.accuracy_threshold
        )

        # Generate recommendations
        recommendations = generate_improvement_recommendations(weak_categories, strong_categories, overall_stats)

        # Save report
        report = save_analysis_report(
            args.output,
            category_performance,
            weak_categories,
            strong_categories,
            recommendations,
            overall_stats,
            confidences
        )

        # Print summary
        print_summary(report)

        print(f"\n✅ Round 2 error analysis complete!")
        print(f"📄 Full report saved to: {args.output}")
        print(f"🎯 Ready to implement improvements based on recommendations")

        return 0

    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
