{"architectures": ["BertForSequenceClassification"], "attention_probs_dropout_prob": 0.1, "classifier_dropout": null, "gradient_checkpointing": false, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "id2label": {"0": "ADDED_DATA_OUT", "1": "ADDED_INAPPLICABLES_OUT", "2": "ADEQUATE_DATA_PLUS", "3": "ALTERED_IMPORTANCE_OUT", "4": "ALTERED_SEQUENCE_OUT", "5": "ASSUMED_IDENTITIES_OUT", "6": "CONTRARY_FACTS_OUT", "7": "CORRECT_DATA_PLUS", "8": "CORRECT_IMPORTANCE_PLUS", "9": "CORRECT_SEQUENCE_PLUS", "10": "CORRECT_SOURCE_PLUS", "11": "DATA_PROVEN_FACTUAL_PLUS", "12": "FALSE_DATA_OUT", "13": "NEUTRAL", "14": "OMITTED_DATA_OUT", "15": "TIME_NOTED_PLUS", "16": "WRONG_TARGET_OUT"}, "initializer_range": 0.02, "intermediate_size": 3072, "label2id": {"ADDED_DATA_OUT": 0, "ADDED_INAPPLICABLES_OUT": 1, "ADEQUATE_DATA_PLUS": 2, "ALTERED_IMPORTANCE_OUT": 3, "ALTERED_SEQUENCE_OUT": 4, "ASSUMED_IDENTITIES_OUT": 5, "CONTRARY_FACTS_OUT": 6, "CORRECT_DATA_PLUS": 7, "CORRECT_IMPORTANCE_PLUS": 8, "CORRECT_SEQUENCE_PLUS": 9, "CORRECT_SOURCE_PLUS": 10, "DATA_PROVEN_FACTUAL_PLUS": 11, "FALSE_DATA_OUT": 12, "NEUTRAL": 13, "OMITTED_DATA_OUT": 14, "TIME_NOTED_PLUS": 15, "WRONG_TARGET_OUT": 16}, "layer_norm_eps": 1e-12, "max_position_embeddings": 512, "model_type": "bert", "num_attention_heads": 12, "num_hidden_layers": 12, "pad_token_id": 0, "position_embedding_type": "absolute", "problem_type": "single_label_classification", "torch_dtype": "float32", "transformers_version": "4.51.3", "type_vocab_size": 2, "use_cache": true, "vocab_size": 30522}