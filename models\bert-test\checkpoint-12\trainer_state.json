{"best_global_step": 12, "best_metric": 2.645671844482422, "best_model_checkpoint": "./models/bert-test\\checkpoint-12", "epoch": 1.0, "eval_steps": 500, "global_step": 12, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "eval_loss": 2.645671844482422, "eval_runtime": 0.7131, "eval_samples_per_second": 16.828, "eval_steps_per_second": 4.207, "step": 12}], "logging_steps": 500, "max_steps": 12, "num_input_tokens_seen": 0, "num_train_epochs": 1, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 884007742800.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}