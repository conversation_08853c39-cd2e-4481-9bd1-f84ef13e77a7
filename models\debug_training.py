#!/usr/bin/env python3
"""
Debug script to test DeBERTa training setup
"""
import pandas as pd
from datasets import Dataset
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

def debug_dataset():
    # Load data
    df = pd.read_csv('data/statements_to_label.csv')
    
    # Filter out rows without labels
    df = df.dropna(subset=['label'])
    df = df[df['label'].str.strip() != '']
    
    print(f"📊 Loaded {len(df)} labeled statements")
    
    # Handle multi-label cases (comma-separated labels)
    all_labels = set()
    for label_str in df['label']:
        if pd.notna(label_str):
            labels = [l.strip() for l in str(label_str).split(',')]
            all_labels.update(labels)
    
    # Create label mappings
    sorted_labels = sorted(list(all_labels))
    label2id = {label: i for i, label in enumerate(sorted_labels)}
    id2label = {i: label for i, label in enumerate(sorted_labels)}
    
    print(f"📋 Found {len(sorted_labels)} unique labels")
    
    # For now, use the first label for single-label classification
    def get_primary_label(label_str):
        if pd.isna(label_str):
            return None
        labels = [l.strip() for l in str(label_str).split(',')]
        return labels[0] if labels else None
    
    df["primary_label"] = df["label"].apply(get_primary_label)
    df = df.dropna(subset=["primary_label"])
    
    # Convert primary labels to IDs
    df["label_id"] = df["primary_label"].map(label2id)
    
    print("Sample data:")
    print(df[['text', 'primary_label', 'label_id']].head())
    
    # Create dataset
    dataset = Dataset.from_pandas(df[['text', 'label_id']])
    
    print("\nDataset structure:")
    print(dataset)
    print("\nFirst example:")
    print(dataset[0])
    
    # Test tokenization
    tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
    
    def tokenize_function(examples):
        # Tokenize the text
        tokenized = tokenizer(examples['text'], truncation=True, padding="max_length", max_length=512)
        # Add labels
        tokenized["labels"] = examples["label_id"]
        return tokenized
    
    tokenized_dataset = dataset.map(tokenize_function, batched=True)
    
    print("\nTokenized dataset structure:")
    print(tokenized_dataset)
    print("\nFirst tokenized example:")
    example = tokenized_dataset[0]
    for key, value in example.items():
        if isinstance(value, list):
            print(f"{key}: {type(value)} of length {len(value)}")
        else:
            print(f"{key}: {type(value)} = {value}")
    
    return tokenized_dataset, label2id, id2label

if __name__ == "__main__":
    debug_dataset()
