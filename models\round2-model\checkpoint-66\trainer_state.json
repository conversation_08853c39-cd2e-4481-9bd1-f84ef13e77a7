{"best_global_step": 50, "best_metric": 2.764742136001587, "best_model_checkpoint": "./models/round2-model\\checkpoint-50", "epoch": 6.0, "eval_steps": 50, "global_step": 66, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9090909090909091, "grad_norm": 10.975519180297852, "learning_rate": 1.8000000000000001e-06, "loss": 2.8841, "step": 10}, {"epoch": 1.8181818181818183, "grad_norm": 8.858501434326172, "learning_rate": 3.8000000000000005e-06, "loss": 2.8658, "step": 20}, {"epoch": 2.7272727272727275, "grad_norm": 7.924839019775391, "learning_rate": 5.8e-06, "loss": 2.863, "step": 30}, {"epoch": 3.6363636363636362, "grad_norm": 6.315624237060547, "learning_rate": 7.800000000000002e-06, "loss": 2.7255, "step": 40}, {"epoch": 4.545454545454545, "grad_norm": 9.05431842803955, "learning_rate": 9.800000000000001e-06, "loss": 2.6721, "step": 50}, {"epoch": 4.545454545454545, "eval_loss": 2.764742136001587, "eval_runtime": 18.2343, "eval_samples_per_second": 1.152, "eval_steps_per_second": 0.165, "step": 50}, {"epoch": 5.454545454545454, "grad_norm": 7.401163578033447, "learning_rate": 1.18e-05, "loss": 2.5935, "step": 60}], "logging_steps": 10, "max_steps": 66, "num_input_tokens_seen": 0, "num_train_epochs": 6, "save_steps": 50, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 3, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 127889194567680.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}