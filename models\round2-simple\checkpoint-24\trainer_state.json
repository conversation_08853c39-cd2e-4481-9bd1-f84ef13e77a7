{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 3.0, "eval_steps": 100, "global_step": 24, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.625, "grad_norm": 7.038710594177246, "learning_rate": 1.6666666666666667e-05, "loss": 3.5031, "step": 5}, {"epoch": 1.25, "grad_norm": 4.973616123199463, "learning_rate": 1.25e-05, "loss": 3.3498, "step": 10}, {"epoch": 1.875, "grad_norm": 6.662764549255371, "learning_rate": 8.333333333333334e-06, "loss": 3.2577, "step": 15}, {"epoch": 2.5, "grad_norm": 6.589779376983643, "learning_rate": 4.166666666666667e-06, "loss": 3.1956, "step": 20}], "logging_steps": 5, "max_steps": 24, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 45397659778560.0, "train_batch_size": 16, "trial_name": null, "trial_params": null}