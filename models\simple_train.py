#!/usr/bin/env python3
"""
Simplified DeBERTa training script for debugging
"""
import pandas as pd
from datasets import Dataset
from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification,
    TrainingArguments, 
    Trainer,
    DataCollatorWithPadding
)
from sklearn.model_selection import train_test_split
import torch

def main():
    print("🚀 Simple DeBERTa Training Test")
    print("=" * 40)
    
    # Load and prepare data
    df = pd.read_csv('data/statements_to_label.csv')
    df = df.dropna(subset=['label'])
    df = df[df['label'].str.strip() != '']
    
    print(f"📊 Loaded {len(df)} labeled statements")
    
    # Get unique labels
    all_labels = set()
    for label_str in df['label']:
        if pd.notna(label_str):
            labels = [l.strip() for l in str(label_str).split(',')]
            all_labels.update(labels)
    
    sorted_labels = sorted(list(all_labels))
    label2id = {label: i for i, label in enumerate(sorted_labels)}
    id2label = {i: label for i, label in enumerate(sorted_labels)}
    
    print(f"📋 Found {len(sorted_labels)} unique labels")
    
    # Use first label only
    def get_primary_label(label_str):
        if pd.isna(label_str):
            return None
        labels = [l.strip() for l in str(label_str).split(',')]
        return labels[0] if labels else None
    
    df["primary_label"] = df["label"].apply(get_primary_label)
    df = df.dropna(subset=["primary_label"])
    df["label_id"] = df["primary_label"].map(label2id)
    
    # Simple train/val split
    train_df, val_df = train_test_split(df, test_size=0.2, random_state=42)
    
    print(f"📊 Train: {len(train_df)} samples, Val: {len(val_df)} samples")
    
    # Create datasets
    train_dataset = Dataset.from_pandas(train_df[['text', 'label_id']])
    val_dataset = Dataset.from_pandas(val_df[['text', 'label_id']])
    
    # Load tokenizer and model
    model_name = 'bert-base-uncased'
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name,
        num_labels=len(label2id),
        id2label=id2label,
        label2id=label2id
    )
    
    # Tokenize function
    def tokenize_function(examples):
        tokenized = tokenizer(
            examples['text'], 
            truncation=True, 
            padding=False,  # Let data collator handle padding
            max_length=512
        )
        tokenized["labels"] = examples["label_id"]
        return tokenized
    
    # Tokenize datasets
    train_dataset = train_dataset.map(tokenize_function, batched=True)
    val_dataset = val_dataset.map(tokenize_function, batched=True)
    
    # Remove unnecessary columns
    train_dataset = train_dataset.remove_columns(['text'])
    val_dataset = val_dataset.remove_columns(['text'])
    
    # Data collator
    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir='./models/bert-test',
        num_train_epochs=1,
        per_device_train_batch_size=4,
        per_device_eval_batch_size=4,
        warmup_steps=10,
        weight_decay=0.01,
        logging_dir='./logs',
        eval_strategy="epoch",
        save_strategy="epoch",
        load_best_model_at_end=True,
        report_to=None,
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        data_collator=data_collator,
    )
    
    # Train
    print("🏋️ Training...")
    trainer.train()
    
    # Save
    trainer.save_model('./models/bert-test')
    tokenizer.save_pretrained('./models/bert-test')
    
    print("✅ Training complete!")
    
    # Test prediction
    test_text = "This is a test statement."
    inputs = tokenizer(test_text, return_tensors="pt", truncation=True, padding=True)
    
    with torch.no_grad():
        outputs = model(**inputs)
        predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
        predicted_class_id = predictions.argmax().item()
        predicted_label = id2label[predicted_class_id]
        confidence = predictions[0][predicted_class_id].item()
    
    print(f"🧪 Test prediction: {predicted_label} (confidence: {confidence:.3f})")

if __name__ == "__main__":
    main()
