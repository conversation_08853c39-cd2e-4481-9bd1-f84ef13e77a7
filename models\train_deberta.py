"""
Enhanced DeBERTa Training Script for TruthAlgorithm Iterative Improvement
Includes error analysis, performance tracking, and baseline establishment.
"""
import os
import pandas as pd
import numpy as np
import json
from datetime import datetime
from datasets import Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForSequenceClassification,
    TrainingArguments,
    Trainer,
    DataCollatorWithPadding
)
import torch
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import argparse


def prepare_dataset(csv_path, text_column="text", label_column="label"):
    """Prepare dataset from CSV file with enhanced multi-label support."""
    # Load data
    df = pd.read_csv(csv_path)

    # Filter out rows without labels
    df = df.dropna(subset=[label_column])
    df = df[df[label_column].str.strip() != '']

    print(f"📊 Loaded {len(df)} labeled statements")

    # Handle multi-label cases (comma-separated labels)
    all_labels = set()
    for label_str in df[label_column]:
        if pd.notna(label_str):
            labels = [l.strip() for l in str(label_str).split(',')]
            all_labels.update(labels)

    # Create label mappings
    sorted_labels = sorted(list(all_labels))
    label2id = {label: i for i, label in enumerate(sorted_labels)}
    id2label = {i: label for i, label in enumerate(sorted_labels)}

    print(f"📋 Found {len(sorted_labels)} unique labels:")
    for label in sorted_labels[:10]:  # Show first 10
        print(f"  - {label}")
    if len(sorted_labels) > 10:
        print(f"  ... and {len(sorted_labels) - 10} more")

    # For now, use the first label for single-label classification
    # TODO: Implement proper multi-label classification in future iterations
    def get_primary_label(label_str):
        if pd.isna(label_str):
            return None
        labels = [l.strip() for l in str(label_str).split(',')]
        return labels[0] if labels else None

    df["primary_label"] = df[label_column].apply(get_primary_label)
    df = df.dropna(subset=["primary_label"])

    # Convert primary labels to IDs
    df["label_id"] = df["primary_label"].map(label2id)

    # Check class distribution for stratification
    label_counts = df["primary_label"].value_counts()
    print(f"📊 Label distribution:")
    for label, count in label_counts.items():
        print(f"  {label}: {count}")

    # Use stratification only if all classes have at least 2 examples
    min_count = label_counts.min()
    use_stratify = min_count >= 2

    if use_stratify:
        print("✅ Using stratified split")
        stratify = df["primary_label"]
    else:
        print(
            f"⚠️ Some classes have only {min_count} example(s). Using random split.")
        stratify = None

    # Split into train and validation
    train_df, val_df = train_test_split(
        df, test_size=0.2, random_state=42,
        stratify=stratify
    )

    print(f"📊 Train: {len(train_df)} samples, Val: {len(val_df)} samples")

    # Convert to Hugging Face datasets
    train_dataset = Dataset.from_pandas(train_df)
    val_dataset = Dataset.from_pandas(val_df)

    return train_dataset, val_dataset, label2id, id2label


def tokenize_function(examples, tokenizer, text_column="text"):
    """Tokenize examples."""
    return tokenizer(examples[text_column], truncation=True, padding="max_length", max_length=512)


def save_training_results(output_dir, results, label2id, id2label):
    """Save training results and metadata for iterative improvement tracking."""

    # Create results directory
    results_dir = os.path.join(output_dir, "training_results")
    os.makedirs(results_dir, exist_ok=True)

    # Save metadata
    metadata = {
        "timestamp": datetime.now().isoformat(),
        "model_type": "DeBERTa-v3-base",
        "num_labels": len(label2id),
        "label2id": label2id,
        "id2label": id2label,
        "training_results": results
    }

    with open(os.path.join(results_dir, "metadata.json"), "w") as f:
        json.dump(metadata, f, indent=2)

    print(f"📁 Training results saved to {results_dir}")


def evaluate_model_performance(trainer, val_dataset, id2label):
    """Comprehensive model evaluation with error analysis."""

    print("🔍 Evaluating model performance...")

    # Get predictions
    predictions = trainer.predict(val_dataset)
    y_pred = np.argmax(predictions.predictions, axis=1)
    y_true = predictions.label_ids

    # Convert IDs back to labels
    pred_labels = [id2label[pred] for pred in y_pred]
    true_labels = [id2label[true] for true in y_true]

    # Classification report
    report = classification_report(true_labels, pred_labels, output_dict=True)

    print("📊 Classification Report:")
    print(classification_report(true_labels, pred_labels))

    # Confusion matrix
    cm = confusion_matrix(true_labels, pred_labels)

    # Error analysis - find misclassified examples
    errors = []
    for i, (pred, true) in enumerate(zip(pred_labels, true_labels)):
        if pred != true:
            text = val_dataset[i]['text']
            errors.append({
                'text': text,
                'predicted': pred,
                'actual': true,
                'confidence': float(np.max(predictions.predictions[i]))
            })

    print(f"❌ Found {len(errors)} misclassified examples")

    # Show worst errors (lowest confidence)
    errors.sort(key=lambda x: x['confidence'])
    print("\n🔍 Most uncertain predictions:")
    for error in errors[:5]:
        print(f"  Text: {error['text'][:60]}...")
        print(
            f"  Predicted: {error['predicted']} (confidence: {error['confidence']:.3f})")
        print(f"  Actual: {error['actual']}")
        print()

    return {
        'classification_report': report,
        'confusion_matrix': cm.tolist(),
        'errors': errors,
        'accuracy': report['accuracy'],
        'macro_f1': report['macro avg']['f1-score']
    }


def main(args):
    print("🚀 Starting DeBERTa Training - TruthAlgorithm Iterative Improvement")
    print("=" * 60)

    # Prepare dataset
    train_dataset, val_dataset, label2id, id2label = prepare_dataset(
        args.data_path, args.text_column, args.label_column
    )

    # Load tokenizer and model
    tokenizer = AutoTokenizer.from_pretrained(args.model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        args.model_name,
        num_labels=len(label2id),
        id2label=id2label,
        label2id=label2id
    )

    # Tokenize datasets
    print("🔤 Tokenizing datasets...")

    def tokenize_and_prepare(examples):
        # Tokenize the text
        tokenized = tokenizer(
            examples[args.text_column], truncation=True, padding="max_length", max_length=512)
        # Add labels
        tokenized["labels"] = examples["label_id"]
        return tokenized

    tokenized_train = train_dataset.map(tokenize_and_prepare, batched=True)
    tokenized_val = val_dataset.map(tokenize_and_prepare, batched=True)

    # Data collator
    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

    # Training arguments
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        learning_rate=args.learning_rate,
        per_device_train_batch_size=args.batch_size,
        per_device_eval_batch_size=args.batch_size,
        num_train_epochs=args.epochs,
        weight_decay=0.01,
        eval_strategy="epoch",
        save_strategy="epoch",
        load_best_model_at_end=True,
        push_to_hub=False,
    )

    # Initialize Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_train,
        eval_dataset=tokenized_val,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )

    # Train model
    print("🏋️ Training model...")
    training_results = trainer.train()

    # Evaluate model performance
    evaluation_results = evaluate_model_performance(
        trainer, tokenized_val, id2label)

    # Save model
    print("💾 Saving model...")
    trainer.save_model(args.output_dir)
    tokenizer.save_pretrained(args.output_dir)

    # Save training results for iterative improvement
    all_results = {
        'training': training_results.metrics,
        'evaluation': evaluation_results
    }
    save_training_results(args.output_dir, all_results, label2id, id2label)

    print(f"✅ Model saved to {args.output_dir}")
    print(f"📊 Final Accuracy: {evaluation_results['accuracy']:.3f}")
    print(f"📊 Final F1-Score: {evaluation_results['macro_f1']:.3f}")
    print(f"📋 Label mapping: {len(id2label)} labels")

    # Print summary for iterative improvement planning
    print("\n🎯 BASELINE ESTABLISHED - ROUND 1 COMPLETE")
    print("=" * 60)
    print("Next steps for iterative improvement:")
    print("1. Analyze error patterns in training_results/metadata.json")
    print("2. Identify weak categories for targeted data collection")
    print("3. Run error analysis tools to find improvement opportunities")
    print("4. Begin Round 2 with enhanced training data")

    return evaluation_results


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Fine-tune DeBERTa for classification")
    parser.add_argument("--data_path", type=str,
                        required=True, help="Path to CSV data file")
    parser.add_argument("--text_column", type=str,
                        default="text", help="Column name for text")
    parser.add_argument("--label_column", type=str,
                        default="label", help="Column name for labels")
    parser.add_argument("--model_name", type=str,
                        default="microsoft/deberta-v3-base", help="Base model name")
    parser.add_argument("--output_dir", type=str,
                        default="models/deberta-lora", help="Output directory")
    parser.add_argument("--learning_rate", type=float,
                        default=2e-5, help="Learning rate")
    parser.add_argument("--batch_size", type=int, default=8, help="Batch size")
    parser.add_argument("--epochs", type=int, default=3,
                        help="Number of epochs")

    args = parser.parse_args()
    main(args)
