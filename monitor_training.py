#!/usr/bin/env python3
"""
Monitor training progress and check for completion
"""
import json
import time
from pathlib import Path
from datetime import datetime

def check_training_progress():
    print("🔍 MONITORING ROUND 2 TRAINING")
    print("="*40)
    
    # Check if training results exist
    results_file = Path("round2_simple_results.json")
    if results_file.exists():
        print("✅ Training completed! Loading results...")
        
        with open(results_file, 'r') as f:
            results = json.load(f)
        
        print(f"\n🎯 TRAINING RESULTS:")
        print(f"   Accuracy: {results.get('accuracy', 'N/A'):.3f}")
        print(f"   Duration: {results.get('training_duration', 'N/A')}")
        print(f"   Model: {results.get('model_name', 'N/A')}")
        print(f"   Training Examples: {results.get('training_examples', 'N/A')}")
        print(f"   Validation Examples: {results.get('validation_examples', 'N/A')}")
        print(f"   Timestamp: {results.get('timestamp', 'N/A')}")
        
        return True
    
    # Check if model files exist
    model_dir = Path("models/round2-simple")
    if model_dir.exists():
        model_files = list(model_dir.glob("*"))
        print(f"📁 Model directory exists with {len(model_files)} files")
        
        key_files = ["pytorch_model.bin", "config.json", "tokenizer_config.json"]
        for file_name in key_files:
            file_path = model_dir / file_name
            if file_path.exists():
                size = file_path.stat().st_size / (1024*1024)  # MB
                print(f"   ✅ {file_name}: {size:.1f} MB")
            else:
                print(f"   ❌ {file_name}: Missing")
    
    # Check logs directory
    logs_dir = Path("logs")
    if logs_dir.exists():
        log_files = list(logs_dir.glob("**/*"))
        if log_files:
            print(f"📊 Found {len(log_files)} log files")
            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"   Latest: {latest_log}")
    
    print(f"\n⏳ Training still in progress...")
    return False

def main():
    completed = check_training_progress()
    
    if completed:
        print(f"\n🎉 SUCCESS! Round 2 training completed.")
        print(f"📁 Model saved to: models/round2-simple/")
        print(f"📊 Results saved to: round2_simple_results.json")
    else:
        print(f"\n🔄 Training in progress. Check again later.")
        print(f"💡 Tip: Training typically takes 5-15 minutes")

if __name__ == "__main__":
    main()
