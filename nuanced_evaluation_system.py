#!/usr/bin/env python3
"""
Nuanced Evaluation System for Outpoints/Pluspoints
Implements proper L<PERSON> methodology with:
1. Base knowledge database for contradiction detection
2. Cross-referencing within statement sets
3. Context-aware evaluation with assumptions tracking
4. Proper handling of conditional statements and exceptions
"""
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Set, Tuple, Optional
import re

class BaseKnowledgeDatabase:
    """Database of well-established facts for contradiction detection"""
    
    def __init__(self):
        self.established_facts = {
            # Physical/Natural Facts
            "sky_color": {
                "default": "blue",
                "exceptions": {
                    "sunset": ["pink", "orange", "red"],
                    "sunrise": ["pink", "orange", "red"], 
                    "storm": ["gray", "dark"],
                    "pollution": ["brown", "hazy"],
                    "night": ["black", "dark"]
                },
                "context_required": ["time", "weather", "location"]
            },
            "water_state": {
                "default": "liquid at room temperature",
                "exceptions": {
                    "freezing": "solid below 0°C",
                    "boiling": "gas above 100°C",
                    "pressure": "varies with atmospheric pressure"
                }
            },
            "gravity": {
                "default": "objects fall downward on Earth",
                "exceptions": {
                    "space": "weightlessness in orbit",
                    "underwater": "buoyancy effects"
                }
            },
            
            # Temporal Facts
            "time_sequence": {
                "default": "cause precedes effect",
                "exceptions": {
                    "quantum": "quantum entanglement effects",
                    "relativity": "time dilation effects"
                }
            },
            "historical_events": {
                "wwii_end": "1945",
                "moon_landing": "1969",
                "berlin_wall_fall": "1989"
            },
            
            # Mathematical/Logical Facts
            "mathematics": {
                "2+2": "4",
                "pi": "approximately 3.14159",
                "prime_numbers": [2, 3, 5, 7, 11, 13, 17, 19, 23, 29]
            },
            
            # Organizational/Business Facts
            "business_logic": {
                "profit_formula": "revenue - expenses = profit",
                "cash_flow": "must have money to pay bills",
                "employment": "employees must be paid for work"
            }
        }
        
        self.working_assumptions = {
            # Assumptions that can be challenged with evidence
            "default_contexts": {
                "location": "Earth surface",
                "time_period": "current era",
                "standard_conditions": "normal atmospheric pressure and temperature"
            }
        }
    
    def check_contradiction(self, statement: str, context: Dict = None) -> Dict:
        """Check if statement contradicts established facts"""
        contradictions = []
        
        # Normalize statement for checking
        statement_lower = statement.lower()
        
        # Check sky color claims
        if "sky" in statement_lower and any(color in statement_lower for color in ["pink", "red", "green", "purple"]):
            sky_claim = self._extract_sky_color_claim(statement_lower)
            if sky_claim:
                contradiction = self._check_sky_color_contradiction(sky_claim, statement, context)
                if contradiction:
                    contradictions.append(contradiction)
        
        # Check mathematical claims
        math_contradiction = self._check_mathematical_contradictions(statement_lower)
        if math_contradiction:
            contradictions.append(math_contradiction)
        
        # Check business logic contradictions
        business_contradiction = self._check_business_logic_contradictions(statement_lower)
        if business_contradiction:
            contradictions.append(business_contradiction)
        
        return {
            "has_contradictions": len(contradictions) > 0,
            "contradictions": contradictions,
            "requires_investigation": any(c.get("needs_context", False) for c in contradictions)
        }
    
    def _extract_sky_color_claim(self, statement: str) -> Optional[str]:
        """Extract sky color claim from statement"""
        sky_colors = ["pink", "red", "green", "purple", "yellow", "orange", "black", "white"]
        for color in sky_colors:
            if f"sky is {color}" in statement or f"sky was {color}" in statement:
                return color
        return None
    
    def _check_sky_color_contradiction(self, claimed_color: str, full_statement: str, context: Dict) -> Optional[Dict]:
        """Check if sky color claim contradicts established facts"""
        sky_facts = self.established_facts["sky_color"]
        
        # Check if it's the default (blue)
        if claimed_color == sky_facts["default"]:
            return None  # No contradiction
        
        # Check if it's a valid exception
        statement_lower = full_statement.lower()
        valid_exception = False
        
        for exception_type, valid_colors in sky_facts["exceptions"].items():
            if exception_type in statement_lower and claimed_color in valid_colors:
                valid_exception = True
                break
        
        if not valid_exception:
            return {
                "type": "CONTRARY_FACTS_OUT",
                "description": f"Sky claimed to be {claimed_color} without proper context",
                "established_fact": f"Sky is normally {sky_facts['default']}",
                "needs_context": True,
                "required_context": sky_facts["context_required"],
                "statement": full_statement
            }
        
        return None
    
    def _check_mathematical_contradictions(self, statement: str) -> Optional[Dict]:
        """Check for mathematical contradictions"""
        # Simple math checks
        if "2+2" in statement.replace(" ", ""):
            if "5" in statement and "2+2" in statement:
                return {
                    "type": "FALSEHOOD_OUT",
                    "description": "Mathematical error: 2+2 ≠ 5",
                    "established_fact": "2+2 = 4",
                    "needs_context": False
                }
        
        return None
    
    def _check_business_logic_contradictions(self, statement: str) -> Optional[Dict]:
        """Check for business logic contradictions"""
        # Check for profit/payment contradictions
        if ("record profit" in statement or "huge profit" in statement) and \
           ("couldn't pay" in statement or "unable to pay" in statement):
            return {
                "type": "CONTRARY_FACTS_OUT", 
                "description": "Contradiction: Record profits but unable to pay bills",
                "established_fact": "Profitable companies should be able to pay expenses",
                "needs_context": True,
                "required_context": ["timing", "cash_flow", "accounting_method"]
            }
        
        return None

class ContextualEvaluator:
    """Evaluates statements with full context awareness"""
    
    def __init__(self, knowledge_db: BaseKnowledgeDatabase):
        self.knowledge_db = knowledge_db
        self.statement_set = []
        self.cross_references = {}
    
    def load_statement_set(self, statements: List[Dict]):
        """Load a set of statements for cross-referencing"""
        self.statement_set = statements
        self._build_cross_references()
    
    def _build_cross_references(self):
        """Build cross-reference index of statements"""
        self.cross_references = {
            "entities": {},  # People, organizations, places
            "dates": {},     # Time references
            "events": {},    # Described events
            "claims": {}     # Factual claims
        }
        
        for i, stmt in enumerate(self.statement_set):
            text = stmt.get('text', '')
            
            # Extract entities (simple pattern matching)
            entities = self._extract_entities(text)
            for entity in entities:
                if entity not in self.cross_references["entities"]:
                    self.cross_references["entities"][entity] = []
                self.cross_references["entities"][entity].append(i)
            
            # Extract dates
            dates = self._extract_dates(text)
            for date in dates:
                if date not in self.cross_references["dates"]:
                    self.cross_references["dates"][date] = []
                self.cross_references["dates"][date].append(i)
    
    def _extract_entities(self, text: str) -> List[str]:
        """Extract named entities from text"""
        # Simple pattern matching for common entities
        entities = []
        
        # Company names (capitalized words ending in Corp, Inc, Ltd, etc.)
        company_pattern = r'\b[A-Z][a-zA-Z]+ (?:Corp|Inc|Ltd|Company|Corporation)\b'
        entities.extend(re.findall(company_pattern, text))
        
        # Person names (Title + Name pattern)
        person_pattern = r'\b(?:Mr|Ms|Mrs|Dr|President|CEO|Director)\s+[A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)?\b'
        entities.extend(re.findall(person_pattern, text))
        
        return entities
    
    def _extract_dates(self, text: str) -> List[str]:
        """Extract date references from text"""
        dates = []
        
        # Year patterns
        year_pattern = r'\b(19|20)\d{2}\b'
        dates.extend(re.findall(year_pattern, text))
        
        # Month/day patterns
        date_pattern = r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b'
        dates.extend(re.findall(date_pattern, text))
        
        return dates
    
    def evaluate_statement(self, statement: str, statement_index: int = None) -> Dict:
        """Evaluate a statement with full contextual awareness"""
        evaluation = {
            "statement": statement,
            "outpoints": [],
            "pluspoints": [],
            "contradictions": [],
            "cross_references": [],
            "context_issues": [],
            "investigation_needed": []
        }
        
        # Check against base knowledge
        contradiction_check = self.knowledge_db.check_contradiction(statement)
        if contradiction_check["has_contradictions"]:
            for contradiction in contradiction_check["contradictions"]:
                evaluation["outpoints"].append(contradiction["type"])
                evaluation["contradictions"].append(contradiction)
                
                if contradiction.get("needs_context", False):
                    evaluation["investigation_needed"].append({
                        "type": "context_verification",
                        "required_context": contradiction.get("required_context", []),
                        "description": f"Need to verify context for: {contradiction['description']}"
                    })
        
        # Check for cross-references within statement set
        if statement_index is not None:
            cross_refs = self._find_cross_references(statement, statement_index)
            evaluation["cross_references"] = cross_refs
            
            # Check for contradictions with other statements
            contradictory_statements = self._find_contradictory_statements(statement, statement_index)
            if contradictory_statements:
                evaluation["outpoints"].append("CONTRARY_FACTS_OUT")
                evaluation["contradictions"].extend(contradictory_statements)
        
        # Evaluate specific outpoints/pluspoints
        self._evaluate_data_completeness(statement, evaluation)
        self._evaluate_time_references(statement, evaluation)
        self._evaluate_source_attribution(statement, evaluation)
        self._evaluate_sequence_logic(statement, evaluation)
        
        return evaluation
    
    def _find_cross_references(self, statement: str, statement_index: int) -> List[Dict]:
        """Find cross-references to other statements"""
        cross_refs = []
        
        # Find statements about same entities
        entities = self._extract_entities(statement)
        for entity in entities:
            if entity in self.cross_references["entities"]:
                related_indices = [i for i in self.cross_references["entities"][entity] if i != statement_index]
                for idx in related_indices:
                    cross_refs.append({
                        "type": "entity_reference",
                        "entity": entity,
                        "related_statement_index": idx,
                        "related_statement": self.statement_set[idx].get('text', '')[:100] + "..."
                    })
        
        return cross_refs
    
    def _find_contradictory_statements(self, statement: str, statement_index: int) -> List[Dict]:
        """Find statements that contradict this one"""
        contradictions = []
        
        # Simple contradiction detection (can be enhanced)
        statement_lower = statement.lower()
        
        for i, other_stmt in enumerate(self.statement_set):
            if i == statement_index:
                continue
                
            other_text = other_stmt.get('text', '').lower()
            
            # Check for direct contradictions
            if self._are_contradictory(statement_lower, other_text):
                contradictions.append({
                    "type": "CONTRARY_FACTS_OUT",
                    "description": "Direct contradiction found between statements",
                    "contradictory_statement_index": i,
                    "contradictory_statement": other_stmt.get('text', '')[:100] + "...",
                    "needs_investigation": True
                })
        
        return contradictions
    
    def _are_contradictory(self, statement1: str, statement2: str) -> bool:
        """Check if two statements are contradictory"""
        # Simple contradiction patterns
        contradiction_pairs = [
            (["profit", "successful", "growing"], ["loss", "failing", "bankrupt", "couldn't pay"]),
            (["increased", "rose", "up"], ["decreased", "fell", "down"]),
            (["safe", "secure"], ["dangerous", "unsafe", "risk"]),
            (["true", "fact", "confirmed"], ["false", "lie", "denied"])
        ]
        
        for positive_terms, negative_terms in contradiction_pairs:
            has_positive = any(term in statement1 for term in positive_terms)
            has_negative = any(term in statement2 for term in negative_terms)
            
            if has_positive and has_negative:
                return True
                
            # Check reverse
            has_positive = any(term in statement2 for term in positive_terms)
            has_negative = any(term in statement1 for term in negative_terms)
            
            if has_positive and has_negative:
                return True
        
        return False
    
    def _evaluate_data_completeness(self, statement: str, evaluation: Dict):
        """Evaluate data completeness (omitted data, adequate data)"""
        # Check for vague terms that indicate omitted data
        vague_terms = ["sources say", "reportedly", "allegedly", "some people", "it is said"]
        
        if any(term in statement.lower() for term in vague_terms):
            evaluation["outpoints"].append("OMITTED_DATA_OUT")
            evaluation["context_issues"].append({
                "type": "vague_attribution",
                "description": "Statement uses vague attribution without specific sources"
            })
        
        # Check for adequate data indicators
        specific_indicators = ["according to", "reported by", "study by", "data shows", "statistics indicate"]
        
        if any(indicator in statement.lower() for indicator in specific_indicators):
            evaluation["pluspoints"].append("ADEQUATE_DATA_PLUS")
    
    def _evaluate_time_references(self, statement: str, evaluation: Dict):
        """Evaluate time references"""
        # Check for specific time references
        time_patterns = [
            r'\b\d{4}\b',  # Years
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\b',  # Months
            r'\b\d{1,2}:\d{2}\b',  # Times
            r'\b(?:yesterday|today|tomorrow|last week|next month)\b'  # Relative times
        ]
        
        has_time_reference = any(re.search(pattern, statement, re.IGNORECASE) for pattern in time_patterns)
        
        if has_time_reference:
            evaluation["pluspoints"].append("TIME_NOTED_PLUS")
        else:
            # Check if time would be expected
            time_expected_indicators = ["occurred", "happened", "announced", "reported", "said"]
            if any(indicator in statement.lower() for indicator in time_expected_indicators):
                evaluation["outpoints"].append("DROPPED_TIME_OUT")
    
    def _evaluate_source_attribution(self, statement: str, evaluation: Dict):
        """Evaluate source attribution"""
        # Good source indicators
        good_sources = ["according to", "reported by", "study by", "official statement", "press release"]
        
        if any(source in statement.lower() for source in good_sources):
            evaluation["pluspoints"].append("CORRECT_SOURCE_PLUS")
        
        # Poor source indicators
        poor_sources = ["sources say", "rumors", "allegedly", "unconfirmed"]
        
        if any(source in statement.lower() for source in poor_sources):
            evaluation["outpoints"].append("WRONG_SOURCE_OUT")
    
    def _evaluate_sequence_logic(self, statement: str, evaluation: Dict):
        """Evaluate logical sequence"""
        # Look for sequence indicators
        sequence_words = ["first", "then", "next", "finally", "before", "after", "following"]
        
        if any(word in statement.lower() for word in sequence_words):
            # This is a sequence statement - check if it makes logical sense
            # For now, assume correct unless obviously wrong
            evaluation["pluspoints"].append("EVENTS_IN_CORRECT_SEQUENCE_PLUS")

def main():
    """Demonstrate the nuanced evaluation system"""
    print("🔍 NUANCED OUTPOINT/PLUSPOINT EVALUATION SYSTEM")
    print("="*60)
    print("Based on L. Ron Hubbard's Investigations methodology")
    print("With base knowledge database and cross-referencing")
    print()
    
    # Initialize system
    knowledge_db = BaseKnowledgeDatabase()
    evaluator = ContextualEvaluator(knowledge_db)
    
    # Test statements
    test_statements = [
        {"text": "The sky is pink today.", "id": "test1"},
        {"text": "The company reported record profits of $2.3 billion.", "id": "test2"},
        {"text": "The same company couldn't pay its suppliers last month.", "id": "test3"},
        {"text": "According to NASA, the sky appears pink during certain sunset conditions.", "id": "test4"},
        {"text": "Sources say there were problems.", "id": "test5"},
        {"text": "The meeting occurred at 3 PM on March 15, 2024.", "id": "test6"}
    ]
    
    # Load statement set for cross-referencing
    evaluator.load_statement_set(test_statements)
    
    # Evaluate each statement
    for i, stmt in enumerate(test_statements):
        print(f"\n📝 EVALUATING STATEMENT {i+1}:")
        print(f"   Text: {stmt['text']}")
        
        evaluation = evaluator.evaluate_statement(stmt['text'], i)
        
        print(f"   Outpoints: {evaluation['outpoints']}")
        print(f"   Pluspoints: {evaluation['pluspoints']}")
        
        if evaluation['contradictions']:
            print(f"   Contradictions: {len(evaluation['contradictions'])}")
            for contradiction in evaluation['contradictions']:
                print(f"      - {contradiction['description']}")
        
        if evaluation['investigation_needed']:
            print(f"   Investigation needed:")
            for investigation in evaluation['investigation_needed']:
                print(f"      - {investigation['description']}")
    
    print(f"\n✅ Nuanced evaluation system demonstrated!")
    print(f"🎯 This system can handle:")
    print(f"   - Base knowledge contradictions")
    print(f"   - Cross-statement references")
    print(f"   - Context-dependent evaluations")
    print(f"   - Investigation requirements")

if __name__ == "__main__":
    main()
