{"label_to_id": {"ADDED_INAPPLICABLE_DATA_OUT": 0, "ADDED_TIME_OUT": 1, "ADEQUATE_DATA_PLUS": 2, "ALTERED_IMPORTANCE_OUT": 3, "ALTERED_SEQUENCE_OUT": 4, "APPLICABLE_DATA_PLUS": 5, "ASSUMED_DIFFERENCES_NOT_DIFFERENT_OUT": 6, "ASSUMED_IDENTITIES_NOT_IDENTICAL_OUT": 7, "ASSUMED_SIMILARITIES_NOT_SIMILAR_OUT": 8, "CONTRARY_FACTS_OUT": 9, "CORRECT_RELATIVE_IMPORTANCE_PLUS": 10, "CORRECT_SOURCE_PLUS": 11, "CORRECT_TARGET_PLUS": 12, "DATA_IN_SAME_CLASSIFICATION_PLUS": 13, "DATA_PROVEN_FACTUAL_PLUS": 14, "DIFFERENCES_ARE_DIFFERENT_PLUS": 15, "DROPPED_TIME_OUT": 16, "EVENTS_IN_CORRECT_SEQUENCE_PLUS": 17, "EXPECTED_TIME_PERIOD_PLUS": 18, "FALSEHOOD_OUT": 19, "IDENTITIES_ARE_IDENTICAL_PLUS": 20, "INCORRECTLY_INCLUDED_DATUM_OUT": 21, "NEUTRAL": 22, "OMITTED_DATA_OUT": 23, "RELATED_FACTS_KNOWN_PLUS": 24, "SIMILARITIES_ARE_SIMILAR_PLUS": 25, "TIME_NOTED_PLUS": 26, "WRONG_SOURCE_OUT": 27, "WRONG_TARGET_OUT": 28}, "id_to_label": {"0": "ADDED_INAPPLICABLE_DATA_OUT", "1": "ADDED_TIME_OUT", "2": "ADEQUATE_DATA_PLUS", "3": "ALTERED_IMPORTANCE_OUT", "4": "ALTERED_SEQUENCE_OUT", "5": "APPLICABLE_DATA_PLUS", "6": "ASSUMED_DIFFERENCES_NOT_DIFFERENT_OUT", "7": "ASSUMED_IDENTITIES_NOT_IDENTICAL_OUT", "8": "ASSUMED_SIMILARITIES_NOT_SIMILAR_OUT", "9": "CONTRARY_FACTS_OUT", "10": "CORRECT_RELATIVE_IMPORTANCE_PLUS", "11": "CORRECT_SOURCE_PLUS", "12": "CORRECT_TARGET_PLUS", "13": "DATA_IN_SAME_CLASSIFICATION_PLUS", "14": "DATA_PROVEN_FACTUAL_PLUS", "15": "DIFFERENCES_ARE_DIFFERENT_PLUS", "16": "DROPPED_TIME_OUT", "17": "EVENTS_IN_CORRECT_SEQUENCE_PLUS", "18": "EXPECTED_TIME_PERIOD_PLUS", "19": "FALSEHOOD_OUT", "20": "IDENTITIES_ARE_IDENTICAL_PLUS", "21": "INCORRECTLY_INCLUDED_DATUM_OUT", "22": "NEUTRAL", "23": "OMITTED_DATA_OUT", "24": "RELATED_FACTS_KNOWN_PLUS", "25": "SIMILARITIES_ARE_SIMILAR_PLUS", "26": "TIME_NOTED_PLUS", "27": "WRONG_SOURCE_OUT", "28": "WRONG_TARGET_OUT"}, "total_categories": 29, "outpoints": ["OMITTED_DATA_OUT", "ALTERED_SEQUENCE_OUT", "DROPPED_TIME_OUT", "FALSEHOOD_OUT", "ALTERED_IMPORTANCE_OUT", "WRONG_TARGET_OUT", "WRONG_SOURCE_OUT", "CONTRARY_FACTS_OUT", "ADDED_TIME_OUT", "ADDED_INAPPLICABLE_DATA_OUT", "INCORRECTLY_INCLUDED_DATUM_OUT", "ASSUMED_IDENTITIES_NOT_IDENTICAL_OUT", "ASSUMED_SIMILARITIES_NOT_SIMILAR_OUT", "ASSUMED_DIFFERENCES_NOT_DIFFERENT_OUT"], "pluspoints": ["RELATED_FACTS_KNOWN_PLUS", "EVENTS_IN_CORRECT_SEQUENCE_PLUS", "TIME_NOTED_PLUS", "DATA_PROVEN_FACTUAL_PLUS", "CORRECT_RELATIVE_IMPORTANCE_PLUS", "EXPECTED_TIME_PERIOD_PLUS", "ADEQUATE_DATA_PLUS", "APPLICABLE_DATA_PLUS", "CORRECT_SOURCE_PLUS", "CORRECT_TARGET_PLUS", "DATA_IN_SAME_CLASSIFICATION_PLUS", "IDENTITIES_ARE_IDENTICAL_PLUS", "SIMILARITIES_ARE_SIMILAR_PLUS", "DIFFERENCES_ARE_DIFFERENT_PLUS"]}