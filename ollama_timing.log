[2025-07-08 22:31:19.317] ============================================================
[2025-07-08 22:31:19.339] PRECISE OLLAMA TIMING TEST
[2025-07-08 22:31:19.339] ============================================================
[2025-07-08 22:31:19.339] TEST 1: Minimal request - just say 'OK'
[2025-07-08 22:31:19.340] Sending request to: http://localhost:11434/api/generate
[2025-07-08 22:31:19.340] Request data: {
  "model": "truth-evaluator",
  "prompt": "Say only \"OK\" and nothing else.",
  "stream": false,
  "options": {
    "num_predict": 5,
    "temperature": 0
  }
}
[2025-07-08 22:31:19.342] Request started at: 2025-07-08 22:31:19.342633
[2025-07-08 22:36:58.652] Request completed at: 2025-07-08 22:36:58.652495
[2025-07-08 22:36:58.652] Total elapsed time: 339.31 seconds (5.66 minutes)
[2025-07-08 22:36:58.652] HTTP Status: 200
[2025-07-08 22:36:58.753] AI Response: 'OK'
[2025-07-08 22:36:58.753] ✅ SUCCESS: AI responded successfully
[2025-07-08 22:36:58.771] 
📊 SIMPLE REQUEST BASELINE: 339.31 seconds
[2025-07-08 22:36:58.772] 
============================================================
[2025-07-08 22:36:58.773] TEST 2: Typical labeling request
[2025-07-08 22:36:58.773] ============================================================
[2025-07-08 22:36:58.774] Based on simple test (339.3s), estimating 1017.9s for this test
[2025-07-08 22:36:58.774] Testing typical outpoint evaluation request...
[2025-07-08 22:36:58.775] Request started at: 2025-07-08 22:36:58.775772
[2025-07-08 22:41:36.239] Request completed at: 2025-07-08 22:41:36.239268
[2025-07-08 22:41:36.239] Total elapsed time: 277.46 seconds (4.62 minutes)
[2025-07-08 22:41:36.354] AI Response: 'NO'
[2025-07-08 22:41:36.354] ✅ SUCCESS: Typical request completed
[2025-07-08 22:41:36.367] 
📊 TYPICAL REQUEST TIME: 277.46 seconds
[2025-07-08 22:41:36.367] 📊 RATIO: Typical request is 0.8x slower than simple
[2025-07-08 22:41:36.367] 
============================================================
[2025-07-08 22:41:36.371] RECOMMENDATIONS
[2025-07-08 22:41:36.371] ============================================================
[2025-07-08 22:41:36.371] ⚠️ Your computer is slow but AI labeling is feasible
[2025-07-08 22:41:36.372] 💡 Recommended timeout: 600 seconds (10 minutes)
[2025-07-08 22:41:36.372] 
📝 Complete log saved to: ollama_timing.log
[2025-07-08 22:41:36.373] 💡 You can delete this log file when done
