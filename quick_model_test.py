#!/usr/bin/env python3
"""
Quick test to check if our DeBERTa model can be loaded and used
"""
import pandas as pd
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import json
from pathlib import Path

def test_model_loading():
    """Test if we can load the model"""
    print("🔍 Testing model loading...")
    
    model_dir = "models/bert-test"
    
    try:
        print(f"Loading tokenizer from {model_dir}...")
        tokenizer = AutoTokenizer.from_pretrained(model_dir)
        print("✅ Tokenizer loaded")
        
        print(f"Loading model from {model_dir}...")
        model = AutoModelForSequenceClassification.from_pretrained(model_dir)
        print("✅ Model loaded")
        
        print(f"Model config: {model.config.num_labels} labels")
        
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None

def test_data_loading():
    """Test if we can load the data"""
    print("\n🔍 Testing data loading...")
    
    data_file = "data/statements_to_label.csv"
    
    try:
        df = pd.read_csv(data_file)
        print(f"✅ Data loaded: {len(df)} rows")
        print(f"Columns: {list(df.columns)}")
        
        # Check for labeled data
        label_col = 'label' if 'label' in df.columns else 'labels'
        if label_col in df.columns:
            labeled_count = df[label_col].notna().sum()
            print(f"✅ Found {labeled_count} labeled statements")
            
            # Show first few labels
            sample_labels = df[df[label_col].notna()][label_col].head(3).tolist()
            print(f"Sample labels: {sample_labels}")
        else:
            print("❌ No label column found")
            
        return df
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def test_single_prediction(model, tokenizer):
    """Test a single prediction"""
    print("\n🔍 Testing single prediction...")
    
    try:
        test_text = "The president announced new policies yesterday."
        
        # Tokenize
        inputs = tokenizer(test_text, return_tensors='pt', truncation=True, padding=True, max_length=512)
        print("✅ Text tokenized")
        
        # Get prediction
        model.eval()
        with torch.no_grad():
            outputs = model(**inputs)
            probs = torch.softmax(outputs.logits, dim=-1)
            confidence = torch.max(probs).item()
            predicted_class = torch.argmax(probs).item()
        
        print(f"✅ Prediction made:")
        print(f"   Predicted class: {predicted_class}")
        print(f"   Confidence: {confidence:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error making prediction: {e}")
        return False

def check_label_mapping():
    """Check if label mapping exists"""
    print("\n🔍 Checking label mapping...")
    
    label_map_file = Path("models/bert-test/label_map.json")
    
    if label_map_file.exists():
        try:
            with open(label_map_file) as f:
                label_map = json.load(f)
            print(f"✅ Label mapping found: {len(label_map)} categories")
            print(f"Categories: {list(label_map.keys())[:5]}...")
            return label_map
        except Exception as e:
            print(f"❌ Error loading label mapping: {e}")
            return None
    else:
        print("⚠️ No label mapping file found")
        return None

def main():
    print("🧪 QUICK MODEL TEST")
    print("="*40)
    
    # Test model loading
    model, tokenizer = test_model_loading()
    if model is None:
        print("❌ Cannot proceed without model")
        return 1
    
    # Test data loading
    df = test_data_loading()
    if df is None:
        print("❌ Cannot proceed without data")
        return 1
    
    # Check label mapping
    label_map = check_label_mapping()
    
    # Test single prediction
    prediction_success = test_single_prediction(model, tokenizer)
    
    # Summary
    print("\n" + "="*40)
    print("📋 TEST SUMMARY")
    print("="*40)
    print(f"Model Loading: {'✅' if model is not None else '❌'}")
    print(f"Data Loading: {'✅' if df is not None else '❌'}")
    print(f"Label Mapping: {'✅' if label_map is not None else '⚠️'}")
    print(f"Prediction: {'✅' if prediction_success else '❌'}")
    
    if all([model is not None, df is not None, prediction_success]):
        print("\n✅ All basic tests passed! Model is functional.")
        print("🚀 Ready for error analysis")
        return 0
    else:
        print("\n❌ Some tests failed. Check issues above.")
        return 1

if __name__ == "__main__":
    exit(main())
