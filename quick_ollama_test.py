import requests
import json

try:
    print("Testing Ollama connection...")
    response = requests.get("http://localhost:11434/api/tags", timeout=10)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Ollama is running!")
        print(f"Available models: {len(data.get('models', []))}")
        for model in data.get('models', []):
            print(f"  - {model.get('name', 'Unknown')}")
        
        # Test truth-evaluator specifically
        print("\nTesting truth-evaluator model...")
        test_response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "truth-evaluator",
                "prompt": "Test: The sky is blue.",
                "stream": False
            },
            timeout=30
        )
        
        if test_response.status_code == 200:
            print("✅ truth-evaluator model is working!")
        else:
            print(f"❌ truth-evaluator error: {test_response.status_code}")
            print(test_response.text)
    else:
        print(f"❌ Connection failed: {response.status_code}")
        
except Exception as e:
    print(f"❌ Error: {e}")
