#!/usr/bin/env python3
"""
Quick test of Round 2 model integration
"""
import json
from pathlib import Path

def test_round2_model():
    print("🧪 QUICK ROUND 2 MODEL TEST")
    print("="*40)
    
    # Test 1: Check model files
    model_dir = Path("models/round2-simple")
    print(f"1. Checking model directory: {model_dir}")
    
    if not model_dir.exists():
        print("❌ Model directory not found!")
        return False
    
    key_files = ["config.json", "model.safetensors", "label_map.json"]
    for file_name in key_files:
        if (model_dir / file_name).exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name}")
            return False
    
    # Test 2: Load label mapping
    print(f"\n2. Testing label mapping...")
    try:
        with open(model_dir / "label_map.json", "r") as f:
            label_data = json.load(f)
        
        num_labels = label_data.get("num_labels", 0)
        print(f"   ✅ Labels: {num_labels}")
        
        if num_labels != 29:
            print(f"   ⚠️ Expected 29 labels, got {num_labels}")
        
    except Exception as e:
        print(f"   ❌ Error loading labels: {e}")
        return False
    
    # Test 3: Test DeBERTa classifier directly
    print(f"\n3. Testing DeBERTa classifier...")
    try:
        from models.deberta_classifier import DeBERTaClassifier
        
        classifier = DeBERTaClassifier("models/round2-simple", threshold=0.05)
        
        if classifier.loaded:
            print(f"   ✅ Classifier loaded successfully")
            
            # Test prediction
            test_text = "The company reported record profits but couldn't pay suppliers."
            label, confidence = classifier.classify(test_text)
            
            print(f"   ✅ Prediction: {label}")
            print(f"   ✅ Confidence: {confidence:.3f}")
            
        else:
            print(f"   ❌ Classifier failed to load")
            return False
            
    except Exception as e:
        print(f"   ❌ Classifier error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 4: Check Ollama
    print(f"\n4. Testing Ollama connection...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print(f"   ✅ Ollama server running")
        else:
            print(f"   ⚠️ Ollama server issue (status: {response.status_code})")
    except Exception as e:
        print(f"   ⚠️ Ollama connection issue: {e}")
    
    print(f"\n🎉 Round 2 model test completed!")
    return True

if __name__ == "__main__":
    success = test_round2_model()
    exit(0 if success else 1)
