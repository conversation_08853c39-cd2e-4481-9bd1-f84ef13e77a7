#!/usr/bin/env python3
"""
Quick test to verify training setup works
"""
import pandas as pd
import json
import sys
from pathlib import Path

def test_training_setup():
    print("🧪 TESTING TRAINING SETUP")
    print("="*30)
    
    try:
        # Test data loading
        print("1. Loading data...")
        df = pd.read_csv("data/complete_28_category_dataset.csv")
        labeled_df = df[df['label'].notna() & (df['label'] != '')]
        print(f"   ✅ Loaded {len(labeled_df)} labeled examples")
        
        # Test mapping
        print("2. Loading mapping...")
        with open('official_28_category_mapping.json', 'r') as f:
            mapping_data = json.load(f)
        label_to_id = mapping_data['label_to_id']
        print(f"   ✅ Loaded {len(label_to_id)} categories")
        
        # Test transformers import
        print("3. Testing transformers...")
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        print("   ✅ Transformers imported successfully")
        
        # Test model loading
        print("4. Testing model initialization...")
        model_name = "bert-base-uncased"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print(f"   ✅ Tokenizer loaded: {model_name}")
        
        model = AutoModelForSequenceClassification.from_pretrained(
            model_name,
            num_labels=len(label_to_id)
        )
        print(f"   ✅ Model loaded with {len(label_to_id)} labels")
        
        # Test data preparation
        print("5. Testing data preparation...")
        texts = []
        labels = []
        
        for _, row in labeled_df.iterrows():
            text = row['text']
            label_str = row['label']
            
            # Take first label if multiple
            if ',' in label_str:
                primary_label = label_str.split(',')[0].strip()
            else:
                primary_label = label_str.strip()
            
            if primary_label in label_to_id:
                texts.append(text)
                labels.append(label_to_id[primary_label])
        
        print(f"   ✅ Prepared {len(texts)} training examples")
        
        # Test tokenization
        print("6. Testing tokenization...")
        sample_text = texts[0] if texts else "Test text"
        encoding = tokenizer(sample_text, truncation=True, padding='max_length', max_length=256)
        print(f"   ✅ Tokenization successful")
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ Ready for training with {len(texts)} examples across {len(label_to_id)} categories")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_training_setup()
    sys.exit(0 if success else 1)
