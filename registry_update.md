## 📁 **NEW FILES DETECTED**

*Auto-generated by maintain_file_registry.py*

### **Data Files**

- **File**: `base_knowledge_database.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 7632 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data_enhancement_report.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1189 bytes
- **Status**: Newly detected, needs documentation

- **File**: `pipeline_test_results.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 759 bytes
- **Status**: Newly detected, needs documentation

- **File**: `round2_error_analysis.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 13227 bytes
- **Status**: Newly detected, needs documentation

- **File**: `round2_improvement_plan.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 2580 bytes
- **Status**: Newly detected, needs documentation

- **File**: `round2_validation_results.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 7845 bytes
- **Status**: Newly detected, needs documentation

- **File**: `sample.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 439 bytes
- **Status**: Newly detected, needs documentation

- **File**: `sample_results.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1993 bytes
- **Status**: Newly detected, needs documentation

- **File**: `test_simple_statement.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 165 bytes
- **Status**: Newly detected, needs documentation

- **File**: `test_simple_statement_results.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 893 bytes
- **Status**: Newly detected, needs documentation

- **File**: `test_statements.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1558 bytes
- **Status**: Newly detected, needs documentation

- **File**: `threshold_optimization_results.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 2348 bytes
- **Status**: Newly detected, needs documentation

- **File**: `training_data_analysis.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 2702 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data/ai_batch_results_20250708_232449.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 21486 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data/ai_batch_results_20250708_235154.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 55214048 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data/ai_batch_results_20250709_014454.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 18407069 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data/ai_batch_results_20250709_015354.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1196405594 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data/statements_to_label.csv` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 15779 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data/statements_to_label_backup_20250709_153655.csv` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 23162 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data/statements_to_label_backup_20250709_154615.csv` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 23162 bytes
- **Status**: Newly detected, needs documentation

- **File**: `data/statements_to_label_backup_corrected.csv` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 15888 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/bert-test/config.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1831 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/bert-test/label_map.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 489 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/bert-test/special_tokens_map.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 132 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/bert-test/threshold_config.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 200 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/bert-test/tokenizer.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 711494 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/bert-test/tokenizer_config.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1277 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-model/config.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1465 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-model/special_tokens_map.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 132 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-model/tokenizer.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 711661 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-model/tokenizer_config.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1277 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-simple/config.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1993 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-simple/label_map.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 2208 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-simple/special_tokens_map.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 132 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-simple/tokenizer.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 711661 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/round2-simple/tokenizer_config.json` 📁 **VARIES**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: data_files
- **Size**: 1277 bytes
- **Status**: Newly detected, needs documentation

### **Documentation**

- **File**: `COMMIT_SUMMARY.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 4832 bytes
- **Status**: Newly detected, needs documentation

- **File**: `custom_model.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 4940 bytes
- **Status**: Newly detected, needs documentation

- **File**: `reference_guide.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 10956 bytes
- **Status**: Newly detected, needs documentation

- **File**: `registry_update.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 0 bytes
- **Status**: Newly detected, needs documentation

- **File**: `rules_analysis_guide.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 2893 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/Admin Dictionary.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 2592329 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/Algorithm.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 5524 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/DeBERTaandLLM(2).txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 5442 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/DeBERTa_Implementation_Plan.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 5873 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/DeBERTa_Labeling_Guide.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 4961 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/ImproveLLMKnowledgePlan.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 2548 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/InitialClassificationPlan.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 6122 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/Investigations.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 73603 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/ITERATIVE_IMPROVEMENT_PLAN.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 7062 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/Labeling_Tools_Comparison.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 5126 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/LLM_STARTUP_GUIDE.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 4102 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/NGVManagementSeriesV1.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 928722 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/ollama_server_output.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 106696 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/outpoint_pluspoint_definitions.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 5519 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/Plan.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 5196 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/project_structure.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 958 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/RecentFixes_2025-01-03.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 5110 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/RegexLayer(1).txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 5391 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/Session_Continuity_Guide.md` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 7132 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/Statementstoclass.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 46807 bytes
- **Status**: Newly detected, needs documentation

- **File**: `docs/Tech Dictionary.txt` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: documentation
- **Size**: 985678 bytes
- **Status**: Newly detected, needs documentation

### **Testing**

- **File**: `test_deberta_threshold.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 3484 bytes
- **Status**: Newly detected, needs documentation

- **File**: `test_fast_pipeline.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 10125 bytes
- **Status**: Newly detected, needs documentation

- **File**: `test_improved_threshold.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 5078 bytes
- **Status**: Newly detected, needs documentation

- **File**: `test_ollama_connection.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 3446 bytes
- **Status**: Newly detected, needs documentation

- **File**: `test_ollama_startup.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 5552 bytes
- **Status**: Newly detected, needs documentation

- **File**: `test_rag_integration.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 3121 bytes
- **Status**: Newly detected, needs documentation

- **File**: `validate_round2_performance.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 9254 bytes
- **Status**: Newly detected, needs documentation

- **File**: `validate_training_data.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 13249 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/check_batch_progress.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 3150 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/test_deberta.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: testing
- **Size**: 2660 bytes
- **Status**: Newly detected, needs documentation

### **Training Scripts**

- **File**: `train_round2_model.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: training_scripts
- **Size**: 9727 bytes
- **Status**: Newly detected, needs documentation

### **Core Algorithm**

- **File**: `models/analyze_errors.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 16995 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/debug_training.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 2691 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/rag_implementation.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 7032 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/simple_train.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 4396 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/statement_comparator.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 4309 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/train_deberta.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 10327 bytes
- **Status**: Newly detected, needs documentation

- **File**: `models/__init__.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 89 bytes
- **Status**: Newly detected, needs documentation

- **File**: `pipeline/__init__.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 46 bytes
- **Status**: Newly detected, needs documentation

- **File**: `rules/rules.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 5208 bytes
- **Status**: Newly detected, needs documentation

- **File**: `rules/rule_engine.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 1671 bytes
- **Status**: Newly detected, needs documentation

- **File**: `rules/__init__.py` ✅ **ACTIVE**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: core_algorithm
- **Size**: 164 bytes
- **Status**: Newly detected, needs documentation

### **Utilities**

- **File**: `utils/ai_label_assistant.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 8482 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/ai_response_reviewer.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 8925 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/batch_ai_labeler.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 8891 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/build_vector_store.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 1941 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/clean_scraped_data.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 4944 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/label_corrector.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 5915 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/manual_labeling_helper.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 7550 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/optimized_prompt_template.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 3532 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/patient_ai_labeler.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 7313 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/precise_timing_test.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 7220 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/prepare_training_data.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 1738 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/scraper.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 15984 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/slow_ai_labeler.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 7706 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/statement_categorizer.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 20968 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/ultra_slow_ai_test.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 2517 bytes
- **Status**: Newly detected, needs documentation

- **File**: `utils/update_session_status.py` 📁 **UTILITY**
- **Purpose**: [AUTO-DETECTED - NEEDS MANUAL DESCRIPTION]
- **Category**: utilities
- **Size**: 6176 bytes
- **Status**: Newly detected, needs documentation
