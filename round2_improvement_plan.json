{"timestamp": "2025-08-03T12:43:43.774108", "baseline_performance": {"overall_accuracy": 0.5172413793103449, "avg_confidence": 0.17157636094709922, "confidence_distribution": {"min": 0.08082187175750732, "max": 0.21919851005077362, "median": 0.1834540292620659, "q25": 0.15619419887661934, "q75": 0.1987209990620613}, "category_performance": {"CORRECT_DATA_PLUS": {"count": 50, "avg_confidence": 0.18124808475375176, "accuracy": 0.54, "min_confidence": 0.08082187175750732, "max_confidence": 0.21919851005077362}, "CORRECT_SEQUENCE_PLUS": {"count": 3, "avg_confidence": 0.14590080579121908, "accuracy": 0.6666666666666666, "min_confidence": 0.13251127302646637, "max_confidence": 0.1606849581003189}, "ADDED_DATA_OUT": {"count": 3, "avg_confidence": 0.09263861427704494, "accuracy": 0.0, "min_confidence": 0.08799176663160324, "max_confidence": 0.09658313542604446}, "WRONG_TARGET_OUT": {"count": 1, "avg_confidence": 0.08925994485616684, "accuracy": 0.0, "min_confidence": 0.08925994485616684, "max_confidence": 0.08925994485616684}, "OMITTED_DATA_OUT": {"count": 1, "avg_confidence": 0.08414649218320847, "accuracy": 1.0, "min_confidence": 0.08414649218320847, "max_confidence": 0.08414649218320847}}}, "recommendations": ["🚨 CRITICAL: Very low confidence - lower threshold to 0.1-0.15", "🔧 Increase training epochs to 5-8 for better convergence", "🎯 Focus on weak categories: CORRECT_DATA_PLUS, CORRECT_SEQUENCE_PLUS, ADDED_DATA_OUT", "📈 Add training data for: CORRECT_SEQUENCE_PLUS, ADDED_DATA_OUT, WRONG_TARGET_OUT", "🎚️ Consider threshold of 0.20 (75th percentile)", "🏗️ Consider larger base model (deberta-v3-base)"], "round2_targets": {"confidence_target": "0.4-0.6", "accuracy_target": "0.7+", "focus_areas": ["Threshold optimization", "Weak category improvement", "Training data augmentation", "Model architecture tuning"]}, "next_steps": ["Test different confidence thresholds", "Identify and augment weak categories", "Experiment with training parameters", "Train Round 2 model"]}