#!/usr/bin/env python3
"""
SAFE FILE CLEANUP SCRIPT
Only removes files that are definitely safe to remove
"""
import os
import shutil
from pathlib import Path

def safe_cleanup():
    """Perform safe cleanup operations"""
    print("🧹 PERFORMING SAFE CLEANUP")
    print("="*30)
    
    removed_count = 0
    
    # Remove __pycache__ directories
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            print(f"Removing: {pycache_path}")
            shutil.rmtree(pycache_path)
            dirs.remove('__pycache__')
            removed_count += 1
    
    # Remove .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                file_path = os.path.join(root, file)
                print(f"Removing: {file_path}")
                os.remove(file_path)
                removed_count += 1
    
    print(f"✅ Cleanup complete! Removed {removed_count} items")

if __name__ == "__main__":
    safe_cleanup()
