{"statements": [{"id": "s1", "text": "The factory reported record profits in Q1.", "source": "Finance-Dept", "timestamp": null, "outpoints": ["contrary_facts"], "pluspoints": [], "truth_score": 0.4}, {"id": "s2", "text": "We were unable to pay suppliers in Q1.", "source": "Accounts-Payable", "timestamp": null, "outpoints": ["contrary_facts"], "pluspoints": [], "truth_score": 0.4}, {"id": "s3", "text": "Everyone knows the CEO is embezzling funds!!", "source": "Office-<PERSON><PERSON><PERSON>", "timestamp": null, "outpoints": ["wrong_source"], "pluspoints": [], "truth_score": 0.4}], "classified_statements": [{"id": "s1", "text": "The factory reported record profits in Q1.", "source": "Finance-Dept", "timestamp": null, "outpoints": ["contrary_facts"], "pluspoints": [], "truth_score": 0.4, "classification": "Possibly False"}, {"id": "s2", "text": "We were unable to pay suppliers in Q1.", "source": "Accounts-Payable", "timestamp": null, "outpoints": ["contrary_facts"], "pluspoints": [], "truth_score": 0.4, "classification": "Possibly False"}, {"id": "s3", "text": "Everyone knows the CEO is embezzling funds!!", "source": "Office-<PERSON><PERSON><PERSON>", "timestamp": null, "outpoints": ["wrong_source"], "pluspoints": [], "truth_score": 0.4, "classification": "Possibly False"}], "whys": [], "report": {"summary": {"total_statements": 3, "red_statements": 0, "yellow_statements": 3, "green_statements": 0}, "whys": [], "recommendations": []}}