#!/usr/bin/env python3
"""
Simple Error Analysis for Round 2 DeBERTa Improvements
Focuses on key metrics and actionable insights
"""
import pandas as pd
import numpy as np
import json
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from collections import defaultdict, Counter

def analyze_model_performance():
    """Analyze current model performance with focus on Round 2 improvements"""
    print("🔍 ROUND 2 ERROR ANALYSIS")
    print("="*50)
    
    # Load model
    print("📂 Loading model...")
    model_dir = "models/bert-test"
    tokenizer = AutoTokenizer.from_pretrained(model_dir)
    model = AutoModelForSequenceClassification.from_pretrained(model_dir)
    
    # Load label mapping
    with open(f"{model_dir}/label_map.json") as f:
        label_map = json.load(f)
    id_to_label = {v: k for k, v in label_map.items()}
    
    # Load data
    print("📂 Loading data...")
    df = pd.read_csv("data/statements_to_label.csv")
    
    # Analyze predictions
    print("🔍 Analyzing predictions...")
    results = []
    confidences = []
    category_stats = defaultdict(list)
    
    model.eval()
    with torch.no_grad():
        for _, row in df.iterrows():
            if pd.isna(row['label']) or row['label'] == '':
                continue
            
            # Get prediction
            inputs = tokenizer(row['text'], return_tensors='pt', truncation=True, padding=True, max_length=512)
            outputs = model(**inputs)
            probs = torch.softmax(outputs.logits, dim=-1)
            confidence = torch.max(probs).item()
            predicted_class = torch.argmax(probs).item()
            predicted_label = id_to_label[predicted_class]
            
            # Parse true labels
            true_labels = [label.strip() for label in row['label'].split(',')]
            is_correct = predicted_label in true_labels
            
            # Store results
            result = {
                'text': row['text'][:100] + '...' if len(row['text']) > 100 else row['text'],
                'true_labels': true_labels,
                'predicted_label': predicted_label,
                'confidence': confidence,
                'correct': is_correct
            }
            results.append(result)
            confidences.append(confidence)
            
            # Track by category
            category_stats[predicted_label].append({
                'confidence': confidence,
                'correct': is_correct
            })
    
    return results, confidences, category_stats, label_map

def generate_insights(results, confidences, category_stats):
    """Generate key insights for Round 2 improvements"""
    print("\n📊 PERFORMANCE ANALYSIS")
    print("="*50)
    
    # Overall statistics
    total_predictions = len(results)
    correct_predictions = sum(1 for r in results if r['correct'])
    overall_accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    avg_confidence = np.mean(confidences)
    
    print(f"📈 Overall Performance:")
    print(f"  Total Predictions: {total_predictions}")
    print(f"  Correct Predictions: {correct_predictions}")
    print(f"  Overall Accuracy: {overall_accuracy:.3f}")
    print(f"  Average Confidence: {avg_confidence:.3f}")
    
    # Confidence distribution
    print(f"\n📊 Confidence Distribution:")
    print(f"  Min: {np.min(confidences):.3f}")
    print(f"  Max: {np.max(confidences):.3f}")
    print(f"  Median: {np.median(confidences):.3f}")
    print(f"  25th percentile: {np.percentile(confidences, 25):.3f}")
    print(f"  75th percentile: {np.percentile(confidences, 75):.3f}")
    
    # Category analysis
    print(f"\n🎯 Category Performance:")
    category_performance = {}
    
    for category, stats in category_stats.items():
        if len(stats) > 0:
            confidences_cat = [s['confidence'] for s in stats]
            accuracy_cat = np.mean([s['correct'] for s in stats])
            
            category_performance[category] = {
                'count': len(stats),
                'avg_confidence': np.mean(confidences_cat),
                'accuracy': accuracy_cat,
                'min_confidence': np.min(confidences_cat),
                'max_confidence': np.max(confidences_cat)
            }
            
            print(f"  {category}:")
            print(f"    Count: {len(stats)}")
            print(f"    Avg Confidence: {np.mean(confidences_cat):.3f}")
            print(f"    Accuracy: {accuracy_cat:.3f}")
    
    return {
        'overall_accuracy': overall_accuracy,
        'avg_confidence': avg_confidence,
        'confidence_distribution': {
            'min': float(np.min(confidences)),
            'max': float(np.max(confidences)),
            'median': float(np.median(confidences)),
            'q25': float(np.percentile(confidences, 25)),
            'q75': float(np.percentile(confidences, 75))
        },
        'category_performance': category_performance
    }

def identify_improvement_opportunities(insights):
    """Identify specific improvement opportunities for Round 2"""
    print(f"\n💡 ROUND 2 IMPROVEMENT OPPORTUNITIES")
    print("="*50)
    
    recommendations = []
    
    # Overall confidence analysis
    avg_conf = insights['avg_confidence']
    if avg_conf < 0.2:
        recommendations.append("🚨 CRITICAL: Very low confidence - lower threshold to 0.1-0.15")
        recommendations.append("🔧 Increase training epochs to 5-8 for better convergence")
    elif avg_conf < 0.3:
        recommendations.append("⚠️ Low confidence - consider lowering threshold to 0.2")
        recommendations.append("🔧 Try different learning rate (e.g., 2e-5 instead of 5e-5)")
    
    # Category-specific recommendations
    weak_categories = []
    strong_categories = []
    
    for category, perf in insights['category_performance'].items():
        if perf['avg_confidence'] < 0.25 or perf['accuracy'] < 0.5 or perf['count'] < 3:
            weak_categories.append(category)
        else:
            strong_categories.append(category)
    
    if weak_categories:
        recommendations.append(f"🎯 Focus on weak categories: {', '.join(weak_categories[:3])}")
        
        # Data augmentation needs
        few_examples = [cat for cat, perf in insights['category_performance'].items() if perf['count'] < 5]
        if few_examples:
            recommendations.append(f"📈 Add training data for: {', '.join(few_examples[:3])}")
    
    # Threshold recommendations
    q75_conf = insights['confidence_distribution']['q75']
    if q75_conf < 0.3:
        recommendations.append(f"🎚️ Consider threshold of {q75_conf:.2f} (75th percentile)")
    
    # Architecture recommendations
    if len(weak_categories) > len(strong_categories):
        recommendations.append("🏗️ Consider larger base model (deberta-v3-base)")
    
    print("🚀 Immediate Actions:")
    for i, rec in enumerate(recommendations[:5], 1):
        print(f"  {i}. {rec}")
    
    return recommendations

def save_round2_plan(insights, recommendations):
    """Save Round 2 improvement plan"""
    plan = {
        'timestamp': pd.Timestamp.now().isoformat(),
        'baseline_performance': insights,
        'recommendations': recommendations,
        'round2_targets': {
            'confidence_target': '0.4-0.6',
            'accuracy_target': '0.7+',
            'focus_areas': [
                'Threshold optimization',
                'Weak category improvement', 
                'Training data augmentation',
                'Model architecture tuning'
            ]
        },
        'next_steps': [
            'Test different confidence thresholds',
            'Identify and augment weak categories',
            'Experiment with training parameters',
            'Train Round 2 model'
        ]
    }
    
    with open('round2_improvement_plan.json', 'w') as f:
        json.dump(plan, f, indent=2, default=str)
    
    print(f"\n💾 Round 2 plan saved to: round2_improvement_plan.json")
    return plan

def main():
    try:
        # Analyze current performance
        results, confidences, category_stats, label_map = analyze_model_performance()
        
        # Generate insights
        insights = generate_insights(results, confidences, category_stats)
        
        # Identify improvements
        recommendations = identify_improvement_opportunities(insights)
        
        # Save plan
        plan = save_round2_plan(insights, recommendations)
        
        print(f"\n✅ Round 2 error analysis complete!")
        print(f"📊 Baseline: {insights['avg_confidence']:.3f} confidence, {insights['overall_accuracy']:.3f} accuracy")
        print(f"🎯 Target: 0.4-0.6 confidence, 0.7+ accuracy")
        print(f"🚀 Ready to implement Round 2 improvements!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
