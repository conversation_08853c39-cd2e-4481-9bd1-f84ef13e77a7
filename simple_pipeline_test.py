#!/usr/bin/env python3
"""
Simple Pipeline Test - Minimal resource usage
Test the complete pipeline with one statement and proper error handling
"""
import json
import time
import sys

def test_pipeline_minimal():
    """Test pipeline with minimal resource usage"""
    print("🧪 MINIMAL PIPELINE TEST")
    print("="*30)
    
    # Create simple test statement
    test_statement = {
        "id": "simple_test",
        "text": "The company reported profits but couldn't pay bills.",
        "time": "2025-08-03",
        "source": "Test"
    }
    
    print(f"📝 Testing statement: {test_statement['text']}")
    
    try:
        # Test 1: Check if Ollama is running
        print("\n1. 🔍 Checking Ollama server...")
        import requests
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                print("   ✅ Ollama server is running")
            else:
                print(f"   ❌ Ollama server issue: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ Ollama not accessible: {e}")
            return False
        
        # Test 2: Test DeBERTa only (no LLM)
        print("\n2. 🧠 Testing DeBERTa layer only...")
        from truth_algorithm import TruthAlgorithm
        
        # Initialize with LLM disabled to avoid timeouts
        algorithm = TruthAlgorithm(
            confidence_threshold=0.05,
            use_llm=False,  # Disable LLM for this test
            use_deberta=True
        )
        
        print("   ✅ Algorithm initialized")
        
        # Process the statement
        results = algorithm.analyze_statements([test_statement])
        
        print("   ✅ Statement processed")
        
        # Display results
        if results:
            result = results[0]
            print(f"\n📊 RESULTS:")
            print(f"   Classification: {result.get('classification', 'Unknown')}")
            print(f"   Score: {result.get('score', 0):.2f}")
            print(f"   Outpoints: {result.get('outpoints', [])}")
            print(f"   Pluspoints: {result.get('pluspoints', [])}")
        
        print(f"\n✅ DeBERTa layer test PASSED")
        
        # Test 3: Test with LLM (single statement, extended timeout)
        print(f"\n3. 🤖 Testing with LLM (this may take several minutes)...")
        
        algorithm_with_llm = TruthAlgorithm(
            confidence_threshold=0.05,
            use_llm=True,
            use_deberta=True
        )
        
        print("   🔄 Processing with LLM... (please wait)")
        start_time = time.time()
        
        results_with_llm = algorithm_with_llm.analyze_statements([test_statement])
        
        duration = time.time() - start_time
        print(f"   ⏱️ LLM processing took {duration:.1f} seconds")
        
        if results_with_llm:
            result = results_with_llm[0]
            print(f"\n📊 RESULTS WITH LLM:")
            print(f"   Classification: {result.get('classification', 'Unknown')}")
            print(f"   Score: {result.get('score', 0):.2f}")
            print(f"   Outpoints: {result.get('outpoints', [])}")
            print(f"   Pluspoints: {result.get('pluspoints', [])}")
        
        print(f"\n🎉 COMPLETE PIPELINE TEST PASSED!")
        print(f"✅ Both DeBERTa and LLM layers working")
        print(f"✅ Round 2 model integration successful")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 SIMPLE PIPELINE TEST - ROUND 2")
    print("="*40)
    
    success = test_pipeline_minimal()
    
    if success:
        print(f"\n🎉 SUCCESS! Pipeline is fully operational")
        print(f"📁 Model: models/round2-simple/")
        print(f"🎯 Ready for optimization work")
    else:
        print(f"\n❌ FAILED! Check error messages above")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
