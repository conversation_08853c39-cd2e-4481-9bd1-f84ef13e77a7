#!/usr/bin/env python3
"""
Simple validation test to confirm our pipeline components work
"""

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from rules.rule_engine import rule_classify
        print("✅ Rule engine imported")
    except Exception as e:
        print(f"❌ Rule engine import failed: {e}")
        return False
    
    try:
        from models.deberta_classifier import DeBERTaClassifier
        print("✅ DeBERTa classifier imported")
    except Exception as e:
        print(f"❌ DeBERTa classifier import failed: {e}")
        return False
    
    try:
        from truth_algorithm import TruthAlgorithm
        print("✅ Truth algorithm imported")
    except Exception as e:
        print(f"❌ Truth algorithm import failed: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality of each component"""
    print("\n🧪 Testing basic functionality...")
    
    # Test rule engine
    try:
        from rules.rule_engine import rule_classify
        result = rule_classify("The president announced new policies yesterday.")
        print(f"✅ Rule engine working: {result}")
    except Exception as e:
        print(f"❌ Rule engine failed: {e}")
        return False
    
    # Test DeBERTa classifier
    try:
        from models.deberta_classifier import DeBERTaClassifier
        classifier = DeBERTaClassifier(model_dir="models/bert-test", threshold=0.3)
        if classifier.loaded:
            result = classifier.classify("Test statement")
            print(f"✅ DeBERTa classifier working: {result}")
        else:
            print("⚠️ DeBERTa model not loaded (expected)")
    except Exception as e:
        print(f"❌ DeBERTa classifier failed: {e}")
        return False
    
    return True

def main():
    print("🚀 SIMPLE VALIDATION TEST")
    print("="*40)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed")
        return False
    
    # Test basic functionality
    if not test_basic_functionality():
        print("\n❌ Functionality test failed")
        return False
    
    print("\n✅ All basic tests passed!")
    print("\n📋 Summary:")
    print("  ✅ All modules import correctly")
    print("  ✅ Rule engine functional")
    print("  ✅ DeBERTa classifier functional")
    print("  ✅ System ready for full pipeline testing")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
