@echo off
echo Starting Ollama server...

REM Try common Ollama installation paths
if exist "C:\Program Files\Ollama\ollama.exe" (
    echo Found Ollama at C:\Program Files\Ollama\
    "C:\Program Files\Ollama\ollama.exe" serve
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe" (
    echo Found Ollama at C:\Users\<USER>\AppData\Local\Programs\Ollama\
    "C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe" serve
    goto :end
)

if exist "C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Ollama\ollama.exe" (
    echo Found Ollama in Start Menu Programs
    "C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Ollama\ollama.exe" serve
    goto :end
)

REM Try to run ollama from PATH
echo Trying to run ollama from PATH...
ollama serve

:end
echo Ollama startup script finished.
pause
