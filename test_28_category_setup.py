#!/usr/bin/env python3
"""
Quick test to verify 28-category setup is working
"""
import pandas as pd
import json
from pathlib import Path

def test_setup():
    print("🧪 TESTING 28-CATEGORY SETUP")
    print("="*40)
    
    # Check files exist
    files_to_check = [
        "data/complete_28_category_dataset.csv",
        "official_28_category_mapping.json"
    ]
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            return False
    
    # Load and validate data
    df = pd.read_csv("data/complete_28_category_dataset.csv")
    labeled_df = df[df['label'].notna() & (df['label'] != '')]
    
    print(f"\n📊 Dataset Stats:")
    print(f"Total statements: {len(df)}")
    print(f"Labeled statements: {len(labeled_df)}")
    
    # Load mapping
    with open('official_28_category_mapping.json', 'r') as f:
        mapping_data = json.load(f)
    
    label_to_id = mapping_data['label_to_id']
    id_to_label = mapping_data['id_to_label']
    
    print(f"Categories in mapping: {len(label_to_id)}")
    
    # Check category coverage
    found_categories = set()
    for _, row in labeled_df.iterrows():
        labels = [label.strip() for label in row['label'].split(',')]
        found_categories.update(labels)
    
    print(f"Categories in data: {len(found_categories)}")
    
    # Check for missing categories
    expected_categories = set(label_to_id.keys())
    missing = expected_categories - found_categories
    extra = found_categories - expected_categories
    
    if missing:
        print(f"❌ Missing categories: {missing}")
    else:
        print(f"✅ All expected categories present")
    
    if extra:
        print(f"⚠️ Extra categories: {extra}")
    
    # Count examples per category
    from collections import Counter
    category_counts = Counter()
    for _, row in labeled_df.iterrows():
        labels = [label.strip() for label in row['label'].split(',')]
        for label in labels:
            category_counts[label] += 1
    
    # Check for rare categories
    rare_categories = [cat for cat, count in category_counts.items() if count < 2]
    if rare_categories:
        print(f"⚠️ Categories with <2 examples: {rare_categories}")
    
    print(f"\n🎯 SETUP STATUS:")
    if not missing and len(found_categories) >= 28:
        print("✅ 28-category setup is READY for training!")
        return True
    else:
        print("❌ Setup needs fixes before training")
        return False

if __name__ == "__main__":
    success = test_setup()
    exit(0 if success else 1)
