#!/usr/bin/env python3
"""
Complete Pipeline Test - Tests all three layers of the Truth Algorithm
1. Rule-based layer (regex patterns)
2. DeBERTa classifier layer 
3. LLM layer with RAG

This validates that our entire system works end-to-end with Round 2 model.
"""
import json
import time
import sys
from datetime import datetime

def create_test_statements():
    """Create a diverse set of test statements to validate all layers"""
    return [
        {
            "id": "test_1",
            "text": "The president said the economy is improving, but unemployment rose by 2% last month.",
            "source": "News Report",
            "timestamp": "2025-01-15",
            "expected_outpoints": ["contrary_facts"],
            "expected_pluspoints": ["correct_source", "time_noted"]
        },
        {
            "id": "test_2", 
            "text": "He announced the new policy yesterday.",
            "source": "Unknown",
            "timestamp": "2025-01-15",
            "expected_outpoints": ["omitted_data"],
            "expected_pluspoints": []
        },
        {
            "id": "test_3",
            "text": "According to the Department of Labor, job creation increased by 150,000 positions in December 2024, marking the third consecutive month of growth.",
            "source": "Official Report",
            "timestamp": "2025-01-15",
            "expected_outpoints": [],
            "expected_pluspoints": ["data_proven_factual", "correct_source", "time_noted", "adequate_data"]
        },
        {
            "id": "test_4",
            "text": "The meeting will happen soon at the usual place.",
            "source": "Internal Memo",
            "timestamp": "2025-01-15",
            "expected_outpoints": ["omitted_data", "dropped_time"],
            "expected_pluspoints": []
        },
        {
            "id": "test_5",
            "text": "Scientists have discovered that water boils at 50 degrees Celsius at sea level.",
            "source": "Research Paper",
            "timestamp": "2025-01-15",
            "expected_outpoints": ["falsehood"],
            "expected_pluspoints": []
        }
    ]

def test_layer_individually():
    """Test each layer individually to isolate any issues"""
    print("🧪 INDIVIDUAL LAYER TESTING")
    print("=" * 50)
    
    test_text = "The president announced new policies yesterday."
    
    # Test 1: Rule-based layer
    print("\n1. Testing Rule-based Layer...")
    try:
        from rules.rule_engine import rule_classify
        rule_results = rule_classify(test_text)
        print(f"✅ Rule-based layer working: {rule_results}")
    except Exception as e:
        print(f"❌ Rule-based layer error: {e}")
        return False
    
    # Test 2: DeBERTa layer
    print("\n2. Testing DeBERTa Layer...")
    try:
        from models.deberta_classifier import DeBERTaClassifier
        deberta = DeBERTaClassifier(model_dir="models/bert-test", threshold=0.5)
        if deberta.loaded:
            result = deberta.classify(test_text)
            print(f"✅ DeBERTa layer working: {result}")
        else:
            print("⚠️ DeBERTa model not loaded (expected for first run)")
    except Exception as e:
        print(f"❌ DeBERTa layer error: {e}")
        return False
    
    # Test 3: LLM layer (only if Ollama is running)
    print("\n3. Testing LLM Layer...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            from models.llm_evaluator import LLMEvaluator
            llm = LLMEvaluator(model_name="truth-evaluator")
            result = llm.evaluate_outpoint("omitted_data", test_text, {})
            print(f"✅ LLM layer working: {result}")
        else:
            print("⚠️ Ollama not running - LLM layer skipped")
    except Exception as e:
        print(f"❌ LLM layer error: {e}")
        print("💡 Make sure Ollama is running: ollama serve")
        return False
    
    return True

def test_complete_pipeline():
    """Test the complete integrated pipeline"""
    print("\n🚀 COMPLETE PIPELINE TESTING")
    print("=" * 50)
    
    # Create test data file
    test_statements = create_test_statements()
    with open("test_statements.json", "w") as f:
        json.dump(test_statements, f, indent=2)
    
    print(f"📝 Created test file with {len(test_statements)} statements")
    
    # Test different pipeline configurations
    configurations = [
        {"name": "Rules Only", "args": ["--rules-only"]},
        {"name": "Rules + DeBERTa", "args": ["--no-llm"]},
        {"name": "Complete Pipeline", "args": []}
    ]
    
    results = {}
    
    for config in configurations:
        print(f"\n🔧 Testing: {config['name']}")
        print("-" * 30)
        
        try:
            # Import and run the truth algorithm
            from truth_algorithm import TruthAlgorithm
            from models.llm_evaluator import LLMEvaluator
            
            # Configure based on test
            use_llm = "--no-llm" not in config["args"] and "--rules-only" not in config["args"]
            use_deberta = "--no-deberta" not in config["args"] and "--rules-only" not in config["args"]
            
            # Initialize LLM evaluator if needed
            evaluator = None
            if use_llm:
                try:
                    evaluator = LLMEvaluator(model_name="truth-evaluator")
                except:
                    print("⚠️ LLM not available, skipping LLM tests")
                    use_llm = False
            
            # Initialize algorithm
            algorithm = TruthAlgorithm(
                llm_evaluator=evaluator,
                use_llm=use_llm,
                use_deberta=use_deberta
            )
            
            # Process statements
            start_time = time.time()
            pipeline_results = algorithm.process_statements(test_statements)
            end_time = time.time()
            
            # Analyze results
            classified = pipeline_results["classified_statements"]
            processing_time = end_time - start_time
            
            print(f"✅ Processed {len(classified)} statements in {processing_time:.2f}s")
            
            # Count classifications
            classifications = {}
            for stmt in classified:
                classification = stmt["classification"]
                classifications[classification] = classifications.get(classification, 0) + 1
            
            print(f"📊 Classifications: {classifications}")
            
            # Count outpoints and pluspoints found
            total_outpoints = sum(len(stmt["outpoints"]) for stmt in classified)
            total_pluspoints = sum(len(stmt["pluspoints"]) for stmt in classified)
            
            print(f"🔍 Found {total_outpoints} outpoints, {total_pluspoints} pluspoints")
            
            results[config["name"]] = {
                "success": True,
                "processing_time": processing_time,
                "classifications": classifications,
                "outpoints": total_outpoints,
                "pluspoints": total_pluspoints
            }
            
        except Exception as e:
            print(f"❌ Error in {config['name']}: {e}")
            results[config["name"]] = {"success": False, "error": str(e)}
    
    return results

def generate_test_report(individual_results, pipeline_results):
    """Generate a comprehensive test report"""
    print("\n📋 COMPREHENSIVE TEST REPORT")
    print("=" * 60)
    
    # Individual layer results
    print("\n🔧 Individual Layer Status:")
    if individual_results:
        print("✅ All individual layers working correctly")
    else:
        print("❌ Some individual layers have issues")
    
    # Pipeline results
    print("\n🚀 Pipeline Configuration Results:")
    for config_name, result in pipeline_results.items():
        if result["success"]:
            print(f"✅ {config_name}: {result['processing_time']:.2f}s, "
                  f"{result['outpoints']} outpoints, {result['pluspoints']} pluspoints")
        else:
            print(f"❌ {config_name}: {result['error']}")
    
    # Recommendations
    print("\n💡 Recommendations:")
    
    successful_configs = [name for name, result in pipeline_results.items() if result["success"]]
    
    if len(successful_configs) == 3:
        print("🎉 All pipeline configurations working! Ready for Round 2 improvements.")
    elif "Rules Only" in successful_configs:
        print("✅ Rule-based layer working - good foundation")
        if "Rules + DeBERTa" not in successful_configs:
            print("🔧 DeBERTa layer needs attention - check model loading")
        if "Complete Pipeline" not in successful_configs:
            print("🔧 LLM layer needs attention - check Ollama server")
    else:
        print("🚨 Basic rule layer has issues - check rule engine")
    
    # Next steps
    print("\n🎯 Next Steps:")
    if len(successful_configs) >= 2:
        print("1. ✅ Ready to proceed with Round 2 DeBERTa improvements")
        print("2. 🔄 Begin error analysis and iterative training")
        print("3. 📊 Set up performance dashboards")
    else:
        print("1. 🔧 Fix pipeline issues identified above")
        print("2. 🔄 Re-run complete pipeline test")
        print("3. 📋 Document any remaining issues")

def main():
    """Main test execution"""
    print("🧪 TRUTH ALGORITHM COMPLETE PIPELINE TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Purpose: Validate all layers before Round 2 improvements")
    
    # Test individual layers
    individual_success = test_layer_individually()
    
    # Test complete pipeline
    pipeline_results = test_complete_pipeline()
    
    # Generate report
    generate_test_report(individual_success, pipeline_results)
    
    # Save detailed results
    detailed_results = {
        "timestamp": datetime.now().isoformat(),
        "individual_layers": individual_success,
        "pipeline_configurations": pipeline_results
    }
    
    with open("pipeline_test_results.json", "w") as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: pipeline_test_results.json")
    
    return len([r for r in pipeline_results.values() if r["success"]]) >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
