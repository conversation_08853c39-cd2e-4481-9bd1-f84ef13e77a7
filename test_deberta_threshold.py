#!/usr/bin/env python3
"""
Direct test of DeBERTa classifier with improved threshold
"""
from models.deberta_classifier import DeBERTaClassifier

def test_deberta_directly():
    """Test DeBERTa classifier directly with new threshold"""
    print("🧪 TESTING DEBERTA CLASSIFIER DIRECTLY")
    print("="*50)
    
    # Test with old threshold (0.3)
    print("📊 Testing with OLD threshold (0.3)...")
    classifier_old = DeBERTaClassifier("models/bert-test", threshold=0.3)
    
    # Test with new threshold (0.05)
    print("📊 Testing with NEW threshold (0.05)...")
    classifier_new = DeBERTaClassifier("models/bert-test", threshold=0.05)
    
    # Test statements
    test_statements = [
        "She said she herself had no intention of giving up the lucrative trade but felt others should be aware of what could lie ahead of them.",
        "<PERSON>, 39, and <PERSON>, 32, both from Cumbria, deny the charges.",
        "Chinese Australians are a crucial group that deserted the conservative Liberal-National coalition in the last election.",
        "<PERSON> has told the BBC that pressure from the Trump administration on Ukraine to give up territory to Russia is \"modern-day appeasement\".",
        "London's Darjeeling Express – the only all-female Indian kitchen in the world – is ground zero for <PERSON><PERSON>."
    ]
    
    print(f"\n🔍 Testing {len(test_statements)} statements...")
    print("-" * 100)
    print(f"{'Statement':<50} {'Old (0.3)':<20} {'New (0.05)':<20} {'Confidence':<12}")
    print("-" * 100)
    
    old_predictions = 0
    new_predictions = 0
    
    for statement in test_statements:
        # Test with old threshold
        old_label, old_conf = classifier_old.classify(statement)
        if old_label:
            old_predictions += 1
            old_result = f"{old_label[:15]}..."
        else:
            old_result = "None"
        
        # Test with new threshold
        new_label, new_conf = classifier_new.classify(statement)
        if new_label:
            new_predictions += 1
            new_result = f"{new_label[:15]}..."
        else:
            new_result = "None"
        
        # Display results
        statement_short = statement[:45] + "..." if len(statement) > 45 else statement
        print(f"{statement_short:<50} {old_result:<20} {new_result:<20} {new_conf:<12.3f}")
    
    # Summary
    print("-" * 100)
    print(f"\n📊 COMPARISON SUMMARY")
    print("="*50)
    print(f"Old Threshold (0.3): {old_predictions}/{len(test_statements)} predictions ({old_predictions/len(test_statements)*100:.1f}%)")
    print(f"New Threshold (0.05): {new_predictions}/{len(test_statements)} predictions ({new_predictions/len(test_statements)*100:.1f}%)")
    
    improvement = new_predictions - old_predictions
    if improvement > 0:
        print(f"✅ IMPROVEMENT: +{improvement} more predictions ({improvement/len(test_statements)*100:.1f}% increase)")
    elif improvement == 0:
        print(f"➖ NO CHANGE: Same number of predictions")
    else:
        print(f"❌ REGRESSION: {improvement} fewer predictions")
    
    if new_predictions == len(test_statements):
        print(f"🎉 SUCCESS: 100% coverage achieved!")
    
    return old_predictions, new_predictions

if __name__ == "__main__":
    old_count, new_count = test_deberta_directly()
    print(f"\n✅ Direct DeBERTa test complete!")
    print(f"📈 Coverage improved from {old_count}/5 to {new_count}/5 statements")
