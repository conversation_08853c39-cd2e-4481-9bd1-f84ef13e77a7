#!/usr/bin/env python3
"""
Fast Pipeline Test - Tests rule-based and DeBERTa layers quickly
Skips LLM layer due to slow response times on this computer
"""
import json
import time
import sys
from datetime import datetime

def test_rule_layer():
    """Test the rule-based layer"""
    print("🔧 Testing Rule-based Layer...")
    try:
        from rules.rule_engine import rule_classify
        
        test_cases = [
            "The president announced new policies yesterday.",
            "He said something important.",
            "According to NASA, the moon landing happened in 1969.",
            "The meeting will happen soon.",
            "Scientists discovered that water boils at 50°C at sea level."
        ]
        
        results = []
        for text in test_cases:
            result = rule_classify(text)
            results.append({"text": text, "result": result})
            print(f"  ✓ '{text[:30]}...' → {result}")
        
        print(f"✅ Rule-based layer working! Processed {len(test_cases)} statements")
        return True, results
        
    except Exception as e:
        print(f"❌ Rule-based layer error: {e}")
        return False, []

def test_deberta_layer():
    """Test the DeBERTa layer"""
    print("\n🤖 Testing DeBERTa Layer...")
    try:
        from models.deberta_classifier import DeBERTaClassifier
        
        # Try to load our trained model
        deberta = DeBERTaClassifier(model_dir="models/bert-test", threshold=0.3)
        
        if not deberta.loaded:
            print("⚠️ DeBERTa model not loaded - this is expected for first run")
            return True, []
        
        test_cases = [
            "The president announced new policies yesterday.",
            "He said something important.", 
            "According to NASA, the moon landing happened in 1969.",
            "The meeting will happen soon.",
            "Scientists discovered that water boils at 50°C at sea level."
        ]
        
        results = []
        for text in test_cases:
            result = deberta.classify(text)
            results.append({"text": text, "result": result})
            print(f"  ✓ '{text[:30]}...' → {result}")
        
        print(f"✅ DeBERTa layer working! Processed {len(test_cases)} statements")
        return True, results
        
    except Exception as e:
        print(f"❌ DeBERTa layer error: {e}")
        return False, []

def test_pipeline_integration():
    """Test the integrated pipeline without LLM"""
    print("\n🚀 Testing Pipeline Integration (Rules + DeBERTa)...")
    
    try:
        # Import the main algorithm
        from truth_algorithm import TruthAlgorithm
        
        # Create test statements
        test_statements = [
            {
                "id": "test_1",
                "text": "The president said the economy is improving, but unemployment rose by 2% last month.",
                "source": "News Report",
                "timestamp": "2025-01-15"
            },
            {
                "id": "test_2",
                "text": "He announced the new policy yesterday.",
                "source": "Unknown", 
                "timestamp": "2025-01-15"
            },
            {
                "id": "test_3",
                "text": "According to the Department of Labor, job creation increased by 150,000 positions in December 2024.",
                "source": "Official Report",
                "timestamp": "2025-01-15"
            }
        ]
        
        # Initialize algorithm without LLM
        algorithm = TruthAlgorithm(
            llm_evaluator=None,
            use_llm=False,
            use_deberta=True
        )
        
        print(f"📝 Processing {len(test_statements)} test statements...")
        
        start_time = time.time()
        results = algorithm.process_statements(test_statements)
        end_time = time.time()
        
        processing_time = end_time - start_time
        classified = results["classified_statements"]
        
        print(f"✅ Processed {len(classified)} statements in {processing_time:.2f}s")
        
        # Analyze results
        total_outpoints = sum(len(stmt["outpoints"]) for stmt in classified)
        total_pluspoints = sum(len(stmt["pluspoints"]) for stmt in classified)
        
        print(f"🔍 Found {total_outpoints} outpoints, {total_pluspoints} pluspoints")
        
        # Show detailed results
        for stmt in classified:
            print(f"\n📄 Statement: '{stmt['text'][:50]}...'")
            print(f"   Classification: {stmt['classification']}")
            if stmt['outpoints']:
                print(f"   Outpoints: {stmt['outpoints']}")
            if stmt['pluspoints']:
                print(f"   Pluspoints: {stmt['pluspoints']}")
        
        return True, {
            "processing_time": processing_time,
            "statements_processed": len(classified),
            "outpoints_found": total_outpoints,
            "pluspoints_found": total_pluspoints,
            "results": classified
        }
        
    except Exception as e:
        print(f"❌ Pipeline integration error: {e}")
        import traceback
        traceback.print_exc()
        return False, {}

def check_llm_availability():
    """Check if LLM is available (but don't wait long)"""
    print("\n🧠 Checking LLM Availability...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            truth_evaluator_available = any('truth-evaluator' in model.get('name', '') for model in models)
            print(f"✅ Ollama running with {len(models)} models")
            if truth_evaluator_available:
                print("✅ truth-evaluator model available (but slow)")
            else:
                print("⚠️ truth-evaluator model not found")
            return True
        else:
            print(f"❌ Ollama responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ LLM not available: {e}")
        return False

def generate_report(rule_success, rule_results, deberta_success, deberta_results, 
                   pipeline_success, pipeline_results, llm_available):
    """Generate a comprehensive test report"""
    print("\n" + "="*60)
    print("📋 PIPELINE TEST REPORT")
    print("="*60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Component Status
    print("\n🔧 Component Status:")
    print(f"  Rules Layer:     {'✅ Working' if rule_success else '❌ Failed'}")
    print(f"  DeBERTa Layer:   {'✅ Working' if deberta_success else '❌ Failed'}")
    print(f"  LLM Layer:       {'✅ Available (slow)' if llm_available else '❌ Not available'}")
    print(f"  Integration:     {'✅ Working' if pipeline_success else '❌ Failed'}")
    
    # Performance Summary
    if pipeline_success and pipeline_results:
        print(f"\n📊 Performance Summary:")
        print(f"  Processing Time: {pipeline_results['processing_time']:.2f}s")
        print(f"  Statements:      {pipeline_results['statements_processed']}")
        print(f"  Outpoints:       {pipeline_results['outpoints_found']}")
        print(f"  Pluspoints:      {pipeline_results['pluspoints_found']}")
    
    # Readiness Assessment
    print(f"\n🎯 System Readiness:")
    
    working_components = sum([rule_success, deberta_success, pipeline_success])
    
    if working_components >= 2:
        print("✅ System is ready for Round 2 improvements!")
        print("✅ Core pipeline (Rules + DeBERTa) is functional")
        if llm_available:
            print("✅ LLM layer available for enhanced accuracy")
        else:
            print("⚠️ LLM layer slow but available when needed")
    else:
        print("❌ System needs attention before Round 2")
        print("🔧 Fix component issues before proceeding")
    
    # Next Steps
    print(f"\n🚀 Recommended Next Steps:")
    if working_components >= 2:
        print("1. ✅ Begin Round 2 DeBERTa improvements")
        print("2. 📊 Run error analysis on current model")
        print("3. 🔄 Start iterative training cycle")
        print("4. 📈 Set up performance dashboards")
    else:
        print("1. 🔧 Fix failing components")
        print("2. 🔄 Re-run pipeline test")
        print("3. 📋 Document remaining issues")

def main():
    """Main test execution"""
    print("🧪 TRUTH ALGORITHM FAST PIPELINE TEST")
    print("="*60)
    print("🎯 Purpose: Validate core layers (Rules + DeBERTa)")
    print("⚠️ Note: Skipping LLM tests due to slow response times")
    
    # Test individual layers
    rule_success, rule_results = test_rule_layer()
    deberta_success, deberta_results = test_deberta_layer()
    
    # Check LLM availability (but don't test it)
    llm_available = check_llm_availability()
    
    # Test integrated pipeline
    pipeline_success, pipeline_results = test_pipeline_integration()
    
    # Generate report
    generate_report(rule_success, rule_results, deberta_success, deberta_results,
                   pipeline_success, pipeline_results, llm_available)
    
    # Save results
    test_results = {
        "timestamp": datetime.now().isoformat(),
        "rule_layer": {"success": rule_success, "results": rule_results},
        "deberta_layer": {"success": deberta_success, "results": deberta_results},
        "llm_layer": {"available": llm_available, "tested": False, "reason": "slow_computer"},
        "pipeline_integration": {"success": pipeline_success, "results": pipeline_results}
    }
    
    with open("fast_pipeline_test_results.json", "w") as f:
        json.dump(test_results, f, indent=2)
    
    print(f"\n💾 Results saved to: fast_pipeline_test_results.json")
    
    # Return success if core components work
    return rule_success and pipeline_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
