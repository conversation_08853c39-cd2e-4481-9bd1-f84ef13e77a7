#!/usr/bin/env python3
"""
Test the improved threshold in our pipeline
"""
import pandas as pd
from truth_algorithm import TruthAlgorithm

def test_improved_pipeline():
    """Test the pipeline with the new optimized threshold"""
    print("🧪 TESTING IMPROVED THRESHOLD (0.05)")
    print("="*50)
    
    # Initialize with optimized threshold
    print("📂 Initializing Truth Algorithm with threshold 0.05...")
    truth_algo = TruthAlgorithm(confidence_threshold=0.05, use_llm=False)
    
    # Load test data
    print("📂 Loading test data...")
    df = pd.read_csv("data/statements_to_label.csv")
    
    # Test on a few statements
    test_statements = [
        "She said she herself had no intention of giving up the lucrative trade but felt others should be aware of what could lie ahead of them.",
        "<PERSON>, 39, and <PERSON>, 32, both from Cumbria, deny the charges.",
        "Chinese Australians are a crucial group that deserted the conservative Liberal-National coalition in the last election.",
        "<PERSON> has told the BBC that pressure from the Trump administration on Ukraine to give up territory to Russia is \"modern-day appeasement\" in an exclusive interview.",
        "London's Darjeeling Express – the only all-female Indian kitchen in the world – is ground zero for <PERSON><PERSON>."
    ]
    
    print(f"\n🔍 Testing {len(test_statements)} statements...")
    print("-" * 80)
    
    results = []
    for i, statement in enumerate(test_statements, 1):
        print(f"\n📝 Statement {i}:")
        print(f"   Text: {statement[:60]}...")
        
        try:
            # Process statement
            result = truth_algo.process_statements([statement])
            
            if result and len(result) > 0:
                statement_result = result[0]
                
                # Extract DeBERTa results
                deberta_results = statement_result.get('deberta_analysis', {})
                if deberta_results:
                    label = deberta_results.get('label', 'None')
                    confidence = deberta_results.get('confidence', 0.0)
                    print(f"   ✅ DeBERTa: {label} (confidence: {confidence:.3f})")
                else:
                    print(f"   ❌ DeBERTa: No prediction made")
                
                # Extract rule-based results
                rule_results = statement_result.get('rule_analysis', {})
                if rule_results:
                    rule_labels = rule_results.get('labels', [])
                    if rule_labels:
                        print(f"   🔧 Rules: {', '.join(rule_labels)}")
                    else:
                        print(f"   🔧 Rules: No matches")
                
                results.append({
                    'statement': statement[:60] + '...',
                    'deberta_label': deberta_results.get('label'),
                    'deberta_confidence': deberta_results.get('confidence', 0.0),
                    'rule_labels': rule_results.get('labels', [])
                })
                
            else:
                print(f"   ❌ No results returned")
                results.append({
                    'statement': statement[:60] + '...',
                    'deberta_label': None,
                    'deberta_confidence': 0.0,
                    'rule_labels': []
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({
                'statement': statement[:60] + '...',
                'deberta_label': 'ERROR',
                'deberta_confidence': 0.0,
                'rule_labels': []
            })
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("="*50)
    
    deberta_predictions = sum(1 for r in results if r['deberta_label'] and r['deberta_label'] != 'ERROR')
    rule_predictions = sum(1 for r in results if r['rule_labels'])
    
    print(f"Total Statements: {len(test_statements)}")
    print(f"DeBERTa Predictions: {deberta_predictions}/{len(test_statements)} ({deberta_predictions/len(test_statements)*100:.1f}%)")
    print(f"Rule Predictions: {rule_predictions}/{len(test_statements)} ({rule_predictions/len(test_statements)*100:.1f}%)")
    
    if deberta_predictions > 0:
        avg_confidence = sum(r['deberta_confidence'] for r in results if r['deberta_label'] and r['deberta_label'] != 'ERROR') / deberta_predictions
        print(f"Average DeBERTa Confidence: {avg_confidence:.3f}")
    
    print(f"\n✅ Threshold test complete!")
    
    if deberta_predictions == len(test_statements):
        print(f"🎉 SUCCESS: All statements received DeBERTa predictions!")
        print(f"📈 Improvement: Previously 0% coverage, now 100% coverage")
    elif deberta_predictions > 0:
        print(f"⚠️ PARTIAL: {deberta_predictions}/{len(test_statements)} statements predicted")
    else:
        print(f"❌ FAILURE: No DeBERTa predictions made")
    
    return results

if __name__ == "__main__":
    results = test_improved_pipeline()
