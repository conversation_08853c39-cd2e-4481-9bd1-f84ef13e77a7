#!/usr/bin/env python3
"""
Simple test script to verify Ollama connection and truth-evaluator model.
"""
import requests
import json


def test_ollama_connection():
    """Test basic connection to Ollama API."""
    print("Testing Ollama connection...")

    # Test simple connection
    url = 'http://localhost:11434/api/generate'
    data = {
        'model': 'truth-evaluator',
        'prompt': 'Hello, are you working? Please respond briefly.',
        'stream': False,
        'options': {
            'temperature': 0.1,
            'num_predict': 50
        }
    }

    try:
        print("Sending request to Ollama...")
        # 2 minutes for slow computer
        response = requests.post(url, json=data, timeout=120)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', 'No response field')
            print(f"✅ SUCCESS: Model responded")
            print(f"Response: {response_text}")
            return True
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ ERROR: Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to Ollama")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False


def test_truth_evaluator_functionality():
    """Test truth-evaluator model with a sample outpoint detection."""
    print("\nTesting truth-evaluator functionality...")

    url = 'http://localhost:11434/api/generate'
    prompt = """
Analyze this statement for logical outpoints and pluspoints:
"Everyone knows the CEO is embezzling funds!!"

Please identify any outpoints (logical errors) or pluspoints (logical strengths) in this statement.
"""

    data = {
        'model': 'truth-evaluator',
        'prompt': prompt,
        'stream': False,
        'options': {
            'temperature': 0.1,
            'num_predict': 200
        }
    }

    try:
        print("Testing outpoint detection...")
        # 3 minutes for analysis task
        response = requests.post(url, json=data, timeout=180)

        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', 'No response field')
            print(f"✅ SUCCESS: Truth evaluator responded")
            print(f"Analysis: {response_text}")
            return True
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("OLLAMA CONNECTION TEST")
    print("=" * 60)

    # Test 1: Basic connection
    basic_test = test_ollama_connection()

    # Test 2: Truth evaluator functionality
    if basic_test:
        functionality_test = test_truth_evaluator_functionality()

        if functionality_test:
            print("\n🎉 ALL TESTS PASSED!")
            print("Ollama and truth-evaluator are working correctly.")
        else:
            print("\n⚠️ Basic connection works, but truth evaluation failed.")
    else:
        print("\n❌ Basic connection failed. Check Ollama setup.")

    print("=" * 60)
