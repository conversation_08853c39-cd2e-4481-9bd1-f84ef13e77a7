#!/usr/bin/env python3
"""
Test script to help diagnose and start Ollama server
"""
import requests
import subprocess
import time
import os
import sys

def test_ollama_connection():
    """Test if Ollama is accessible"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running and accessible!")
            models = response.json().get('models', [])
            print(f"📋 Available models: {len(models)}")
            for model in models:
                print(f"  - {model.get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ Ollama responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama (connection refused)")
        return False
    except requests.exceptions.Timeout:
        print("❌ Ollama connection timed out")
        return False
    except Exception as e:
        print(f"❌ Error connecting to Ollama: {e}")
        return False

def find_ollama_executable():
    """Try to find Ollama executable"""
    possible_paths = [
        r"C:\Program Files\Ollama\ollama.exe",
        r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe".format(os.environ.get('USERNAME', '')),
        r"C:\Users\<USER>\AppData\Local\Ollama\ollama.exe".format(os.environ.get('USERNAME', '')),
        "ollama.exe",
        "ollama"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ Found Ollama at: {path}")
            return path
        else:
            print(f"❌ Not found: {path}")
    
    return None

def start_ollama_server(executable_path):
    """Try to start Ollama server"""
    try:
        print(f"🚀 Starting Ollama server: {executable_path}")
        process = subprocess.Popen([executable_path, "serve"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # Wait a bit for server to start
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ Ollama server process started")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Ollama server failed to start")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting Ollama: {e}")
        return None

def test_truth_evaluator_model():
    """Test if truth-evaluator model is available"""
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "truth-evaluator",
                "prompt": "Test prompt",
                "stream": False
            },
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ truth-evaluator model is working!")
            return True
        else:
            print(f"❌ truth-evaluator model error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing truth-evaluator model: {e}")
        return False

def main():
    print("🔍 Ollama Startup Diagnostic Tool")
    print("=" * 40)
    
    # Step 1: Test if Ollama is already running
    print("\n1. Testing Ollama connection...")
    if test_ollama_connection():
        print("\n2. Testing truth-evaluator model...")
        if test_truth_evaluator_model():
            print("\n🎉 Everything is working! Ollama and truth-evaluator are ready.")
            return True
        else:
            print("\n⚠️ Ollama is running but truth-evaluator model needs to be loaded.")
            return False
    
    # Step 2: Try to find and start Ollama
    print("\n2. Searching for Ollama executable...")
    executable = find_ollama_executable()
    
    if not executable:
        print("\n❌ Could not find Ollama executable.")
        print("Please install Ollama from: https://ollama.ai/download")
        return False
    
    # Step 3: Try to start Ollama
    print("\n3. Starting Ollama server...")
    process = start_ollama_server(executable)
    
    if not process:
        return False
    
    # Step 4: Test connection again
    print("\n4. Testing connection after startup...")
    time.sleep(2)
    
    if test_ollama_connection():
        print("\n5. Testing truth-evaluator model...")
        if test_truth_evaluator_model():
            print("\n🎉 Success! Ollama and truth-evaluator are now running.")
            return True
        else:
            print("\n⚠️ Ollama started but truth-evaluator model needs to be created.")
            print("Run: ollama create truth-evaluator -f Modelfile")
            return False
    else:
        print("\n❌ Ollama server started but is not responding to API calls.")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Troubleshooting suggestions:")
        print("1. Install Ollama from https://ollama.ai/download")
        print("2. Run 'ollama serve' manually in a separate terminal")
        print("3. Create the truth-evaluator model: ollama create truth-evaluator -f Modelfile")
        print("4. Check firewall settings for port 11434")
    
    sys.exit(0 if success else 1)
