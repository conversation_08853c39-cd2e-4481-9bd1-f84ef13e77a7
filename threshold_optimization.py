#!/usr/bin/env python3
"""
Confidence Threshold Optimization for Round 2
Tests different thresholds to find optimal balance
"""
import pandas as pd
import numpy as np
import json
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from collections import defaultdict

def test_threshold_performance(model, tokenizer, df, label_map, threshold):
    """Test model performance at a specific confidence threshold"""
    id_to_label = {v: k for k, v in label_map.items()}
    
    results = {
        'threshold': threshold,
        'predictions_made': 0,
        'correct_predictions': 0,
        'total_statements': 0,
        'precision': 0.0,
        'recall': 0.0,
        'f1': 0.0,
        'coverage': 0.0
    }
    
    predictions_made = 0
    correct_predictions = 0
    total_statements = 0
    
    model.eval()
    with torch.no_grad():
        for _, row in df.iterrows():
            if pd.isna(row['label']) or row['label'] == '':
                continue
            
            total_statements += 1
            
            # Get prediction
            inputs = tokenizer(row['text'], return_tensors='pt', truncation=True, padding=True, max_length=512)
            outputs = model(**inputs)
            probs = torch.softmax(outputs.logits, dim=-1)
            confidence = torch.max(probs).item()
            
            # Only make prediction if confidence exceeds threshold
            if confidence >= threshold:
                predictions_made += 1
                predicted_class = torch.argmax(probs).item()
                predicted_label = id_to_label[predicted_class]
                
                # Check if correct
                true_labels = [label.strip() for label in row['label'].split(',')]
                if predicted_label in true_labels:
                    correct_predictions += 1
    
    # Calculate metrics
    if predictions_made > 0:
        precision = correct_predictions / predictions_made
    else:
        precision = 0.0
    
    if total_statements > 0:
        recall = correct_predictions / total_statements
        coverage = predictions_made / total_statements
    else:
        recall = 0.0
        coverage = 0.0
    
    if precision + recall > 0:
        f1 = 2 * (precision * recall) / (precision + recall)
    else:
        f1 = 0.0
    
    results.update({
        'predictions_made': predictions_made,
        'correct_predictions': correct_predictions,
        'total_statements': total_statements,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'coverage': coverage
    })
    
    return results

def optimize_threshold():
    """Test multiple thresholds and find optimal balance"""
    print("🎚️ CONFIDENCE THRESHOLD OPTIMIZATION")
    print("="*50)
    
    # Load model and data
    print("📂 Loading model and data...")
    model_dir = "models/bert-test"
    tokenizer = AutoTokenizer.from_pretrained(model_dir)
    model = AutoModelForSequenceClassification.from_pretrained(model_dir)
    
    with open(f"{model_dir}/label_map.json") as f:
        label_map = json.load(f)
    
    df = pd.read_csv("data/statements_to_label.csv")
    
    # Test different thresholds
    thresholds = [0.05, 0.10, 0.15, 0.20, 0.25, 0.30, 0.35, 0.40]
    results = []
    
    print("\n🧪 Testing thresholds...")
    for threshold in thresholds:
        print(f"  Testing threshold {threshold:.2f}...")
        result = test_threshold_performance(model, tokenizer, df, label_map, threshold)
        results.append(result)
        
        print(f"    Precision: {result['precision']:.3f}, Recall: {result['recall']:.3f}, F1: {result['f1']:.3f}, Coverage: {result['coverage']:.3f}")
    
    return results

def analyze_threshold_results(results):
    """Analyze threshold test results and recommend optimal threshold"""
    print(f"\n📊 THRESHOLD ANALYSIS RESULTS")
    print("="*50)
    
    # Display results table
    print(f"{'Threshold':<10} {'Precision':<10} {'Recall':<8} {'F1':<8} {'Coverage':<10} {'Predictions':<12}")
    print("-" * 70)
    
    for result in results:
        print(f"{result['threshold']:<10.2f} "
              f"{result['precision']:<10.3f} "
              f"{result['recall']:<8.3f} "
              f"{result['f1']:<8.3f} "
              f"{result['coverage']:<10.3f} "
              f"{result['predictions_made']:<12}")
    
    # Find optimal thresholds for different criteria
    best_f1 = max(results, key=lambda x: x['f1'])
    best_precision = max(results, key=lambda x: x['precision'])
    best_recall = max(results, key=lambda x: x['recall'])
    best_coverage = max(results, key=lambda x: x['coverage'])
    
    print(f"\n🏆 OPTIMAL THRESHOLDS:")
    print(f"  Best F1 Score: {best_f1['threshold']:.2f} (F1: {best_f1['f1']:.3f})")
    print(f"  Best Precision: {best_precision['threshold']:.2f} (Precision: {best_precision['precision']:.3f})")
    print(f"  Best Recall: {best_recall['threshold']:.2f} (Recall: {best_recall['recall']:.3f})")
    print(f"  Best Coverage: {best_coverage['threshold']:.2f} (Coverage: {best_coverage['coverage']:.3f})")
    
    # Recommend threshold based on balanced criteria
    # Look for threshold with good balance of precision, recall, and coverage
    balanced_scores = []
    for result in results:
        # Weighted score: F1 (40%) + Coverage (30%) + Precision (30%)
        balanced_score = (0.4 * result['f1'] + 
                         0.3 * result['coverage'] + 
                         0.3 * result['precision'])
        balanced_scores.append((result['threshold'], balanced_score, result))
    
    best_balanced = max(balanced_scores, key=lambda x: x[1])
    recommended_threshold = best_balanced[0]
    recommended_result = best_balanced[2]
    
    print(f"\n💡 RECOMMENDED THRESHOLD: {recommended_threshold:.2f}")
    print(f"  Precision: {recommended_result['precision']:.3f}")
    print(f"  Recall: {recommended_result['recall']:.3f}")
    print(f"  F1 Score: {recommended_result['f1']:.3f}")
    print(f"  Coverage: {recommended_result['coverage']:.3f}")
    print(f"  Predictions: {recommended_result['predictions_made']}/{recommended_result['total_statements']}")
    
    return recommended_threshold, results

def save_threshold_analysis(recommended_threshold, results):
    """Save threshold analysis results"""
    analysis = {
        'timestamp': pd.Timestamp.now().isoformat(),
        'recommended_threshold': recommended_threshold,
        'current_threshold': 0.3,
        'improvement': f"Change from 0.3 to {recommended_threshold:.2f}",
        'all_results': results,
        'summary': {
            'baseline_threshold': 0.3,
            'recommended_threshold': recommended_threshold,
            'expected_improvement': 'Increased coverage and recall with maintained precision'
        }
    }
    
    with open('threshold_optimization_results.json', 'w') as f:
        json.dump(analysis, f, indent=2, default=str)
    
    print(f"\n💾 Threshold analysis saved to: threshold_optimization_results.json")
    return analysis

def update_model_config(recommended_threshold):
    """Update model configuration with new threshold"""
    print(f"\n🔧 UPDATING MODEL CONFIGURATION")
    print("="*40)
    
    # Update DeBERTa classifier configuration
    config_updates = {
        'confidence_threshold': recommended_threshold,
        'previous_threshold': 0.3,
        'update_reason': 'Round 2 threshold optimization',
        'expected_improvement': 'Better balance of precision, recall, and coverage'
    }
    
    # Save updated config
    with open('models/bert-test/threshold_config.json', 'w') as f:
        json.dump(config_updates, f, indent=2)
    
    print(f"✅ Updated threshold from 0.3 to {recommended_threshold:.2f}")
    print(f"💾 Configuration saved to: models/bert-test/threshold_config.json")
    
    return config_updates

def main():
    try:
        # Optimize threshold
        results = optimize_threshold()
        
        # Analyze results
        recommended_threshold, all_results = analyze_threshold_results(results)
        
        # Save analysis
        analysis = save_threshold_analysis(recommended_threshold, all_results)
        
        # Update model config
        config = update_model_config(recommended_threshold)
        
        print(f"\n✅ Threshold optimization complete!")
        print(f"🎯 Recommended threshold: {recommended_threshold:.2f}")
        print(f"📈 Expected improvement in coverage and recall")
        print(f"🚀 Ready for next Round 2 improvement!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during threshold optimization: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
