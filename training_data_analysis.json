{"timestamp": "2025-08-03T12:50:34.604207", "label_distribution": {"OMITTED_DATA_OUT": 12, "ADEQUATE_DATA_PLUS": 35, "DATA_PROVEN_FACTUAL_PLUS": 6, "CORRECT_SOURCE_PLUS": 5, "NEUTRAL": 2, "ALTERED_IMPORTANCE_OUT": 3, "TIME_NOTED_PLUS": 12, "CORRECT_DATA_PLUS": 28, "CORRECT_SEQUENCE_PLUS": 3, "CORRECT_IMPORTANCE_PLUS": 2, "CONTRARY_FACTS_OUT": 5, "FALSE_DATA_OUT": 4, "ALTERED_SEQUENCE_OUT": 1, "WRONG_TARGET_OUT": 1, "ASSUMED_IDENTITIES_OUT": 2, "ADDED_DATA_OUT": 1, "ADDED_INAPPLICABLES_OUT": 1}, "total_labels": 123, "unique_labels": 17, "identified_issues": ["📉 RARE LABELS: 7 categories with <3 examples", "   • NEUTRAL: 2 examples", "   • CORRECT_IMPORTANCE_PLUS: 2 examples", "   • ALTERED_SEQUENCE_OUT: 1 examples", "   • WRONG_TARGET_OUT: 1 examples", "   • ASSUMED_IDENTITIES_OUT: 2 examples", "⚖️ IMBALANCED: Outpoints vs Pluspoints ratio 3.0:1"], "enhancement_plan": {"immediate_actions": ["🎯 Priority: Add examples for rare categories"], "data_collection_targets": [{"label": "NEUTRAL", "current": 2, "target": 5, "needed": 3}, {"label": "CORRECT_IMPORTANCE_PLUS", "current": 2, "target": 5, "needed": 3}, {"label": "ALTERED_SEQUENCE_OUT", "current": 1, "target": 5, "needed": 4}, {"label": "WRONG_TARGET_OUT", "current": 1, "target": 5, "needed": 4}, {"label": "ASSUMED_IDENTITIES_OUT", "current": 2, "target": 5, "needed": 3}, {"label": "ADDED_DATA_OUT", "current": 1, "target": 5, "needed": 4}, {"label": "ADDED_INAPPLICABLES_OUT", "current": 1, "target": 5, "needed": 4}], "augmentation_strategies": ["Use AI-assisted labeling for additional BBC statements", "Paraphrase existing examples to increase diversity", "Focus on underrepresented outpoint categories", "Create synthetic examples for rare categories"], "balancing_actions": []}, "recommendations": {"priority_1": "Add examples for rare categories (<3 instances)", "priority_2": "Balance outpoint vs pluspoint representation", "priority_3": "Reduce model bias toward dominant categories", "priority_4": "Implement data augmentation strategies"}}