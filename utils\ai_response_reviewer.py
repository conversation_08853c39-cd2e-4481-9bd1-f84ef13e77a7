#!/usr/bin/env python3
"""
AI Response Reviewer - Clean up and apply batch AI labeling results
Reviews AI responses from batch processing and applies cleaned labels to the dataset.
"""

import pandas as pd
import json
import sys
import re
from datetime import datetime

# Valid categories for validation
VALID_CATEGORIES = {
    'OMITTED_DATA_OUT', 'ALTERED_SEQUENCE_OUT', 'DROPPED_TIME_OUT', 'FALSE_DATA_OUT',
    'ALTERED_IMPORTANCE_OUT', 'CONTRARY_FACTS_OUT', 'OMITTED_TIME_OUT', 'ADDED_TIME_OUT',
    'ADDED_DATA_OUT', 'ADDED_INAPPLICABLES_OUT', 'ASSUMED_IDENTITIES_OUT', 'ASSUMED_SIMILARITIES_OUT',
    'WRONG_TARGET_OUT', 'WRONG_SOURCE_OUT', 'ADEQUATE_DATA_PLUS', 'CORRECT_SEQUENCE_PLUS',
    'TIME_NOTED_PLUS', 'DATA_PROVEN_FACTUAL_PLUS', 'CORRECT_IMPORTANCE_PLUS', 'CONSISTENT_FACTS_PLUS',
    'TIME_PROPERLY_NOTED_PLUS', 'CORRECT_TIME_PLUS', 'CORRECT_DATA_PLUS', 'APPLICABLE_DATA_PLUS',
    'CORRECT_IDENTITY_PLUS', 'CORRECT_SIMILARITY_PLUS', 'CORRECT_TARGET_PLUS', 'CORRECT_SOURCE_PLUS',
    'NEUTRAL'
}


def clean_ai_response(raw_response):
    """Clean and parse AI response to extract valid categories"""
    if not raw_response:
        return []

    # Remove common prefixes and clean up
    response = raw_response.upper().strip()
    response = re.sub(
        r'^(THE STATEMENT.*?WOULD BE|CATEGORY|CATEGORIES?)[:.]?\s*', '', response)
    response = re.sub(r'^["\']*', '', response)
    response = re.sub(r'["\']*$', '', response)

    # Handle common formatting issues
    response = response.replace('_', '_')  # Fix spacing issues
    response = response.replace(' _', '_')
    response = response.replace('_ ', '_')

    # Split by common separators
    categories = []
    for separator in [',', ';', ' AND ', ' & ', '\n']:
        if separator in response:
            parts = response.split(separator)
            for part in parts:
                part = part.strip()
                if part and part in VALID_CATEGORIES:
                    categories.append(part)
            if categories:
                break

    # If no separators found, check if it's a single valid category
    if not categories and response in VALID_CATEGORIES:
        categories = [response]

    # Remove duplicates while preserving order
    seen = set()
    unique_categories = []
    for cat in categories:
        if cat not in seen:
            seen.add(cat)
            unique_categories.append(cat)

    return unique_categories


def process_single_result(result):
    """Process a single AI result"""
    statement_id = result['statement_id']
    statement = result['statement']
    ai_result = result['ai_result']

    cleaned_result = {
        'statement_id': statement_id,
        'statement': statement,
        'original_ai_response': ai_result.get('response', '') if ai_result['success'] else None,
        'ai_success': ai_result['success'],
        'response_time': ai_result.get('response_time', 0),
        'suggested_labels': [],
        'needs_manual_review': False,
        'review_reason': ''
    }

    if ai_result['success']:
        # Clean the AI response
        suggested_labels = clean_ai_response(ai_result['response'])

        if suggested_labels:
            cleaned_result['suggested_labels'] = suggested_labels
        else:
            cleaned_result['needs_manual_review'] = True
            cleaned_result['review_reason'] = 'Could not parse AI response'
    else:
        cleaned_result['needs_manual_review'] = True
        cleaned_result['review_reason'] = f"AI failed: {ai_result.get('error', 'Unknown error')}"

    return cleaned_result


def review_and_suggest_labels(results_file):
    """Review AI results and suggest cleaned labels"""

    print(f"📁 Loading results file: {results_file}")
    print(f"⏳ This may take a moment for large files...")

    try:
        with open(results_file, 'r') as f:
            results = json.load(f)
    except FileNotFoundError:
        print(f"❌ Could not find results file: {results_file}")
        return None
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON in results file: {results_file}")
        return None
    except MemoryError:
        print(f"❌ File too large for memory. Processing in chunks...")
        return process_large_file_in_chunks(results_file)

    print(f"📊 Reviewing {len(results)} AI responses...")

    cleaned_results = []
    successful_labels = 0
    failed_labels = 0

    # Process in batches to avoid memory issues
    batch_size = 10
    for i in range(0, len(results), batch_size):
        batch = results[i:i+batch_size]
        if len(results) > 50:  # Only show progress for large files
            print(
                f"📊 Processing batch {i//batch_size + 1}/{(len(results)-1)//batch_size + 1}")

        for result in batch:
            cleaned_result = process_single_result(result)
            cleaned_results.append(cleaned_result)

            if cleaned_result['suggested_labels']:
                successful_labels += 1
            else:
                failed_labels += 1

    print(f"✅ Successfully parsed: {successful_labels}")
    print(f"⚠️ Need manual review: {failed_labels}")

    return cleaned_results


def apply_labels_to_dataset(cleaned_results, dry_run=True):
    """Apply the cleaned labels to the main dataset"""

    try:
        df = pd.read_csv('data/statements_to_label.csv')
    except FileNotFoundError:
        print("❌ Could not find data/statements_to_label.csv")
        return False

    labels_applied = 0

    for result in cleaned_results:
        if result['suggested_labels'] and not result['needs_manual_review']:
            statement_id = result['statement_id']
            labels = result['suggested_labels']

            # Find the row in the dataframe (using index since CSV doesn't have 'id' column)
            if statement_id in df.index:
                # Join multiple labels with commas
                label_string = ', '.join(labels)

                if not dry_run:
                    df.loc[statement_id, 'label'] = label_string

                labels_applied += 1
                print(f"Statement {statement_id}: {label_string}")

    if not dry_run:
        # Save the updated dataset
        backup_file = f"data/statements_to_label_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(backup_file, index=False)
        print(f"💾 Backup saved to: {backup_file}")

        df.to_csv('data/statements_to_label.csv', index=False)
        print(f"💾 Updated dataset saved")

    print(f"📊 Labels applied: {labels_applied}")
    return True


def main():
    if len(sys.argv) < 2:
        print(
            "Usage: python utils/ai_response_reviewer.py <results_file.json> [--apply]")
        print("\nExample:")
        print("  python utils/ai_response_reviewer.py data/ai_batch_results_20250709_143022.json")
        print("  python utils/ai_response_reviewer.py data/ai_batch_results_20250709_143022.json --apply")
        sys.exit(1)

    results_file = sys.argv[1]
    apply_labels = '--apply' in sys.argv

    print("🔍 AI Response Reviewer")
    print("=" * 50)

    # Review and clean the AI responses
    cleaned_results = review_and_suggest_labels(results_file)
    if not cleaned_results:
        sys.exit(1)

    print(f"\n📋 Review Summary:")

    # Show statements that need manual review
    manual_review_needed = [
        r for r in cleaned_results if r['needs_manual_review']]
    if manual_review_needed:
        print(
            f"\n⚠️ Statements needing manual review ({len(manual_review_needed)}):")
        for result in manual_review_needed:
            print(
                f"  Statement {result['statement_id']}: {result['review_reason']}")
            print(
                f"    \"{result['statement'][:60]}{'...' if len(result['statement']) > 60 else ''}\"")

    # Show successfully parsed labels
    successful_results = [
        r for r in cleaned_results if not r['needs_manual_review']]
    if successful_results:
        print(f"\n✅ Successfully parsed labels ({len(successful_results)}):")
        for result in successful_results[:5]:  # Show first 5 as examples
            labels = ', '.join(result['suggested_labels'])
            print(f"  Statement {result['statement_id']}: {labels}")
        if len(successful_results) > 5:
            print(f"  ... and {len(successful_results) - 5} more")

    # Apply labels if requested
    if apply_labels:
        print(f"\n🚀 Applying labels to dataset...")
        apply_labels_to_dataset(cleaned_results, dry_run=False)
    else:
        print(f"\n🔍 Dry run - showing what would be applied:")
        apply_labels_to_dataset(cleaned_results, dry_run=True)
        print(f"\n💡 To actually apply these labels, run:")
        print(
            f"   python utils/ai_response_reviewer.py {results_file} --apply")


if __name__ == "__main__":
    main()
