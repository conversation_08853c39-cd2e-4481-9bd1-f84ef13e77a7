#!/usr/bin/env python3
"""
Batch AI Labeling Tool - No User Interaction Required
Processes all unlabeled statements and saves AI responses to file for later review.
Designed for slow computers with proper timeouts.
"""

import pandas as pd
import requests
import json
import time
import sys
import os
from datetime import datetime

# Ollama configuration
OLLAMA_URL = "http://localhost:11434/api/generate"
MODEL_NAME = "truth-evaluator"  # Use the same model as the working tools
TIMEOUT_SECONDS = 900  # 15 minutes based on timing test results


def test_ollama_connection():
    """Test if <PERSON>lla<PERSON> is responding"""
    print("Testing Ollama connection...")
    print("⏳ This may take several minutes on slow computers...")

    try:
        response = requests.post(
            OLLAMA_URL,
            json={
                "model": MODEL_NAME,
                "prompt": "Hi",
                "stream": False,
                "options": {"num_predict": 5}
            },
            timeout=TIMEOUT_SECONDS
        )
        if response.status_code == 200:
            print("✅ Ollama connection OK")
            return True
        else:
            print(f"❌ Ollama error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False


def get_ai_label(statement, statement_id):
    """Get AI label suggestion for a statement"""

    prompt = f"""Analyze this statement using L. Ron Hubbard's Investigations methodology. Apply appropriate outpoints (logical errors) and/or pluspoints (logical strengths).

STATEMENT: "{statement}"

OUTPOINTS (logical errors):
1. OMITTED_DATA_OUT - Missing important information
2. ALTERED_SEQUENCE_OUT - Events out of chronological order
3. DROPPED_TIME_OUT - Missing time information
4. FALSE_DATA_OUT - Incorrect/inaccurate information
5. ALTERED_IMPORTANCE_OUT - Wrong emphasis given
6. CONTRARY_FACTS_OUT - Contradictory information
7. OMITTED_TIME_OUT - Missing time context
8. ADDED_TIME_OUT - Incorrect time added
9. ADDED_DATA_OUT - Unnecessary information added
10. ADDED_INAPPLICABLES_OUT - Irrelevant information included
11. ASSUMED_IDENTITIES_OUT - False assumptions about identity
12. ASSUMED_SIMILARITIES_OUT - False assumptions about similarity
13. WRONG_TARGET_OUT - Addressing wrong subject
14. WRONG_SOURCE_OUT - Information from unreliable source

PLUSPOINTS (logical strengths):
15. ADEQUATE_DATA_PLUS - Sufficient information provided
16. CORRECT_SEQUENCE_PLUS - Events in proper chronological order
17. TIME_NOTED_PLUS - Time properly specified
18. DATA_PROVEN_FACTUAL_PLUS - Information verified as accurate
19. CORRECT_IMPORTANCE_PLUS - Proper emphasis given
20. CONSISTENT_FACTS_PLUS - Information is internally consistent
21. TIME_PROPERLY_NOTED_PLUS - Time context appropriate
22. CORRECT_TIME_PLUS - Accurate timing provided
23. CORRECT_DATA_PLUS - Accurate information
24. APPLICABLE_DATA_PLUS - Relevant information included
25. CORRECT_IDENTITY_PLUS - Proper identification made
26. CORRECT_SIMILARITY_PLUS - Valid comparisons made
27. CORRECT_TARGET_PLUS - Addressing correct subject
28. CORRECT_SOURCE_PLUS - Information from reliable source

CRITICAL RULES:
- Do NOT apply contradictory labels (e.g., both ALTERED_SEQUENCE_OUT and CORRECT_SEQUENCE_PLUS)
- Apply only labels that actually apply to this specific statement
- If no significant outpoints or pluspoints, use "NEUTRAL"
- Multiple labels are allowed if they address different aspects

Respond with ONLY the applicable category names, separated by commas.

Examples:
- "OMITTED_DATA_OUT"
- "TIME_NOTED_PLUS, ADEQUATE_DATA_PLUS"
- "CONTRARY_FACTS_OUT, WRONG_SOURCE_OUT"
- "NEUTRAL"
"""

    try:
        start_time = time.time()
        response = requests.post(
            OLLAMA_URL,
            json={
                "model": MODEL_NAME,
                "prompt": prompt,
                "stream": False
            },
            timeout=TIMEOUT_SECONDS
        )

        end_time = time.time()
        response_time = end_time - start_time

        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '').strip()

            return {
                'success': True,
                'response': ai_response,
                'response_time': response_time,
                'raw_response': result
            }
        else:
            return {
                'success': False,
                'error': f"HTTP {response.status_code}",
                'response_time': response_time
            }

    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'response_time': time.time() - start_time if 'start_time' in locals() else 0
        }


def main():
    print("🤖 Batch AI Labeling Tool")
    print("=" * 50)

    # Skip connection test - go straight to processing
    # (Connection test takes 5+ minutes on slow computers)
    print("⚡ Skipping connection test - starting processing directly")
    print("💡 Based on session status, Ollama is confirmed working")

    # Load data
    try:
        df = pd.read_csv('data/statements_to_label.csv')
        print(f"📊 Loaded {len(df)} statements")
    except FileNotFoundError:
        print("❌ Could not find data/statements_to_label.csv")
        sys.exit(1)

    # Check for existing batch results to resume from
    import glob
    existing_files = glob.glob("data/ai_batch_results_*.json")
    processed_ids = set()

    if existing_files:
        latest_file = max(existing_files, key=os.path.getctime)
        try:
            with open(latest_file, 'r') as f:
                existing_results = json.load(f)
            processed_ids = {r['statement_id'] for r in existing_results}
            print(f"📁 Found existing results: {latest_file}")
            print(f"📊 Already processed: {len(processed_ids)} statements")
        except:
            print("⚠️ Could not read existing results, starting fresh")

    # Find unlabeled statements that haven't been processed yet
    unlabeled = df[df['label'].isna() | (df['label'] == '')]
    remaining = unlabeled[~unlabeled.index.isin(processed_ids)]

    print(f"🎯 Total unlabeled: {len(unlabeled)} statements")
    print(f"🎯 Remaining to process: {len(remaining)} statements")

    if len(remaining) == 0:
        print("✅ All statements have been processed!")
        print("💡 Use the AI response reviewer to apply results to dataset")
        return

    # Prepare output file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"data/ai_batch_results_{timestamp}.json"

    print(f"\n🚀 Starting batch processing...")
    print(f"📁 Results will be saved to: {output_file}")
    print(
        f"⏱️ Estimated time: {len(remaining) * 5} minutes ({len(remaining)} statements × 5 min each)")
    print(f"💡 This will run automatically - no user input needed!")
    print(f"🔄 You can check progress by looking at the output file")

    results = []

    for idx, (row_idx, row) in enumerate(remaining.iterrows(), 1):
        statement_id = row_idx  # Use pandas index as ID
        statement = row['text']  # Column is named 'text', not 'statement'

        print(f"\n--- Statement {statement_id} ({idx}/{len(unlabeled)}) ---")
        print(
            f"📝 Analyzing: {statement[:80]}{'...' if len(statement) > 80 else ''}")
        print(f"⏳ Sending to LLM (this will take about 5 minutes)...")

        # Get AI response
        ai_result = get_ai_label(statement, statement_id)

        # Prepare result record
        result = {
            'statement_id': statement_id,
            'statement': statement,
            'timestamp': datetime.now().isoformat(),
            'ai_result': ai_result,
            'processed_order': idx
        }

        results.append(result)

        # Show result
        if ai_result['success']:
            print(
                f"⏱️ Response received in {ai_result['response_time']:.1f} seconds")
            print(f"✅ AI suggests: {ai_result['response']}")
        else:
            print(f"❌ AI failed: {ai_result['error']}")

        # Save results after each statement (in case of interruption)
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)

        print(f"💾 Progress saved ({idx}/{len(remaining)} complete)")

    print(f"\n🎉 Batch processing complete!")
    print(f"📁 Results saved to: {output_file}")
    print(f"📊 Processed {len(results)} statements")
    print(f"\n🔍 Next steps:")
    print(f"1. Review the results file: {output_file}")
    print(f"2. Use the AI review tool to clean up and apply labels")
    print(f"3. Check progress: python utils/manual_labeling_helper.py --progress_only")


if __name__ == "__main__":
    main()
