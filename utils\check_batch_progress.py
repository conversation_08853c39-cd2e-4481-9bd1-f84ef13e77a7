#!/usr/bin/env python3
"""
Quick Batch Progress Checker
Shows current status of AI batch labeling process
"""

import json
import os
import glob
from datetime import datetime

def check_batch_progress():
    """Check progress of the most recent batch labeling run"""
    
    # Find the most recent batch results file
    batch_files = glob.glob("data/ai_batch_results_*.json")
    if not batch_files:
        print("❌ No batch results files found")
        return
    
    latest_file = max(batch_files, key=os.path.getctime)
    
    try:
        with open(latest_file, 'r') as f:
            results = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"❌ Error reading {latest_file}: {e}")
        return
    
    if not results:
        print("📊 Batch file exists but no results yet")
        return
    
    # Calculate statistics
    total_processed = len(results)
    successful = sum(1 for r in results if r['ai_result']['success'])
    failed = total_processed - successful
    
    # Get timing info
    response_times = [r['ai_result']['response_time'] for r in results if r['ai_result']['success']]
    avg_time = sum(response_times) / len(response_times) if response_times else 0
    
    # Get latest timestamp
    latest_timestamp = max(r['timestamp'] for r in results)
    latest_time = datetime.fromisoformat(latest_timestamp)
    
    print("🤖 AI Batch Labeling Progress")
    print("=" * 40)
    print(f"📁 File: {os.path.basename(latest_file)}")
    print(f"📊 Progress: {total_processed}/91 statements ({total_processed/91*100:.1f}%)")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success rate: {successful/total_processed*100:.1f}%")
    print(f"⏱️ Avg response time: {avg_time:.1f} seconds ({avg_time/60:.1f} minutes)")
    print(f"🕐 Last update: {latest_time.strftime('%H:%M:%S')}")
    
    # Estimate completion
    remaining = 91 - total_processed
    if avg_time > 0:
        est_remaining_minutes = (remaining * avg_time) / 60
        est_hours = int(est_remaining_minutes // 60)
        est_mins = int(est_remaining_minutes % 60)
        print(f"⏳ Estimated remaining: {est_hours}h {est_mins}m ({remaining} statements)")
    
    # Show recent results
    print(f"\n📋 Last 3 results:")
    for result in results[-3:]:
        stmt_id = result['statement_id']
        success = "✅" if result['ai_result']['success'] else "❌"
        stmt_preview = result['statement'][:50] + "..." if len(result['statement']) > 50 else result['statement']
        print(f"  {success} Statement {stmt_id}: {stmt_preview}")
        
        if result['ai_result']['success']:
            response = result['ai_result']['response'][:60] + "..." if len(result['ai_result']['response']) > 60 else result['ai_result']['response']
            print(f"      → {response}")
        else:
            error = result['ai_result']['error'][:60] + "..." if len(result['ai_result']['error']) > 60 else result['ai_result']['error']
            print(f"      → Error: {error}")

if __name__ == "__main__":
    check_batch_progress()
