#!/usr/bin/env python3
"""
Label Corrector - Fix AI-generated labels using proper L<PERSON> Ron <PERSON> methodology
Analyzes statements and applies correct outpoints/pluspoints based on actual definitions.
"""

import pandas as pd
import sys

def analyze_statement(statement):
    """
    Analyze a statement using proper L. Ron Hubbard Investigations methodology.
    Returns appropriate outpoints and pluspoints.
    """
    statement = statement.strip()
    labels = []
    
    # Check for common outpoints
    
    # OMITTED_DATA_OUT - Missing important information
    if any(word in statement.lower() for word in ['he said', 'she said', 'they said']) and not any(name in statement for name in ['trump', 'biden', 'putin']):
        if '"' in statement and statement.count('"') >= 2:  # Quote without clear attribution
            labels.append('OMITTED_DATA_OUT')
    
    # Check for vague references
    if any(phrase in statement.lower() for phrase in ['he ', 'she ', 'they ', 'it ', 'this ', 'that ']):
        # Only if the reference is unclear from context
        if statement.startswith('"') or 'he said' in statement.lower():
            labels.append('OMITTED_DATA_OUT')
    
    # DROPPED_TIME_OUT - Missing time information
    if any(word in statement.lower() for word in ['recently', 'soon', 'later', 'earlier']) and not any(time in statement.lower() for time in ['2024', '2025', 'january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']):
        labels.append('DROPPED_TIME_OUT')
    
    # FALSE_DATA_OUT - Check for obviously false information (requires domain knowledge)
    # This is hard to automate, will handle manually for specific cases
    
    # ALTERED_IMPORTANCE_OUT - Wrong emphasis (hard to detect automatically)
    
    # CONTRARY_FACTS_OUT - Contradictory information within the statement
    if ' but ' in statement.lower() or ' however ' in statement.lower():
        # Check if it's actually contradictory vs just contrasting
        pass  # Will handle manually
    
    # Check for pluspoints
    
    # TIME_NOTED_PLUS - Time properly specified
    time_indicators = ['2024', '2025', 'january', 'february', 'march', 'april', 'may', 'june', 
                      'july', 'august', 'september', 'october', 'november', 'december',
                      'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday',
                      'anniversary', 'year', 'month', 'week', 'day']
    
    if any(time in statement.lower() for time in time_indicators):
        labels.append('TIME_NOTED_PLUS')
    
    # ADEQUATE_DATA_PLUS - Sufficient information provided
    if len(statement.split()) >= 10 and not any(out in labels for out in ['OMITTED_DATA_OUT']):
        # Statement has reasonable length and doesn't omit key data
        labels.append('ADEQUATE_DATA_PLUS')
    
    # DATA_PROVEN_FACTUAL_PLUS - Information that appears factual
    factual_indicators = ['government', 'official', 'report', 'study', 'data', 'statistics', 
                         'according to', 'spokesperson', 'minister', 'president']
    if any(indicator in statement.lower() for indicator in factual_indicators):
        labels.append('DATA_PROVEN_FACTUAL_PLUS')
    
    # CORRECT_SOURCE_PLUS - Information from reliable source
    source_indicators = ['spokesperson', 'minister', 'president', 'government', 'official', 
                        'according to', 'reported by']
    if any(indicator in statement.lower() for indicator in source_indicators):
        labels.append('CORRECT_SOURCE_PLUS')
    
    # If no specific issues found and statement seems complete
    if not labels or all('PLUS' in label for label in labels):
        if len(statement.split()) >= 5 and not statement.startswith('"'):
            labels.append('CORRECT_DATA_PLUS')
    
    # Remove duplicates and return
    return list(set(labels)) if labels else ['NEUTRAL']

def correct_labels():
    """Correct the AI-generated labels in the dataset"""
    
    try:
        df = pd.read_csv('data/statements_to_label.csv')
    except FileNotFoundError:
        print("❌ Could not find data/statements_to_label.csv")
        return
    
    print("🔍 Analyzing AI-labeled statements for corrections...")
    
    corrections_made = 0
    
    for idx, row in df.iterrows():
        if pd.notna(row['label']) and row['label'].strip():
            statement = row['text']
            current_labels = row['label']
            
            # Check for obvious over-labeling (more than 5 labels is suspicious)
            label_count = len(current_labels.split(','))
            
            if label_count > 5:
                print(f"\n📝 Statement {idx}: {statement[:60]}...")
                print(f"❌ Current labels ({label_count}): {current_labels[:100]}...")
                
                # Get corrected labels
                corrected_labels = analyze_statement(statement)
                corrected_string = ', '.join(corrected_labels)
                
                print(f"✅ Corrected labels ({len(corrected_labels)}): {corrected_string}")
                
                # Apply correction
                df.loc[idx, 'label'] = corrected_string
                corrections_made += 1
    
    if corrections_made > 0:
        # Save backup
        backup_file = f"data/statements_to_label_backup_corrected.csv"
        df.to_csv(backup_file, index=False)
        print(f"💾 Backup saved to: {backup_file}")
        
        # Save corrected version
        df.to_csv('data/statements_to_label.csv', index=False)
        print(f"💾 Corrected dataset saved")
        print(f"📊 Made corrections to {corrections_made} statements")
    else:
        print("✅ No corrections needed")

if __name__ == "__main__":
    correct_labels()
