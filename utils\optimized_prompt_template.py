#!/usr/bin/env python3
"""
Optimized Prompt Template for AI Labeling
Much shorter than 1.1M tokens while keeping all definitions and preventing contradictions.
"""

def create_optimized_prompt(statement):
    """Create an optimized prompt that's concise but complete"""
    
    prompt = f"""Analyze this statement using <PERSON><PERSON>'s Investigations methodology. Apply appropriate outpoints (logical errors) and/or pluspoints (logical strengths).

STATEMENT: "{statement}"

OUTPOINTS (logical errors):
1. OMITTED_DATA_OUT - Missing important information
2. ALTERED_SEQUENCE_OUT - Events out of chronological order
3. DROPPED_TIME_OUT - Missing time information
4. FALSE_DATA_OUT - Incorrect/inaccurate information
5. ALTERED_IMPORTANCE_OUT - Wrong emphasis given
6. CONTRARY_FACTS_OUT - Contradictory information
7. OMITTED_TIME_OUT - Missing time context
8. ADDED_TIME_OUT - Incorrect time added
9. ADDED_DATA_OUT - Unnecessary information added
10. ADDED_INAPPLICABLES_OUT - Irrelevant information included
11. ASSUMED_IDENTITIES_OUT - False assumptions about identity
12. ASSUMED_SIMILARITIES_OUT - False assumptions about similarity
13. WRONG_TARGET_OUT - Addressing wrong subject
14. WRONG_SOURCE_OUT - Information from unreliable source

PLUSPOINTS (logical strengths):
15. ADEQUATE_DATA_PLUS - Sufficient information provided
16. CORRECT_SEQUENCE_PLUS - Events in proper chronological order
17. TIME_NOTED_PLUS - Time properly specified
18. DATA_PROVEN_FACTUAL_PLUS - Information verified as accurate
19. CORRECT_IMPORTANCE_PLUS - Proper emphasis given
20. CONSISTENT_FACTS_PLUS - Information is internally consistent
21. TIME_PROPERLY_NOTED_PLUS - Time context appropriate
22. CORRECT_TIME_PLUS - Accurate timing provided
23. CORRECT_DATA_PLUS - Accurate information
24. APPLICABLE_DATA_PLUS - Relevant information included
25. CORRECT_IDENTITY_PLUS - Proper identification made
26. CORRECT_SIMILARITY_PLUS - Valid comparisons made
27. CORRECT_TARGET_PLUS - Addressing correct subject
28. CORRECT_SOURCE_PLUS - Information from reliable source

CRITICAL RULES:
- Do NOT apply contradictory labels (e.g., both ALTERED_SEQUENCE_OUT and CORRECT_SEQUENCE_PLUS)
- Apply only labels that actually apply to this specific statement
- If no significant outpoints or pluspoints, use "NEUTRAL"
- Multiple labels are allowed if they address different aspects

Respond with ONLY the applicable category names, separated by commas.

Examples:
- "OMITTED_DATA_OUT"
- "TIME_NOTED_PLUS, ADEQUATE_DATA_PLUS"
- "CONTRARY_FACTS_OUT, WRONG_SOURCE_OUT"
- "NEUTRAL"
"""
    
    return prompt

def test_prompt_length():
    """Test the prompt length to ensure it's under token limits"""
    test_statement = "This is a test statement to check prompt length."
    prompt = create_optimized_prompt(test_statement)
    
    # Rough token estimation (1 token ≈ 4 characters)
    char_count = len(prompt)
    estimated_tokens = char_count // 4
    
    print(f"📊 Prompt Analysis:")
    print(f"Characters: {char_count:,}")
    print(f"Estimated tokens: {estimated_tokens:,}")
    print(f"Ollama limit: 2,048 tokens")
    print(f"Status: {'✅ FITS' if estimated_tokens < 2048 else '❌ TOO LONG'}")
    
    return estimated_tokens < 2048

if __name__ == "__main__":
    test_prompt_length()
    
    # Show example prompt
    print("\n" + "="*50)
    print("EXAMPLE PROMPT:")
    print("="*50)
    example_prompt = create_optimized_prompt("Donald Trump says joining the US would be a massive tax cut for Canadians.")
    print(example_prompt)
