#!/usr/bin/env python3
"""
Patient AI labeler - designed for very slow computers.
Shows progress while waiting and never gives up.
"""
import pandas as pd
import requests
import json
import time
import sys
import os
import threading

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# All 28 categories
ALL_CATEGORIES = [
    # Outpoints (14)
    "OMITTED_DATA_OUT", "ALTERED_SEQUENCE_OUT", "DROPPED_TIME_OUT", 
    "WRONG_SOURCE_OUT", "CONTRARY_FACTS_OUT", "ADDED_INAPPLICABLES_OUT",
    "ASSUMED_SIMILARITIES_OUT", "ALTERED_IMPORTANCE_OUT", "FALSEDATA_OUT",
    "INCOMPLETE_DATA_OUT", "ASSUMED_IDENTITIES_OUT", "EVALUATION_OUT",
    "OUTPOINT_OUT", "PRIOR_CONFUSION_OUT",
    # Pluspoints (14)
    "DATA_PROVEN_FACTUAL_PLUS", "CORRECT_SEQUENCE_PLUS", "TIME_NOTED_PLUS",
    "CORRECT_SOURCE_PLUS", "ADEQUATE_DATA_PLUS", "APPLICABLE_DATA_PLUS",
    "CORRECT_RELATIVE_IMPORTANCE_PLUS", "COMPLETE_DATA_PLUS", "LOGICAL_PLUS",
    "CORRECT_ESTIMATION_PLUS", "CORRECT_IDENTITY_PLUS", "KNOWN_DATUM_PLUS",
    "PLUSPOINT_PLUS", "CORRECT_WHY_PLUS"
]

class ProgressIndicator:
    """Shows progress while waiting for slow AI responses."""
    
    def __init__(self):
        self.running = False
        self.thread = None
        
    def start(self, message="Waiting for AI"):
        self.running = True
        self.thread = threading.Thread(target=self._show_progress, args=(message,))
        self.thread.daemon = True
        self.thread.start()
        
    def stop(self):
        self.running = False
        if self.thread:
            self.thread.join()
        print()  # New line after progress
        
    def _show_progress(self, message):
        chars = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
        i = 0
        start_time = time.time()
        
        while self.running:
            elapsed = time.time() - start_time
            minutes = int(elapsed // 60)
            seconds = int(elapsed % 60)
            
            print(f"\r{chars[i % len(chars)]} {message}... {minutes:02d}:{seconds:02d}", end="", flush=True)
            time.sleep(0.5)
            i += 1

def patient_ai_request(prompt, max_wait_minutes=15):
    """Make an AI request with patience and progress indication."""
    
    progress = ProgressIndicator()
    progress.start(f"Asking AI (max {max_wait_minutes} min)")
    
    try:
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': 'truth-evaluator',
                'prompt': prompt,
                'stream': False,
                'options': {
                    'temperature': 0.1,
                    'num_predict': 50
                }
            },
            timeout=max_wait_minutes * 60  # Convert to seconds
        )
        
        progress.stop()
        
        if response.status_code == 200:
            result = response.json()
            return result.get('response', '').strip()
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return None
            
    except requests.exceptions.Timeout:
        progress.stop()
        print(f"⏰ AI didn't respond within {max_wait_minutes} minutes")
        return None
    except Exception as e:
        progress.stop()
        print(f"❌ Error: {e}")
        return None

def test_ai_connection():
    """Test AI connection with patience."""
    print("🧪 Testing AI connection...")
    
    response = patient_ai_request("Say 'READY' if you can respond.", max_wait_minutes=10)
    
    if response:
        print(f"✅ AI responded: {response}")
        return True
    else:
        print("❌ AI connection failed")
        return False

def label_statement_patiently(statement):
    """Label a statement with maximum patience."""
    
    print(f"\n📝 Statement: {statement[:100]}...")
    
    prompt = f"""
Analyze this statement for logical outpoints and pluspoints:

Statement: "{statement}"

Choose the SINGLE most appropriate category:

OUTPOINTS: OMITTED_DATA_OUT, WRONG_SOURCE_OUT, CONTRARY_FACTS_OUT, ALTERED_IMPORTANCE_OUT, FALSEDATA_OUT, INCOMPLETE_DATA_OUT
PLUSPOINTS: DATA_PROVEN_FACTUAL_PLUS, CORRECT_SOURCE_PLUS, ADEQUATE_DATA_PLUS, TIME_NOTED_PLUS, COMPLETE_DATA_PLUS, LOGICAL_PLUS
NEUTRAL: Neither clearly positive nor negative

Respond with ONLY the category name.
"""

    response = patient_ai_request(prompt, max_wait_minutes=15)
    
    if response:
        # Extract category from response
        response_upper = response.upper()
        for category in ALL_CATEGORIES + ['NEUTRAL']:
            if category in response_upper:
                print(f"🤖 AI suggests: {category}")
                return category
        
        print(f"🤔 Unclear AI response: {response}")
        return "UNCLEAR"
    else:
        return "FAILED"

def main():
    """Main patient AI labeling function."""
    print("🐌 Patient AI Labeler for Very Slow Computers")
    print("=" * 60)
    print("💡 This tool will wait as long as needed for AI responses")
    print("💡 You can press Ctrl+C to cancel at any time")
    print()
    
    # Test connection first
    if not test_ai_connection():
        print("❌ Cannot connect to AI. Make sure Ollama is running.")
        return
    
    # Load data
    data_file = "data/statements_to_label.csv"
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return
    
    df = pd.read_csv(data_file)
    unlabeled = df[df['label'].isna() | (df['label'] == '')]
    
    print(f"📊 Found {len(unlabeled)} unlabeled statements")
    
    if len(unlabeled) == 0:
        print("🎉 All statements already labeled!")
        return
    
    print(f"\n🚀 Starting patient AI labeling...")
    print("⚠️  Each statement may take 5-15 minutes")
    
    labeled_count = 0
    
    try:
        for idx, row in unlabeled.head(3).iterrows():  # Start with just 3
            statement = row['text']
            
            print(f"\n--- Statement {idx + 1} ---")
            ai_label = label_statement_patiently(statement)
            
            if ai_label in ALL_CATEGORIES + ['NEUTRAL']:
                # Ask user to confirm
                print(f"\nStatement: {statement}")
                print(f"AI suggests: {ai_label}")
                
                confirm = input("\nAccept this label? (y/n/s to skip): ").strip().lower()
                
                if confirm == 'y':
                    df.at[idx, 'label'] = ai_label
                    df.at[idx, 'notes'] = f"AI-labeled as {ai_label}"
                    labeled_count += 1
                    print(f"✅ Accepted: {ai_label}")
                elif confirm == 's':
                    print("⏭️ Skipped")
                else:
                    print("❌ Rejected")
            else:
                print(f"⚠️ AI labeling failed: {ai_label}")
                
    except KeyboardInterrupt:
        print("\n🛑 Cancelled by user")
    
    # Save results
    if labeled_count > 0:
        df.to_csv(data_file, index=False)
        print(f"\n🎉 Successfully labeled {labeled_count} statements!")
    else:
        print("\n📝 No new labels added")

if __name__ == "__main__":
    main()
