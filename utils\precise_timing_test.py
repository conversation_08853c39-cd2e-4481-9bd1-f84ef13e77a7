#!/usr/bin/env python3
"""
Precise timing test to measure exactly how long AI responses take.
Creates a log of what's happening with Ollama.
"""
import requests
import json
import time
import datetime
import os

def log_message(message, log_file="ollama_timing.log"):
    """Log a message with timestamp."""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    log_entry = f"[{timestamp}] {message}\n"
    
    print(message)  # Also print to console
    
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(log_entry)

def test_simple_response():
    """Test a very simple response and measure timing precisely."""
    log_file = "ollama_timing.log"
    
    # Clear previous log
    if os.path.exists(log_file):
        os.remove(log_file)
    
    log_message("=" * 60)
    log_message("PRECISE OLLAMA TIMING TEST")
    log_message("=" * 60)
    
    # Test 1: Minimal request
    log_message("TEST 1: Minimal request - just say 'OK'")
    
    url = 'http://localhost:11434/api/generate'
    data = {
        'model': 'truth-evaluator',
        'prompt': 'Say only "OK" and nothing else.',
        'stream': False,
        'options': {
            'num_predict': 5,  # Very short response
            'temperature': 0
        }
    }
    
    log_message(f"Sending request to: {url}")
    log_message(f"Request data: {json.dumps(data, indent=2)}")
    
    start_time = time.time()
    log_message(f"Request started at: {datetime.datetime.now()}")
    
    try:
        response = requests.post(url, json=data, timeout=900)  # 15 minutes max
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        log_message(f"Request completed at: {datetime.datetime.now()}")
        log_message(f"Total elapsed time: {elapsed:.2f} seconds ({elapsed/60:.2f} minutes)")
        log_message(f"HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', 'No response field')
            log_message(f"AI Response: '{response_text}'")
            log_message("✅ SUCCESS: AI responded successfully")
            
            return elapsed
        else:
            log_message(f"❌ HTTP Error: {response.status_code}")
            log_message(f"Response body: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        elapsed = end_time - start_time
        log_message(f"⏰ TIMEOUT after {elapsed:.2f} seconds ({elapsed/60:.2f} minutes)")
        return None
        
    except Exception as e:
        end_time = time.time()
        elapsed = end_time - start_time
        log_message(f"❌ ERROR after {elapsed:.2f} seconds: {e}")
        return None

def test_typical_request(baseline_time):
    """Test a typical labeling request."""
    log_message("\n" + "=" * 60)
    log_message("TEST 2: Typical labeling request")
    log_message("=" * 60)
    
    if baseline_time:
        estimated_time = baseline_time * 3  # Estimate 3x longer for complex request
        log_message(f"Based on simple test ({baseline_time:.1f}s), estimating {estimated_time:.1f}s for this test")
    
    url = 'http://localhost:11434/api/generate'
    data = {
        'model': 'truth-evaluator',
        'prompt': '''Analyze this statement for logical outpoints:
"The factory reported record profits in Q1."

Is this statement missing crucial data (OMITTED_DATA_OUT)? 
Respond with only: YES or NO''',
        'stream': False,
        'options': {
            'num_predict': 10,
            'temperature': 0.1
        }
    }
    
    log_message("Testing typical outpoint evaluation request...")
    
    start_time = time.time()
    log_message(f"Request started at: {datetime.datetime.now()}")
    
    try:
        response = requests.post(url, json=data, timeout=1800)  # 30 minutes max
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        log_message(f"Request completed at: {datetime.datetime.now()}")
        log_message(f"Total elapsed time: {elapsed:.2f} seconds ({elapsed/60:.2f} minutes)")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', 'No response field')
            log_message(f"AI Response: '{response_text}'")
            log_message("✅ SUCCESS: Typical request completed")
            
            return elapsed
        else:
            log_message(f"❌ HTTP Error: {response.status_code}")
            return None
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        elapsed = end_time - start_time
        log_message(f"⏰ TIMEOUT after {elapsed:.2f} seconds ({elapsed/60:.2f} minutes)")
        return None
        
    except Exception as e:
        end_time = time.time()
        elapsed = end_time - start_time
        log_message(f"❌ ERROR after {elapsed:.2f} seconds: {e}")
        return None

def main():
    """Run the complete timing test."""
    print("🕐 Starting precise timing test...")
    print("📝 Creating detailed log: ollama_timing.log")
    print("⚠️  This may take 10-30 minutes depending on your computer speed")
    print("💡 You can press Ctrl+C to cancel")
    print()
    
    # Test 1: Simple request
    simple_time = test_simple_response()
    
    if simple_time:
        log_message(f"\n📊 SIMPLE REQUEST BASELINE: {simple_time:.2f} seconds")
        
        # Test 2: Typical request
        typical_time = test_typical_request(simple_time)
        
        if typical_time:
            log_message(f"\n📊 TYPICAL REQUEST TIME: {typical_time:.2f} seconds")
            log_message(f"📊 RATIO: Typical request is {typical_time/simple_time:.1f}x slower than simple")
            
            # Recommendations
            log_message("\n" + "=" * 60)
            log_message("RECOMMENDATIONS")
            log_message("=" * 60)
            
            if typical_time < 60:
                log_message("✅ Your computer can handle AI labeling reasonably well")
                log_message("💡 Recommended timeout: 120 seconds")
            elif typical_time < 300:
                log_message("⚠️ Your computer is slow but AI labeling is feasible")
                log_message("💡 Recommended timeout: 600 seconds (10 minutes)")
            else:
                log_message("🐌 Your computer is very slow for AI labeling")
                log_message("💡 Recommended timeout: 1800 seconds (30 minutes)")
                log_message("💡 Consider using manual labeling tools instead")
        else:
            log_message("❌ Typical request failed - AI labeling may not be practical")
    else:
        log_message("❌ Simple request failed - check Ollama setup")
    
    log_message("\n📝 Complete log saved to: ollama_timing.log")
    log_message("💡 You can delete this log file when done")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("\n🛑 Test cancelled by user")
        print("\n📝 Partial log saved to: ollama_timing.log")
