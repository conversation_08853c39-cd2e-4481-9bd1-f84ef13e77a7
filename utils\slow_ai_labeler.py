#!/usr/bin/env python3
"""
Slow-computer-friendly AI labeling tool.
Optimized for systems where LLM responses take a long time.
"""
import pandas as pd
import requests
import json
import time
import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# All 28 categories
ALL_CATEGORIES = [
    # Outpoints (14)
    "OMITTED_DATA_OUT", "ALTERED_SEQUENCE_OUT", "DROPPED_TIME_OUT",
    "WRONG_SOURCE_OUT", "CONTRARY_FACTS_OUT", "ADDED_INAPPLICABLES_OUT",
    "ASSUMED_SIMILARITIES_OUT", "ALTERED_IMPORTANCE_OUT", "FALSEDATA_OUT",
    "INCOMPLETE_DATA_OUT", "ASSUMED_IDENTITIES_OUT", "EVALUATION_OUT",
    "OUTPOINT_OUT", "PRIOR_CONFUSION_OUT",
    # Pluspoints (14)
    "DATA_PROVEN_FACTUAL_PLUS", "CORRECT_SEQUENCE_PLUS", "TIME_NOTED_PLUS",
    "CORRECT_SOURCE_PLUS", "ADEQUATE_DATA_PLUS", "APPLICABLE_DATA_PLUS",
    "CORRECT_RELATIVE_IMPORTANCE_PLUS", "COMPLETE_DATA_PLUS", "LOGICAL_PLUS",
    "CORRECT_ESTIMATION_PLUS", "CORRECT_IDENTITY_PLUS", "KNOWN_DATUM_PLUS",
    "PLUSPOINT_PLUS", "CORRECT_WHY_PLUS"
]


def test_ollama_connection():
    """Test if Ollama is responding with very long timeout."""
    print("Testing Ollama connection...")
    print("⏳ This may take several minutes on slow computers...")
    try:
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': 'truth-evaluator',
                'prompt': 'Hi',
                'stream': False,
                'options': {'num_predict': 5}
            },
            timeout=900  # 15 minutes based on timing test results
        )
        return response.status_code == 200
    except requests.exceptions.Timeout:
        print("⏰ Connection test timed out after 10 minutes")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False


def label_single_statement(statement, timeout=900):
    """Label a single statement with extended timeout."""
    print(f"\n📝 Analyzing: {statement[:100]}...")

    prompt = f"""
Analyze this statement for logical outpoints and pluspoints according to L. Ron Hubbard's Investigations methodology:

Statement: "{statement}"

Choose the SINGLE most appropriate category from these options:

OUTPOINTS (logical errors):
- OMITTED_DATA_OUT: Missing crucial information
- WRONG_SOURCE_OUT: Unreliable or inappropriate source
- CONTRARY_FACTS_OUT: Contradicts known facts
- ALTERED_IMPORTANCE_OUT: Wrong emphasis or priority
- FALSEDATA_OUT: Contains false information
- INCOMPLETE_DATA_OUT: Partial or insufficient data

PLUSPOINTS (logical strengths):
- DATA_PROVEN_FACTUAL_PLUS: Verifiable factual information
- CORRECT_SOURCE_PLUS: Reliable, appropriate source
- ADEQUATE_DATA_PLUS: Sufficient information provided
- TIME_NOTED_PLUS: Clear time references
- COMPLETE_DATA_PLUS: Comprehensive information
- LOGICAL_PLUS: Logical reasoning

NEUTRAL: Neither clearly positive nor negative

Respond with ONLY the category name (e.g., "OMITTED_DATA_OUT" or "NEUTRAL").
"""

    try:
        print("⏳ Sending to LLM (this will take about 5 minutes based on timing test)...")
        start_time = time.time()

        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': 'truth-evaluator',
                'prompt': prompt,
                'stream': False,
                'options': {
                    'temperature': 0.1,
                    'num_predict': 50
                }
            },
            timeout=timeout
        )

        elapsed = time.time() - start_time
        print(f"⏱️ Response received in {elapsed:.1f} seconds")

        if response.status_code == 200:
            result = response.json()
            ai_label = result.get('response', '').strip().upper()

            # Clean up the response to extract just the category
            for category in ALL_CATEGORIES + ['NEUTRAL']:
                if category in ai_label:
                    print(f"✅ AI suggests: {category}")
                    return category

            print(f"⚠️ Unclear response: {ai_label}")
            return "UNCLEAR_AI_RESPONSE"
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return "AI_ERROR"

    except requests.exceptions.Timeout:
        print("⏰ Request timed out after 15 minutes (model very slow)")
        return "TIMEOUT"
    except Exception as e:
        print(f"❌ Error: {e}")
        return "ERROR"


def interactive_labeling():
    """Interactive labeling with AI assistance."""
    print("🤖 Slow-Computer AI Labeling Tool")
    print("=" * 50)

    # Test connection first
    if not test_ollama_connection():
        print("❌ Cannot connect to Ollama. Make sure it's running.")
        return

    print("✅ Ollama connection OK")

    # Load data
    data_file = "data/statements_to_label.csv"
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return

    df = pd.read_csv(data_file)
    print(f"📊 Loaded {len(df)} statements")

    # Find unlabeled statements
    unlabeled = df[df['label'].isna() | (df['label'] == '')]
    print(f"🎯 Found {len(unlabeled)} unlabeled statements")

    if len(unlabeled) == 0:
        print("🎉 All statements are already labeled!")
        return

    print("\n🚀 Starting AI-assisted labeling...")
    print("Note: Each statement takes about 5 minutes based on timing test")
    print("💡 Timing test confirmed your system works - just be patient!")

    labeled_count = 0
    # Start with just 5 statements
    for idx, row in unlabeled.head(5).iterrows():
        statement = row['text']

        print(f"\n--- Statement {idx + 1} ---")
        ai_label = label_single_statement(statement)

        if ai_label in ALL_CATEGORIES + ['NEUTRAL']:
            # Ask user to confirm or modify
            print(f"\nStatement: {statement}")
            print(f"AI suggests: {ai_label}")

            user_input = input(
                "\nAccept (y), different label (type it), or skip (s)? ").strip()

            if user_input.lower() == 'y':
                df.at[idx, 'label'] = ai_label
                df.at[idx, 'notes'] = f"AI-labeled as {ai_label}"
                labeled_count += 1
                print(f"✅ Accepted: {ai_label}")
            elif user_input.lower() == 's':
                print("⏭️ Skipped")
                continue
            elif user_input.upper() in ALL_CATEGORIES + ['NEUTRAL']:
                df.at[idx, 'label'] = user_input.upper()
                df.at[idx,
                      'notes'] = f"User corrected from AI suggestion {ai_label}"
                labeled_count += 1
                print(f"✅ Corrected to: {user_input.upper()}")
            else:
                print("❌ Invalid label, skipping")
        else:
            print(f"⚠️ AI failed to label: {ai_label}")
            manual_label = input(
                "Enter manual label (or 's' to skip): ").strip()
            if manual_label.upper() in ALL_CATEGORIES + ['NEUTRAL']:
                df.at[idx, 'label'] = manual_label.upper()
                df.at[idx, 'notes'] = f"Manual label (AI failed)"
                labeled_count += 1
                print(f"✅ Manual label: {manual_label.upper()}")

    # Save results
    if labeled_count > 0:
        df.to_csv(data_file, index=False)
        print(
            f"\n🎉 Labeled {labeled_count} statements and saved to {data_file}")
    else:
        print("\n📝 No new labels added")


if __name__ == "__main__":
    interactive_labeling()
