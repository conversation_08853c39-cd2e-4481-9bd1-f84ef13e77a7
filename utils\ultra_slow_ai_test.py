#!/usr/bin/env python3
"""
Ultra-slow computer AI test with very long timeouts.
"""
import requests
import json
import time

def test_with_very_long_timeout():
    """Test with 10-minute timeout for very slow computers."""
    print("🐌 Ultra-Slow Computer AI Test")
    print("=" * 50)
    print("⚠️  This test uses a 10-minute timeout!")
    print("⚠️  Your computer may be very slow, so we'll wait longer.")
    print()
    
    url = 'http://localhost:11434/api/generate'
    data = {
        'model': 'truth-evaluator',
        'prompt': 'Say "WORKING" if you can respond.',
        'stream': False,
        'options': {
            'num_predict': 10,
            'temperature': 0
        }
    }
    
    print("🚀 Sending request to Ollama...")
    print("⏳ This may take up to 10 minutes on very slow computers...")
    print("💡 You can press Ctrl+C to cancel if needed")
    
    start_time = time.time()
    
    try:
        response = requests.post(url, json=data, timeout=600)  # 10 minutes!
        
        elapsed = time.time() - start_time
        print(f"\n⏱️  Response received after {elapsed:.1f} seconds ({elapsed/60:.1f} minutes)")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', 'No response')
            print(f"✅ SUCCESS: {response_text}")
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"\n⏰ Still timed out after {elapsed/60:.1f} minutes")
        print("💡 Your computer might be too slow for AI-assisted labeling")
        return False
    except KeyboardInterrupt:
        elapsed = time.time() - start_time
        print(f"\n🛑 Cancelled by user after {elapsed:.1f} seconds")
        return False
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_with_very_long_timeout()
    
    if success:
        print("\n🎉 AI labeling should work on your computer!")
        print("💡 Use the slow AI labeler with confidence.")
    else:
        print("\n📝 Recommendation: Use manual labeling tools instead")
        print("   - GUI tool: python utils/statement_categorizer.py docs/Statementstoclass.txt data/gui_labeled.csv")
        print("   - Manual helper: python utils/manual_labeling_helper.py")
