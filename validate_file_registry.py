#!/usr/bin/env python3
"""
Validate FILE_REGISTRY.md against actual directory structure
"""
import os
import re
from pathlib import Path

def get_actual_files():
    """Get actual files in the project directory"""
    actual_files = set()
    
    for root, dirs, files in os.walk('.'):
        # Skip __pycache__ and .git directories
        dirs[:] = [d for d in dirs if not d.startswith('__pycache__') and d != '.git']
        
        for file in files:
            # Skip .pyc files and other temp files
            if not file.endswith('.pyc') and not file.startswith('.'):
                rel_path = os.path.relpath(os.path.join(root, file), '.')
                actual_files.add(rel_path.replace('\\', '/'))
    
    return actual_files

def parse_registry_files():
    """Parse files mentioned in FILE_REGISTRY.md"""
    registry_files = set()
    
    try:
        with open('docs/FILE_REGISTRY.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract file paths from registry
        # Look for patterns like: **File**: `filename.py`
        file_patterns = [
            r'\*\*File\*\*:\s*`([^`]+)`',
            r'\*\*Directory\*\*:\s*`([^`]+)`',
            r'`([^`]+\.py)`',
            r'`([^`]+\.csv)`',
            r'`([^`]+\.json)`',
            r'`([^`]+\.md)`',
            r'`([^`]+\.txt)`',
        ]
        
        for pattern in file_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                # Clean up the path
                clean_path = match.strip()
                if clean_path and not clean_path.startswith('python '):
                    registry_files.add(clean_path)
    
    except FileNotFoundError:
        print("❌ FILE_REGISTRY.md not found!")
        return set()
    
    return registry_files

def validate_registry():
    """Validate registry against actual files"""
    print("🔍 VALIDATING FILE_REGISTRY.md AGAINST ACTUAL DIRECTORY")
    print("="*60)
    
    actual_files = get_actual_files()
    registry_files = parse_registry_files()
    
    print(f"📊 SUMMARY:")
    print(f"   Actual files found: {len(actual_files)}")
    print(f"   Registry files listed: {len(registry_files)}")
    
    # Key files that should be documented
    key_files = {
        'truth_algorithm.py',
        'base_knowledge_db.py',
        'nuanced_evaluation_system.py',
        'analyze_data_quality.py',
        'manual_data_review.py',
        'data/complete_28_category_dataset.csv',
        'official_28_category_mapping.json',
        'models/round2-simple',
        'docs/MODEL_DOCUMENTATION.md',
        'docs/FILE_REGISTRY.md',
        'SESSION_STATUS.md',
        'README.md'
    }
    
    print(f"\n✅ KEY FILES VALIDATION:")
    missing_key_files = []
    for key_file in key_files:
        if any(key_file in af for af in actual_files):
            print(f"   ✅ {key_file}")
        else:
            missing_key_files.append(key_file)
            print(f"   ❌ {key_file} - NOT FOUND")
    
    # Check for undocumented important files
    print(f"\n📋 IMPORTANT FILES NOT IN REGISTRY:")
    important_patterns = [
        r'.*\.py$',
        r'.*\.csv$', 
        r'.*\.json$',
        r'.*\.md$'
    ]
    
    undocumented_important = []
    for actual_file in actual_files:
        if any(re.match(pattern, actual_file) for pattern in important_patterns):
            # Check if this file is mentioned in registry
            file_name = os.path.basename(actual_file)
            if not any(file_name in rf or actual_file in rf for rf in registry_files):
                # Skip some obvious temp/cache files
                if not any(skip in actual_file for skip in ['checkpoint-', '__pycache__', '.pyc']):
                    undocumented_important.append(actual_file)
    
    if undocumented_important:
        print(f"   Found {len(undocumented_important)} undocumented files:")
        for file in sorted(undocumented_important)[:20]:  # Show first 20
            print(f"   📄 {file}")
        if len(undocumented_important) > 20:
            print(f"   ... and {len(undocumented_important) - 20} more")
    else:
        print(f"   ✅ All important files appear to be documented")
    
    # Check model directories specifically
    print(f"\n🤖 MODEL DIRECTORIES:")
    model_dirs = ['models/round2-simple', 'models/bert-test', 'models/round2-model']
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            files_in_model = len([f for f in actual_files if f.startswith(model_dir.replace('\\', '/'))])
            print(f"   ✅ {model_dir}: {files_in_model} files")
        else:
            print(f"   ❌ {model_dir}: NOT FOUND")
    
    # Check data directory
    print(f"\n📊 DATA DIRECTORY:")
    data_files = [f for f in actual_files if f.startswith('data/')]
    print(f"   Found {len(data_files)} data files:")
    for data_file in sorted(data_files):
        print(f"   📄 {data_file}")
    
    # Check docs directory
    print(f"\n📚 DOCS DIRECTORY:")
    doc_files = [f for f in actual_files if f.startswith('docs/')]
    print(f"   Found {len(doc_files)} documentation files:")
    for doc_file in sorted(doc_files)[:10]:  # Show first 10
        print(f"   📄 {doc_file}")
    if len(doc_files) > 10:
        print(f"   ... and {len(doc_files) - 10} more")
    
    # Overall assessment
    print(f"\n🎯 REGISTRY VALIDATION RESULT:")
    if missing_key_files:
        print(f"   ⚠️ NEEDS UPDATE: {len(missing_key_files)} key files missing from registry")
        for missing in missing_key_files:
            print(f"      - {missing}")
    else:
        print(f"   ✅ GOOD: All key files are documented")
    
    if len(undocumented_important) > 10:
        print(f"   ⚠️ CONSIDER: {len(undocumented_important)} files could be added to registry")
    else:
        print(f"   ✅ GOOD: Registry coverage appears comprehensive")
    
    return {
        'actual_files': actual_files,
        'registry_files': registry_files,
        'missing_key_files': missing_key_files,
        'undocumented_important': undocumented_important
    }

def main():
    print("🔍 FILE REGISTRY VALIDATION")
    print("="*40)
    
    validation_result = validate_registry()
    
    print(f"\n💾 Validation complete!")
    
    if not validation_result['missing_key_files'] and len(validation_result['undocumented_important']) <= 10:
        print(f"✅ FILE_REGISTRY.md is accurate and comprehensive!")
        print(f"🎯 Ready to proceed with data validation.")
    else:
        print(f"⚠️ FILE_REGISTRY.md needs minor updates.")
        print(f"🔧 Consider adding missing files to registry.")

if __name__ == "__main__":
    main()
