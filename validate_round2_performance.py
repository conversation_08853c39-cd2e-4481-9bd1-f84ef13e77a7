#!/usr/bin/env python3
"""
Comprehensive Round 2 Performance Validation
Compare against baseline and analyze results
"""
import pandas as pd
import json
import numpy as np
from pathlib import Path
from datetime import datetime
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

def load_model_and_data():
    """Load Round 2 model and test data"""
    print("📊 LOADING MODEL AND DATA")
    print("="*40)
    
    # Load model
    model_dir = "models/round2-simple"
    print(f"Loading model from: {model_dir}")
    
    tokenizer = AutoTokenizer.from_pretrained(model_dir)
    model = AutoModelForSequenceClassification.from_pretrained(model_dir)
    
    # Load label mapping
    with open(f"{model_dir}/label_map.json", "r") as f:
        label_mapping = json.load(f)
    
    label_to_id = label_mapping['label_to_id']
    id_to_label = label_mapping['id_to_label']
    
    print(f"✅ Model loaded with {len(label_to_id)} categories")
    
    # Load test data
    df = pd.read_csv("data/complete_28_category_dataset.csv")
    labeled_df = df[df['label'].notna() & (df['label'] != '')]
    
    print(f"✅ Loaded {len(labeled_df)} test examples")
    
    return model, tokenizer, label_to_id, id_to_label, labeled_df

def evaluate_model(model, tokenizer, label_to_id, id_to_label, test_df):
    """Evaluate model performance"""
    print(f"\n🎯 EVALUATING MODEL PERFORMANCE")
    print("="*40)
    
    # Prepare test data
    texts = []
    true_labels = []
    
    for _, row in test_df.iterrows():
        text = row['text']
        label_str = row['label']
        
        # Take first label if multiple
        if ',' in label_str:
            primary_label = label_str.split(',')[0].strip()
        else:
            primary_label = label_str.strip()
        
        if primary_label in label_to_id:
            texts.append(text)
            true_labels.append(label_to_id[primary_label])
    
    print(f"📊 Evaluating on {len(texts)} examples")
    
    # Make predictions
    model.eval()
    predicted_labels = []
    confidences = []
    
    with torch.no_grad():
        for text in texts:
            # Tokenize
            inputs = tokenizer(
                text,
                truncation=True,
                padding='max_length',
                max_length=256,
                return_tensors='pt'
            )
            
            # Predict
            outputs = model(**inputs)
            logits = outputs.logits
            
            # Get prediction and confidence
            probs = torch.softmax(logits, dim=-1)
            predicted_id = torch.argmax(probs, dim=-1).item()
            confidence = torch.max(probs).item()
            
            predicted_labels.append(predicted_id)
            confidences.append(confidence)
    
    # Calculate metrics
    accuracy = np.mean(np.array(predicted_labels) == np.array(true_labels))
    avg_confidence = np.mean(confidences)
    
    print(f"🎯 RESULTS:")
    print(f"   Accuracy: {accuracy:.3f} ({accuracy*100:.1f}%)")
    print(f"   Average Confidence: {avg_confidence:.3f}")
    print(f"   Total Predictions: {len(predicted_labels)}")
    
    # Category-wise analysis
    print(f"\n📋 CATEGORY-WISE PERFORMANCE:")
    
    # Convert predictions to category names
    true_categories = [id_to_label[str(label)] for label in true_labels]
    pred_categories = [id_to_label[str(label)] for label in predicted_labels]
    
    # Classification report
    try:
        report = classification_report(
            true_categories, 
            pred_categories, 
            output_dict=True,
            zero_division=0
        )
        
        # Show top performing categories
        category_scores = []
        for category, metrics in report.items():
            if isinstance(metrics, dict) and 'f1-score' in metrics:
                category_scores.append((category, metrics['f1-score'], metrics['support']))
        
        # Sort by F1 score
        category_scores.sort(key=lambda x: x[1], reverse=True)
        
        print(f"   Top 5 performing categories:")
        for i, (category, f1, support) in enumerate(category_scores[:5]):
            print(f"   {i+1}. {category}: F1={f1:.3f} (n={support})")
        
        print(f"   Bottom 5 performing categories:")
        for i, (category, f1, support) in enumerate(category_scores[-5:]):
            print(f"   {len(category_scores)-4+i}. {category}: F1={f1:.3f} (n={support})")
            
    except Exception as e:
        print(f"   ⚠️ Could not generate detailed report: {e}")
    
    return {
        'accuracy': accuracy,
        'avg_confidence': avg_confidence,
        'total_predictions': len(predicted_labels),
        'true_labels': true_labels,
        'predicted_labels': predicted_labels,
        'confidences': confidences
    }

def compare_with_baseline():
    """Compare with baseline performance"""
    print(f"\n📊 BASELINE COMPARISON")
    print("="*40)
    
    # Baseline metrics (from previous analysis)
    baseline = {
        'accuracy': 0.517,  # 51.7%
        'avg_confidence': 0.172,
        'coverage': 1.0,  # 100% after threshold optimization
        'categories': 17,  # Incomplete
        'model': 'models/bert-test'
    }
    
    # Round 2 metrics
    round2 = {
        'accuracy': 0.034,  # From training results
        'avg_confidence': 0.0,  # To be measured
        'coverage': 1.0,  # Assumed
        'categories': 29,  # Complete (28 + NEUTRAL)
        'model': 'models/round2-simple'
    }
    
    print(f"📋 COMPARISON:")
    print(f"                    Baseline    Round 2     Change")
    print(f"   Accuracy:        {baseline['accuracy']:.3f}      {round2['accuracy']:.3f}     {round2['accuracy']-baseline['accuracy']:+.3f}")
    print(f"   Avg Confidence:  {baseline['avg_confidence']:.3f}      {round2['avg_confidence']:.3f}     {round2['avg_confidence']-baseline['avg_confidence']:+.3f}")
    print(f"   Categories:      {baseline['categories']:2d}         {round2['categories']:2d}        +{round2['categories']-baseline['categories']:2d}")
    print(f"   Coverage:        {baseline['coverage']:.3f}      {round2['coverage']:.3f}     {round2['coverage']-baseline['coverage']:+.3f}")
    
    # Analysis
    print(f"\n🔍 ANALYSIS:")
    if round2['accuracy'] < baseline['accuracy']:
        print(f"   ⚠️ Accuracy DECREASED by {(baseline['accuracy']-round2['accuracy'])*100:.1f}%")
        print(f"   🔍 Possible causes:")
        print(f"      - Increased complexity (17→29 categories)")
        print(f"      - Insufficient training epochs")
        print(f"      - Data quality issues")
        print(f"      - Model architecture mismatch")
    
    if round2['categories'] > baseline['categories']:
        print(f"   ✅ Category coverage IMPROVED (+{round2['categories']-baseline['categories']} categories)")
        print(f"   ✅ Now includes all 28 official L. Ron Hubbard categories")
    
    return baseline, round2

def main():
    try:
        print("🚀 ROUND 2 PERFORMANCE VALIDATION")
        print("="*50)
        
        # Load model and data
        model, tokenizer, label_to_id, id_to_label, test_df = load_model_and_data()
        
        # Evaluate performance
        results = evaluate_model(model, tokenizer, label_to_id, id_to_label, test_df)
        
        # Compare with baseline
        baseline, round2 = compare_with_baseline()
        
        # Update round2 with actual results
        round2['avg_confidence'] = results['avg_confidence']
        
        # Save detailed results
        detailed_results = {
            'round2_results': results,
            'baseline_comparison': {
                'baseline': baseline,
                'round2': round2
            },
            'timestamp': datetime.now().isoformat(),
            'model_path': 'models/round2-simple',
            'test_examples': len(test_df)
        }
        
        with open('round2_validation_results.json', 'w') as f:
            json.dump(detailed_results, f, indent=2, default=str)
        
        print(f"\n💾 RESULTS SAVED:")
        print(f"   📊 Detailed results: round2_validation_results.json")
        print(f"   📁 Model location: models/round2-simple/")
        
        # Recommendations
        print(f"\n🎯 RECOMMENDATIONS:")
        if results['accuracy'] < 0.1:
            print(f"   🔧 URGENT: Very low accuracy - investigate training issues")
            print(f"   📋 Next steps:")
            print(f"      1. Check data quality and labeling")
            print(f"      2. Increase training epochs")
            print(f"      3. Adjust learning rate")
            print(f"      4. Verify category mapping")
        else:
            print(f"   ✅ Model trained successfully")
            print(f"   📈 Consider further optimization for better performance")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
