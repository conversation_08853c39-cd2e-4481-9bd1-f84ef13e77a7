#!/usr/bin/env python3
"""
Validate Training Data with Nuanced L<PERSON> Methodology
Manually review and correct AI-labeled data using proper investigation techniques
"""
import pandas as pd
import json
from nuanced_evaluation_system import BaseKnowledgeDatabase, ContextualEvaluator

def load_lrh_definitions():
    """Load official <PERSON><PERSON> definitions from source documents"""
    definitions = {
        "outpoints": {
            "OMITTED_DATA_OUT": {
                "definition": "An omitted anything is an outpoint. This can be an omitted person, terminal, object, energy, space, time, form, sequence or even an omitted scene.",
                "source": "Investigations.txt",
                "examples": ["Missing crucial information", "Incomplete reports", "Vague statements without specifics"],
                "key_indicators": ["sources say", "reportedly", "some people", "it is said", "missing context"]
            },
            "ALTERED_SEQUENCE_OUT": {
                "definition": "Any things, events, objects, sizes, in a wrong sequence is an outpoint. A sequence that should be one and isn't is an outpoint.",
                "source": "Investigations.txt", 
                "examples": ["Events out of logical order", "Cart before the horse", "Effect before cause"],
                "key_indicators": ["before", "after", "then", "first", "timeline errors"]
            },
            "DROPPED_TIME_OUT": {
                "definition": "Time that should be noted and isn't would be an outpoint of 'dropped time.' It is a special case of an omitted datum.",
                "source": "Investigations.txt",
                "examples": ["Undated events", "Missing timestamps", "Vague time references"],
                "key_indicators": ["sometime", "recently", "a while ago", "missing dates"]
            },
            "FALSEHOOD_OUT": {
                "definition": "When you hear two facts that are contrary, one is a falsehood or both are. Willful or unintentional, a falsehood is an outpoint.",
                "source": "Investigations.txt",
                "examples": ["Demonstrably false statements", "Contradicted claims", "Proven lies"],
                "key_indicators": ["contradicts known facts", "proven false", "impossible claims"]
            },
            "CONTRARY_FACTS_OUT": {
                "definition": "Two contradictory 'facts' qualifies as an outpoint. We don't know which is true but we do know they can't both be true.",
                "source": "Investigations.txt",
                "examples": ["Record profits but can't pay bills", "Contradictory statements from same source"],
                "key_indicators": ["contradictory", "conflicting", "opposite claims"]
            }
        },
        "pluspoints": {
            "DATA_PROVEN_FACTUAL_PLUS": {
                "definition": "Data that has been verified and proven to be factual and true.",
                "source": "Investigations.txt",
                "examples": ["Verified statistics", "Confirmed reports", "Documented facts"],
                "key_indicators": ["verified", "confirmed", "documented", "proven", "official data"]
            },
            "ADEQUATE_DATA_PLUS": {
                "definition": "Sufficient information is present to understand the situation completely.",
                "source": "Investigations.txt", 
                "examples": ["Complete reports", "Detailed information", "Sufficient context"],
                "key_indicators": ["detailed", "complete", "specific", "comprehensive"]
            },
            "CORRECT_SOURCE_PLUS": {
                "definition": "Information comes from a reliable, appropriate, and properly attributed source.",
                "source": "Investigations.txt",
                "examples": ["Official statements", "Credible sources", "Proper attribution"],
                "key_indicators": ["according to", "official", "reported by", "study by"]
            },
            "TIME_NOTED_PLUS": {
                "definition": "Proper timestamps and time references are provided when relevant.",
                "source": "Investigations.txt",
                "examples": ["Specific dates", "Clear timeframes", "Proper chronology"],
                "key_indicators": ["on [date]", "at [time]", "during", "specific timing"]
            }
        }
    }
    return definitions

def validate_single_statement(statement_data, definitions, knowledge_db, evaluator):
    """Manually validate a single statement's labeling"""
    text = statement_data['text']
    current_labels = statement_data.get('label', '').split(',') if statement_data.get('label') else []
    current_labels = [label.strip() for label in current_labels if label.strip()]
    
    print(f"\n" + "="*80)
    print(f"STATEMENT: {text}")
    print(f"CURRENT LABELS: {', '.join(current_labels)}")
    print("="*80)
    
    # Get nuanced evaluation
    evaluation = evaluator.evaluate_statement(text)
    
    print(f"\nAUTOMATIC EVALUATION:")
    print(f"Suggested Outpoints: {evaluation['outpoints']}")
    print(f"Suggested Pluspoints: {evaluation['pluspoints']}")
    
    if evaluation['contradictions']:
        print(f"Contradictions Found: {len(evaluation['contradictions'])}")
        for contradiction in evaluation['contradictions']:
            print(f"  - {contradiction['description']}")
    
    if evaluation['investigation_needed']:
        print(f"Investigation Needed:")
        for investigation in evaluation['investigation_needed']:
            print(f"  - {investigation['description']}")
    
    # Manual review process
    print(f"\nMANUAL REVIEW:")
    print(f"1. Check against L. Ron Hubbard definitions")
    print(f"2. Consider context and nuance")
    print(f"3. Look for missed outpoints/pluspoints")
    print(f"4. Verify no false positives")
    
    # Show relevant definitions
    all_suggested = evaluation['outpoints'] + evaluation['pluspoints']
    if all_suggested:
        print(f"\nRELEVANT DEFINITIONS:")
        for category in all_suggested:
            if category in definitions['outpoints']:
                defn = definitions['outpoints'][category]
                print(f"\n{category} (OUTPOINT):")
                print(f"  Definition: {defn['definition']}")
                print(f"  Key indicators: {', '.join(defn['key_indicators'])}")
            elif category in definitions['pluspoints']:
                defn = definitions['pluspoints'][category]
                print(f"\n{category} (PLUSPOINT):")
                print(f"  Definition: {defn['definition']}")
                print(f"  Key indicators: {', '.join(defn['key_indicators'])}")
    
    # Validation questions
    validation_result = {
        'original_text': text,
        'original_labels': current_labels,
        'suggested_labels': all_suggested,
        'validation_needed': True,
        'issues_found': [],
        'recommended_labels': []
    }
    
    # Check for common labeling errors
    issues = []
    
    # Check if current labels match LRH definitions
    for label in current_labels:
        if label not in definitions['outpoints'] and label not in definitions['pluspoints'] and label != 'NEUTRAL':
            issues.append(f"Unknown category: {label}")
    
    # Check for obvious mismatches
    if 'DATA_PROVEN_FACTUAL_PLUS' in current_labels:
        if any(indicator in text.lower() for indicator in ['sources say', 'reportedly', 'allegedly']):
            issues.append("DATA_PROVEN_FACTUAL_PLUS conflicts with vague attribution")
    
    if 'OMITTED_DATA_OUT' not in current_labels:
        if any(indicator in text.lower() for indicator in ['sources say', 'some people', 'it is said']):
            issues.append("Missing OMITTED_DATA_OUT for vague attribution")
    
    validation_result['issues_found'] = issues
    
    return validation_result

def batch_validate_training_data():
    """Validate all training data systematically"""
    print("🔍 VALIDATING TRAINING DATA WITH L. RON HUBBARD METHODOLOGY")
    print("="*70)
    
    # Load data and systems
    df = pd.read_csv("data/complete_28_category_dataset.csv")
    labeled_df = df[df['label'].notna() & (df['label'] != '')]
    
    definitions = load_lrh_definitions()
    knowledge_db = BaseKnowledgeDatabase()
    evaluator = ContextualEvaluator(knowledge_db)
    
    # Load statement set for cross-referencing
    statements = [{'text': row['text'], 'id': idx} for idx, row in labeled_df.iterrows()]
    evaluator.load_statement_set(statements)
    
    print(f"📊 Validating {len(labeled_df)} labeled statements")
    
    validation_results = []
    high_priority_issues = []
    
    for idx, row in labeled_df.iterrows():
        validation = validate_single_statement(row, definitions, knowledge_db, evaluator)
        validation_results.append(validation)
        
        if validation['issues_found']:
            high_priority_issues.append({
                'index': idx,
                'text': row['text'][:100] + '...',
                'issues': validation['issues_found']
            })
        
        # Show progress
        if (idx + 1) % 10 == 0:
            print(f"\n📈 Progress: {idx + 1}/{len(labeled_df)} statements validated")
    
    # Summary report
    print(f"\n" + "="*70)
    print(f"VALIDATION SUMMARY")
    print(f"="*70)
    
    total_issues = sum(len(v['issues_found']) for v in validation_results)
    statements_with_issues = len([v for v in validation_results if v['issues_found']])
    
    print(f"Total statements validated: {len(validation_results)}")
    print(f"Statements with issues: {statements_with_issues}")
    print(f"Total issues found: {total_issues}")
    print(f"Issue rate: {statements_with_issues/len(validation_results)*100:.1f}%")
    
    if high_priority_issues:
        print(f"\n🚨 HIGH PRIORITY ISSUES:")
        for issue in high_priority_issues[:10]:  # Show top 10
            print(f"\nStatement: {issue['text']}")
            for problem in issue['issues']:
                print(f"  - {problem}")
    
    # Save validation results
    with open('training_data_validation.json', 'w') as f:
        json.dump({
            'validation_results': validation_results,
            'summary': {
                'total_statements': len(validation_results),
                'statements_with_issues': statements_with_issues,
                'total_issues': total_issues,
                'issue_rate': statements_with_issues/len(validation_results)
            },
            'high_priority_issues': high_priority_issues,
            'timestamp': pd.Timestamp.now().isoformat()
        }, f, indent=2)
    
    print(f"\n💾 Validation results saved to: training_data_validation.json")
    
    return validation_results

def create_corrected_dataset(validation_results):
    """Create a corrected dataset based on validation results"""
    print(f"\n🔧 CREATING CORRECTED DATASET")
    print("="*40)
    
    # This would involve manual correction based on validation results
    # For now, we'll create a framework for the correction process
    
    corrections_needed = [v for v in validation_results if v['issues_found']]
    
    print(f"📋 Statements needing correction: {len(corrections_needed)}")
    print(f"📝 Manual review process required for:")
    
    correction_categories = {
        'unknown_categories': [],
        'definition_mismatches': [],
        'missing_outpoints': [],
        'false_positives': []
    }
    
    for correction in corrections_needed:
        for issue in correction['issues_found']:
            if 'Unknown category' in issue:
                correction_categories['unknown_categories'].append(correction)
            elif 'conflicts with' in issue:
                correction_categories['definition_mismatches'].append(correction)
            elif 'Missing' in issue:
                correction_categories['missing_outpoints'].append(correction)
    
    for category, items in correction_categories.items():
        if items:
            print(f"  - {category.replace('_', ' ').title()}: {len(items)} statements")
    
    print(f"\n🎯 RECOMMENDED CORRECTION PROCESS:")
    print(f"1. Review unknown categories and map to official LRH categories")
    print(f"2. Fix definition mismatches using LRH source material")
    print(f"3. Add missing outpoints based on nuanced evaluation")
    print(f"4. Remove false positive labels")
    print(f"5. Create corrected dataset for Round 3 training")

def main():
    print("🔍 TRAINING DATA VALIDATION SYSTEM")
    print("="*50)
    print("Validating against L. Ron Hubbard's official definitions")
    print("Using nuanced evaluation with base knowledge database")
    print()
    
    # Run validation
    validation_results = batch_validate_training_data()
    
    # Create correction plan
    create_corrected_dataset(validation_results)
    
    print(f"\n✅ VALIDATION COMPLETE!")
    print(f"🎯 Next steps:")
    print(f"1. Review validation results in training_data_validation.json")
    print(f"2. Manually correct high-priority issues")
    print(f"3. Create corrected dataset")
    print(f"4. Retrain Round 3 model with validated data")

if __name__ == "__main__":
    main()
